#####江北嘴投资集团建设银行收款配置
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('CCB_WX_CQJBC_1', 'MERCHANTID', '', '商户号', 'CCB_WX_CQJBC');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('CCB_WX_CQJBC_2', 'group_description', '江北嘴投资集团建设银行收款配置', '描述', 'CCB_WX_CQJBC');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('CCB_WX_CQJBC_3', 'enabled', 'true', '是否启用', 'CCB_WX_CQJBC');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('CCB_WX_CQJBC_4', 'notifyUrl', 'http://pay.yeyeku.com/collection-pay/callback/ALLINPAY_WX', '通知地址', 'CCB_WX_CQJBC');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('CCB_WX_CQJBC_5', 'POSID', '', '商 户 柜 台 代码', 'CCB_WX_CQJBC');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('CCB_WX_CQJBC_6', 'PUB', '', '公钥后 30 位,商户从建行商户服务平台下载，截取后 30 位。仅作为源串参加 MD5 摘要，不作为参数传递', 'CCB_WX_CQJBC');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('CCB_WX_CQJBC_7', 'apiurl', 'https://vsp.allinpay.com/apiweb/unitorder', '接口地址', 'CCB_WX_CQJBC');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('CCB_WX_CQJBC_8', 'BRANCHID', '', '分行代码', 'CCB_WX_CQJBC');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('CCB_WX_CQJBC_9', 'cashier_page', 'https://ibsbjstar.ccb.com.cn/CCBIS/ccbMain?', '下单页面', 'CCB_WX_CQJBC');