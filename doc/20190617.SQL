use gs_user_info ;
### 添加对麦卡游戏平台的支持
insert into gs_collection_dealer_account values('2',now(),0,0,0,0,0,1,null,'游戏平台',null,0);
alter  table gs_collection_dealer_trade modify  column open_id  varchar(512) DEFAULT NULL;
alter  table gs_collection_dealer_account_log modify  column open_id  varchar(512) DEFAULT NULL;

alter  table gs_collection_dealer_trade add  column out_trade_no  varchar(32) DEFAULT NULL comment '业务方订单号';
alter  table gs_collection_dealer_trade add  column  refund_time datetime DEFAULT NULL comment '退款时间';
alter  table gs_collection_dealer_trade add  column  refund_operator varchar(64) DEFAULT NULL comment '退款操作人';
alter  table gs_collection_dealer_trade add  column  service_fee int(11) DEFAULT NULL comment '第三方支付渠道收取的手续费(分)';
alter  table gs_collection_dealer_trade add  column  refunded_amount bigint(20) DEFAULT 0 comment '已退款金额(分)';



INSERT INTO gs_asset_deposit_channel_conf VALUES('WXPAY_MKAPP_06','defaultIp','************','终端IP','WXPAY_MKAPP');
INSERT INTO gs_asset_deposit_channel_conf VALUES('WXPAY_MKAPP_07','notifyUrl','https://collectionpay.goodsogood.com/collection-pay/callback/weixin','通知地址','WXPAY_MKAPP');
INSERT INTO gs_asset_deposit_channel_conf VALUES('WXPAY_MKAPP_08','tradeUrl','https://api.mch.weixin.qq.com/pay/unifiedorder','URL地址','WXPAY_MKAPP');
INSERT INTO gs_asset_deposit_channel_conf VALUES('WXPAY_MKAPP_09','pageUrl','https://collectionpay.goodsogood.com/collection-pay/callback/redirect?orderId=','跳转地址','WXPAY_MKAPP');


INSERT INTO gs_asset_deposit_channel_conf VALUES('ALIPAY_GAME_1','partner','2088621468123508','','ALIPAY_GAME');
INSERT INTO gs_asset_deposit_channel_conf VALUES('ALIPAY_GAME_2','privateKey','MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAIVNo82PdJnr6kWkNPyoa77lfemG+AewXfhnO4JpfT1qr62KKGe/0kA4yarmj7OT61gdo+7jikB180uWIaC0thd5z1rk11J6fbeiyWeS+lSbo+pS5qE4cdR5kwvJ1T1LA1xC/tqgSKbSEVu52cpYuaWR0SyoEVslAyFofLnakqGRAgMBAAECgYB+0Z9tt/X5XbzyZkdWu7BN260PV61uOzcbVLMD3fIdVPsvgYnxMkgHez85DMJX2PkESqakZesoak59caYqauH0UfWWXwjZNxPUmODveCv95CyUte853DszB2UsOgebN/AuygfKFdSNX51dvbqMZobu9Yb7XcIwv7llRfPMtabusQJBAL4dP3NNT8N6v2UAGj+EsBuE99BfPZ2wuY39xS/IOqlT8P53M5/9rLnwd7bJZRbiDN2oRaCysSLeH0vQK4d8rmUCQQCzgC2IGajHdKOqKfyguQyY4K1p0MqcqMKVQmli43L6yUEsRQ3JRE7eVamgGhsCcPicrq+jCx96pguFdwyK8829AkAqbxTOg58eHrPBs3goXVGBkvhNRjTWka+ZBd+Rfru5N8c+xRR8zX5UMlxGLEWoCeSdRQXljH0FL1cE8P4GS8oRAkA1jSgIUp9n4gRx39TenlaDxYP27bOEyVRD82w9dF35M9Gdw3w5b4eYDTZt7VvhfpKtK/B6U/cwQBlv1/SF//aBAkBwCRJkMN9234Wp6xe1y3zFDK6POTuFH8nWL7kvq9YQnEm5Xt0GvaCZA2lLkO9Oo3hIbx8VoQ+A3zEebCDUeaRd','私钥','ALIPAY_GAME');
INSERT INTO gs_asset_deposit_channel_conf VALUES('ALIPAY_GAME_3','publicKey','MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDDI6d306Q8fIfCOaTXyiUeJHkrIvYISRcc73s3vF1ZT7XN8RNPwJxo8pWaJMmvyTn9N4HQ632qJBVHf8sxHi/fEsraprwCtzvzQETrNRwVxLO5jVmRGi60j8Ue1efIlzPXV9je9mkjzOmdssymZkh2QhUrCmZYI/FCEa3/cNMW0QIDAQAB','公钥','ALIPAY_GAME');
INSERT INTO gs_asset_deposit_channel_conf VALUES('ALIPAY_GAME_4','notifyUrl','https://collectionpay.goodsogood.com/collection-pay/callback/alipay_common','回调地址','ALIPAY_GAME');
INSERT INTO gs_asset_deposit_channel_conf VALUES('ALIPAY_GAME_5','appId','2017051207219081','','ALIPAY_GAME'); 
INSERT INTO gs_asset_deposit_channel_conf VALUES('ALIPAY_GAME_6','enabled','true','是否启用','ALIPAY_GAME');
INSERT INTO gs_asset_deposit_channel_conf VALUES('ALIPAY_GAME_7','signType','RSA','签名类型','ALIPAY_GAME');
INSERT INTO gs_asset_deposit_channel_conf VALUES('ALIPAY_GAME_8','pageUrl','https://collectionpay.goodsogood.com/collection-pay/callback/redirect?orderId=','跳转地址','ALIPAY_GAME');
INSERT INTO gs_asset_deposit_channel_conf VALUES('ALIPAY_GAME_10','description','麦卡-游戏平台支付宝配置','说明','ALIPAY_GAME');
