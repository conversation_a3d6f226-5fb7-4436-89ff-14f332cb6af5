CREATE TABLE `gs_collection_dealer_account` (
  `dealer_id` int(20) NOT NULL AUTO_INCREMENT COMMENT '主键(渠道id)',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `balance` bigint(20) DEFAULT '0' COMMENT '当前余额 (单位：分)',
  `wxpay_sum` bigint(20) DEFAULT '0' COMMENT '微信充值总金额',
  `unionpay_sum` bigint(20) DEFAULT '0' COMMENT '银联充值总金额',
  `wxpay_num` bigint(20) DEFAULT '0' COMMENT '微信充值总笔数',
  `unionpay_num` bigint(20) DEFAULT '0' COMMENT '银联充值总笔数',
  `status` int(1) DEFAULT NULL COMMENT '1表示正常，2表示锁定  ',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `dealer_name` varchar(100) DEFAULT NULL COMMENT '渠道名称',
  `outer_id` varchar(64) DEFAULT NULL COMMENT '外部id',
  `version` bigint(20) DEFAULT '0',
  PRIMARY KEY (`dealer_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='代收经销商资金账户表';




CREATE TABLE `gs_collection_dealer_trade` (
  `trade_no` varchar(64) NOT NULL COMMENT '主键,系统单号',
  `dealer_id` int(20) NOT NULL COMMENT '(渠道id)',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `pay_time` datetime DEFAULT NULL COMMENT '付款时间',
  `amount` bigint(20) DEFAULT '0' COMMENT '金额',
  `channel_txn_id` varchar(64) DEFAULT NULL COMMENT '支付渠道单号',
  `prepay_id` varchar(64) DEFAULT NULL COMMENT '预支付交易会话标识',
  `channel` varchar(64) DEFAULT NULL COMMENT '支付渠道名称',
  `channel_code` varchar(64) DEFAULT NULL COMMENT '支付渠道代码',
  `status` int(10) NOT NULL COMMENT '状态:0:等待支付;1:支付成功;',
  `status_des` varchar(20) DEFAULT NULL,
  `subject` varchar(512) NOT NULL,
  `body` varchar(1024) NOT NULL,
  `attach` text COMMENT '附加数据',
  `open_id` varchar(512) NOT NULL COMMENT '付款人用户标识',
  `notify_url` varchar(1024) DEFAULT NULL,
  `version` bigint(20) DEFAULT NULL,
  `txn_time` varchar(30) DEFAULT NULL,
  PRIMARY KEY (`trade_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='代收经销商付款订单';





CREATE TABLE `gs_collection_dealer_account_log` (
  `log_id` varchar(64) NOT NULL COMMENT '主键',
  `dealer_id` int(20) NOT NULL COMMENT '(渠道id)',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `dealer_balance` bigint(20) DEFAULT '0' COMMENT '代收经销商当前余额 (单位：分)',
  `amount` bigint(20) DEFAULT '0' COMMENT '金额',
  `IN_OR_OUT` char(1) NOT NULL COMMENT '1:收入 2:支出',
  `trade_no` varchar(64) DEFAULT NULL COMMENT '系统单号',
  `channel_txn_id` varchar(64) DEFAULT NULL COMMENT '支付渠道单号',
  `channel` varchar(64) DEFAULT NULL COMMENT '支付渠道名称',
  `action` varchar(20) NOT NULL,
  `content` varchar(512) NOT NULL,
  `open_id` varchar(512) NOT NULL,
  PRIMARY KEY (`log_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='代收经销商资金账户日志表';

INSERT INTO `gs_collection_dealer_account` VALUES ('1', now(), '0', '0', '0', '0', '0', '1', null, '代收党费', null, '0');

#####支付渠道配置

#####中国银行微信公众号支付
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('70', 'merchatKey', '5dd64700983ca24f53d8f95c9f54421b', '', 'BOC_WXPAY_1');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('71', 'mchId', 'm20180126000009289', '', 'BOC_WXPAY_1');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('72', 'appId', 'a20180126000009289', '', 'BOC_WXPAY_1');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('73', 'notifyUrl', 'http://pay.yeyeku.com/collection-pay/callback/weixin', '', 'BOC_WXPAY_1');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('74', 'defaultIp', '************', '', 'BOC_WXPAY_1');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('75', 'enabled', 'true', '', 'BOC_WXPAY_1');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('76', 'tradeUrl', 'https://api.boc.dcorepay.com/pay/unifiedorder', '', 'BOC_WXPAY_1');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('77', 'notifySignCheck', 'true', '', 'BOC_WXPAY_1');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('78', 'channelAccount', 'G端代收党费-中国共产党重庆市直属机关工作委员会-中国银行微信公众号支付账号', '', 'BOC_WXPAY_1');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('79', 'store_id', 's20181204000013490', '门店id', 'BOC_WXPAY_1');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('80', 'store_name', '固守大数据', '门店名称', 'BOC_WXPAY_1');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('81', 'tradeQueryUrl', 'https://api.boc.dcorepay.com/pay/orderquery',
 '查询订单url', 'BOC_WXPAY_1');
 
#####银联wap测试   
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('90', 'merId', '***************', '商户号码', 'UNIONPAY_1_TEST');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('91', 'acpsdk.version', '5.1.0', '报文版本号，固定5.1.0，请勿改动', 'UNIONPAY_1_TEST');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('92', 'acpsdk.signMethod', '01', '签名方式，证书方式固定01，请勿改动', 'UNIONPAY_1_TEST');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('93', 'acpsdk.ifValidateCNName', 'false', '是否验证验签证书的CN，测试环境请设置false，生产环境请设置true。非false的值默认都当true处理。', 'UNIONPAY_1_TEST');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('94', 'acpsdk.ifValidateRemoteCert', 'false', '是否验证https证书，测试环境请设置false，生产环境建议优先尝试true，不行再false。非true的值默认都当false处理。', 'UNIONPAY_1_TEST');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('95', 'acpsdk.backUrl', 'http://pay.yeyeku.com/collection-pay/callback/unionpay', '后台通知地址，填写接收银联后台通知的地址，必须外网能访问', 'UNIONPAY_1_TEST');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('96', 'acpsdk.frontUrl', 'http://localhost:8080/ACPSample_B2C/frontRcvResponse', '前台通知地址，填写银联前台通知的地址，必须外网能访问', 'UNIONPAY_1_TEST');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('97', 'acpsdk.signCert.path', '/home/<USER>/appdata/tmp/collection-pay/certs/***************/acp_test_sign.pfx', '签名证书路径', 'UNIONPAY_1_TEST');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('98', 'acpsdk.signCert.pwd', '000000', '签名证书密码，测试环境固定000000，生产环境请修改为从cfca下载的正式证书的密码，正式环境证书密码位数需小于等于6位，否则上传到商户服务网站会失败', 'UNIONPAY_1_TEST');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('99', 'acpsdk.signCert.type', 'PKCS12', '签名证书类型，固定不需要修改', 'UNIONPAY_1_TEST');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('100', 'acpsdk.encryptCert.path', '/home/<USER>/appdata/tmp/collection-pay/certs/***************/acp_test_enc.cer', '敏感信息加密证书路径(商户号开通了商户对敏感信息加密的权限，需要对 卡号accNo，pin和phoneNo，cvn2，expired加密（如果这些上送的话），对敏感信息加密使用)', 'UNIONPAY_1_TEST');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('101', 'acpsdk.middleCert.path', '/home/<USER>/appdata/tmp/collection-pay/certs/***************/acp_test_middle.cer', '验签中级证书路径(银联提供)', 'UNIONPAY_1_TEST');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('102', 'acpsdk.rootCert.path', '/home/<USER>/appdata/tmp/collection-pay/certs/***************/acp_test_root.cer', ' 验签根证书路径(银联提供)', 'UNIONPAY_1_TEST');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('103', 'acpsdk.frontTransUrl', 'https://gateway.test.95516.com/gateway/api/frontTransReq.do',null, 'UNIONPAY_1_TEST');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('104', 'acpsdk.backTransUrl', 'https://gateway.test.95516.com/gateway/api/backTransReq.do', null, 'UNIONPAY_1_TEST');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('105', 'acpsdk.singleQueryUrl', 'https://gateway.test.95516.com/gateway/api/queryTrans.do', null, 'UNIONPAY_1_TEST');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('106', 'acpsdk.batchTransUr', 'https://gateway.test.95516.com/gateway/api/batchTrans.do', null, 'UNIONPAY_1_TEST');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('107', 'acpsdk.fileTransUrl', 'https://filedownload.test.95516.com/',null, 'UNIONPAY_1_TEST');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('108', 'acpsdk.appTransUrl', 'https://gateway.test.95516.com/gateway/api/appTransReq.do', null, 'UNIONPAY_1_TEST');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('109', 'acpsdk.cardTransUrl', 'https://gateway.test.95516.com/gateway/api/cardTransReq.do',null, 'UNIONPAY_1_TEST');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('110', 'group_description', '银联wap测试','配置描述', 'UNIONPAY_1_TEST');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('111', 'enabled', 'true', '是否启用', 'UNIONPAY_1_TEST'); 
 
 
 
 UPDATE gs_asset_deposit_channel_conf SET option_value='https://collectionpay.goodsogood.com/collection-pay/callback/unionpay' WHERE id`='95' ;
 UPDATE gs_asset_deposit_channel_conf SET option_value='https://collectionpay.goodsogood.com/collection-pay/callback/weixin' WHERE id`='73' ;
 
 
### 20181214
### 配置openapi 权限
###创建权限项-代收款
INSERT INTO gs_openapi_api VALUES('17175465977421234283','/v1.0/collection-pay/service','代收款','BASIC','OPEN','collection-pay',0,'代收款',NOW());
##给党费系统 (appid=82CE08978AAC4BCABEFFCCFE2FE2B9A0)添加权限
INSERT INTO gs_openapi_api_auth values('82CE08978AAC4BCABEFFCCFE2FE2B9A0','17175465977421234283','/v1.0/collection-pay/service',0,NOW(),'collection-pay');
 
####
alter table gs_collection_dealer_trade ADD USER_ID VARCHAR(64) COMMENT '用户唯一标识';
CREATE INDEX idx_crt_time ON gs_collection_dealer_trade (CREATE_TIME) ;
CREATE INDEX idx_dealer_id ON gs_collection_dealer_trade (dealer_id) ;
CREATE INDEX idx_status ON gs_collection_dealer_trade (status) ;
CREATE INDEX idx_uid ON gs_collection_dealer_trade (USER_ID) ;
######20190108

#####中国工商银行微信公众号支付-(沙坪坝区党费缴纳专用)
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('ICBC_WX_SPB_1', 'sign_type', 'RSA2', '签名类型，CA-工行颁发的证书认证，RSA-RSAWithSha1，RSA2-RSAWithSh256，缺省为RSA', 'ICBC_WX_SPB');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('ICBC_WX_SPB_10', 'mer_id', '************', '商户号', 'ICBC_WX_SPB');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('ICBC_WX_SPB_11', 'encrypt_key', '5ijWznYklQxghro7KQ1nQA==', '对称加密的密钥', 'ICBC_WX_SPB');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('ICBC_WX_SPB_12', 'rsa_private_key', 'MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC0vJSfdgunBRf3aoHd2U9gZKqFfucNK8cnlm6py4RsbL5YOkq4RVOVdhzCaL55CxmvRgwuO0FOi6TvUiYKfG0/qu3qmFrBWMUpWtPQRnX39NSGCVnp4S4KpMbQ17F7aVQ+fj5YX1YnMljYTv7viwX96/hC0owDdWqEnF3JnhIcyW5KTvyjlToP9wltD2x/LvRTsNvROq07jWLMh35CeSG4ak0/RN44ayFoLBfdIJY/KUeAbo/2baBsB3TuKXD5Pt5nc4kMrSvYdIpV0kYfb01e8AIz3c6aNi2QTDDEqxitqF1r6AKpMjbUQ59aa3pXiTHDCs7JHZppLqZKdbf7ywPnAgMBAAECggEBAIVglZ2IE9lR3PntU6FQeU99eonBuOjzYalJTqlnU3ZgWafqfLG4F5K480hU3BufoXjDjMGhYPSCAVnLmd8rK8RMc/8oJ/F/fSCjsqSFGxG9UAkKZZw/XSblqwL76jJU7KKhr4iv8nhCo2YeBezo/6b/uNo3XrQ+tjje3Cepy1wJ3RGisVqlMXBPeAnIqdpE9s3Q8bE3iNmZqsRoFv7EC1vQb6zE6JqG7Hyb/cjmF4LPt3W2HR90wz80KycQzdvU1zrC03mdHLrG20V/cXQ+Mv33W3UOrp2e41wRNv9jJ9/DpqrYFPBgt77+k3lECopsmQ/q/WaS5Gt9ZC/p8/YAKEECgYEA3J39kj+k8tyEHZkAlGcqEphEjamObHh7M42kccqkVZwCRePH+bVWTnNPnJDWfZbKsDDoe/HpNbArL7xP+7pniMUaGiKVYwO1K1o/du6YYUZ54HKexu+fi3CU35/d8l6CM0b4rjWH1ej86TMFcxF8sxXhdgS8Vxo7CweDG+4KBqkCgYEA0bkxPQKSm/1ur09X5GEyzMGgxiablD1/9qPNac3BZE0178kjeQVWuAxhtY+OVYDi6KuIQ+GHpF3Fj4Pk6FZZjVIX+fQlmRDb9vAVXb+sQ15szuy+tBiH2Xmc1bQLgeMbX/PHQEAkttb1kH0GGHla4RBFX11fcohBtu1rZqKToA8CgYB+xX5/OnkzYQm95DyUCk//zWWfLa58oCLTdfHnvXT/CJ25wXEZCV2lyNcXdZb7JdoksoX1KwZCZo/6ZzBiyzNRQUmFeRf0hJ3nYn2Tfvkq9I92LQcV833vZ2r0sW2dJzeT9ZVSrcn3XmFuM3ZPL95DHjCnW4NKXfVNmix5sSgruQKBgHSBU5gX8MgVTsK26hWl5WmX+y94zR7Z11d1gaxQMCn/NoJhk4IsdBNEVZdeC2z2b5dfixBink6yBQkH9UcYiSEXxNgHSEey9O9GSihNTW4Ta5C8NeioY/dtNrIFFgtu1YtFSqAnwf2Mhthgi91WtSSoPFGQ9nASv6bWV76pfCcDAoGBAKovP5RT5TAsR2WjDfsrEbzVca7jacnDYejScVIDC2fe5IlzUETynv3TYUQcVx+Fl/vtGu+xNFILSprk4xcDmf0D0V6Y5m4ZYjKop9xM7OKYByUbcPOi8aiHNEy1kRvhSB6QHdza2V827g11z1KDp+3ca+6tckJzaVQQ6xRCc0WV', 'RSA私钥', 'ICBC_WX_SPB');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('ICBC_WX_SPB_13', 'rsa_pub_key', 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtLyUn3YLpwUX92qB3dlPYGSqhX7nDSvHJ5ZuqcuEbGy+WDpKuEVTlXYcwmi+eQsZr0YMLjtBTouk71ImCnxtP6rt6phawVjFKVrT0EZ19/TUhglZ6eEuCqTG0Nexe2lUPn4+WF9WJzJY2E7+74sF/ev4QtKMA3VqhJxdyZ4SHMluSk78o5U6D/cJbQ9sfy70U7Db0TqtO41izId+QnkhuGpNP0TeOGshaCwX3SCWPylHgG6P9m2gbAd07ilw+T7eZ3OJDK0r2HSKVdJGH29NXvACM93OmjYtkEwwxKsYrahda+gCqTI21EOfWmt6V4kxwwrOyR2aaS6mSnW3+8sD5wIDAQAB', 'RSA公钥', 'ICBC_WX_SPB');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('ICBC_WX_SPB_14', 'icbc_pub_key', 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCMpjaWjngB4E3ATh+G1DVAmQnIpiPEFAEDqRfNGAVvvH35yDetqewKi0l7OEceTMN1C6NPym3zStvSoQayjYV+eIcZERkx31KhtFu9clZKgRTyPjdKMIth/wBtPKjL/5+PYalLdomM4ONthrPgnkN4x4R0+D4+EBpXo8gNiAFsNwIDAQAB', '工行网关RSA公钥', 'ICBC_WX_SPB');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('ICBC_WX_SPB_15', 'tradeQueryUrl', 'https://gw.open.icbc.com.cn/api/qrcode/V2/query', '查询地址', 'ICBC_WX_SPB');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('ICBC_WX_SPB_2', 'app_id', '10000000000000637002', 'APP的编号,应用在API开放平台注册时生成', 'ICBC_WX_SPB');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('ICBC_WX_SPB_3', 'tp_app_id', '', '第三方应用ID；商户在微信公众号内接入时必送，上送微信分配的公众账号ID；商户通过支付宝生活号接入时必送，上送支付宝分配的应用ID', 'ICBC_WX_SPB');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('ICBC_WX_SPB_4', 'notifyUrl', 'http://pay.yeyeku.com/collection-pay/callback/ICBC_WX', '', 'ICBC_WX_SPB');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('ICBC_WX_SPB_5', 'encrypt_type', 'AES', '加密方式', 'ICBC_WX_SPB');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('ICBC_WX_SPB_6', 'enabled', 'true', '是否启用', 'ICBC_WX_SPB');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('ICBC_WX_SPB_7', 'tradeUrl', 'https://gw.open.icbc.com.cn/ui/aggregate/payment/request/V1', '下单链接', 'ICBC_WX_SPB');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('ICBC_WX_SPB_8', 'ca', '', '采用ca认证方式时，需上送证书', 'ICBC_WX_SPB');
INSERT INTO `gs_asset_deposit_channel_conf` VALUES ('ICBC_WX_SPB_9', 'channelAccount', '党费系统-中国工商银行微信公众号支付-(沙坪坝区党费缴纳专用)', '', 'ICBC_WX_SPB');

#######








 
 
 
