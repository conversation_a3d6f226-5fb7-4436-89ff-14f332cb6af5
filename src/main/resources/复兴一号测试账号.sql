INSERT INTO `gs_asset_deposit_channel_conf` (`id`, `option_key`, `option_value`, `description`, `channel`) VALUES (281, 'enabled', 'true', '是否可用', 'BOC_RENAISSANCE_2');
INSERT INTO `gs_asset_deposit_channel_conf` (`id`, `option_key`, `option_value`, `description`, `channel`) VALUES (280, 'verifier_file_url', 'E:cert/zzb-testdecrypt.cer', '验签文件地址', 'BOC_RENAISSANCE_2');
INSERT INTO `gs_asset_deposit_channel_conf` (`id`, `option_key`, `option_value`, `description`, `channel`) VALUES (279, 'wxAppId', 'wx40440ae941525083', 'wxAPPId', 'BOC_RENAISSANCE_2');
INSERT INTO `gs_asset_deposit_channel_conf` (`id`, `option_key`, `option_value`, `description`, `channel`) VALUES (278, 'queryUrl', 'https://fuxing1.boc.cn/pbpsdpbp/GWPay.asmx/Query', '主动查询地址', 'BOC_RENAISSANCE_2');
INSERT INTO `gs_asset_deposit_channel_conf` (`id`, `option_key`, `option_value`, `description`, `channel`) VALUES (277, 'asyncNotifyUrl', 'https://hyxfzhdw.12371.gov.cn/gsyw/ows-pay/collection-pay/callback/boc_renaissance', '异常回调地址', 'BOC_RENAISSANCE_2');
INSERT INTO `gs_asset_deposit_channel_conf` (`id`, `option_key`, `option_value`, `description`, `channel`) VALUES (276, 'sign_file_name_url', 'E:cert/zzb-test/TEST-OUTLINK-PBPS-DPBP.pfx', '测试账号验签文件路径', 'BOC_RENAISSANCE_2');
INSERT INTO `gs_asset_deposit_channel_conf` (`id`, `option_key`, `option_value`, `description`, `channel`) VALUES (275, 'bankAccountId', 'P05000000001', '测试账号标识', 'BOC_RENAISSANCE_2');
INSERT INTO `gs_asset_deposit_channel_conf` (`id`, `option_key`, `option_value`, `description`, `channel`) VALUES (274, 'prodNo', 'P05', '测试账号产品号', 'BOC_RENAISSANCE_2');
INSERT INTO `gs_asset_deposit_channel_conf` (`id`, `option_key`, `option_value`, `description`, `channel`) VALUES (273, 'sign_file_password', 'aa1111111', '测试账号加签密码', 'BOC_RENAISSANCE_2');
