/*
 Navicat Premium Data Transfer

 Source Server         : 烟草-正式环境
 Source Server Type    : MySQL
 Source Server Version : 80028
 Source Host           : rm-9bqj7o505686kn2ee.mysql.rds.ops.cqyccloud.com:3306
 Source Schema         : gs_ows_cqyc_pay_online

 Target Server Type    : MySQL
 Target Server Version : 80028
 File Encoding         : 65001

 Date: 06/09/2024 09:36:31
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for gs_asset_deposit_channel_conf
-- ----------------------------
CREATE TABLE `gs_asset_deposit_channel_conf`  (
  `id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `option_key` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `option_value` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `channel` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_opt_key`(`option_key` ASC) USING BTREE,
  INDEX `idx_opt_group`(`channel` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for gs_collection_alipay_config
-- ----------------------------
CREATE TABLE `gs_collection_alipay_config`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `org_id` bigint NOT NULL COMMENT '组织id',
  `org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商户名称',
  `account_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '账号id',
  `app_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '第三方商户app_id',
  `app_auth_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '授权token',
  `auth_time` datetime NULL DEFAULT NULL COMMENT '授权时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `org_id`(`org_id` ASC) USING BTREE,
  INDEX `app_id`(`app_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 50 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for gs_collection_dealer_trade
-- ----------------------------
CREATE TABLE `gs_collection_dealer_trade`  (
  `trade_no` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键,系统单号',
  `dealer_id` int NOT NULL COMMENT '(渠道id)',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `pay_time` datetime NULL DEFAULT NULL COMMENT '付款时间',
  `amount` bigint NULL DEFAULT 0 COMMENT '金额',
  `channel_txn_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '支付渠道单号',
  `prepay_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预支付交易会话标识',
  `channel` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '支付渠道名称',
  `channel_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '支付渠道代码',
  `status` int NOT NULL COMMENT '状态:0:等待支付;1:支付成功;',
  `status_des` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `subject` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `body` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `attach` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '附加数据',
  `open_id` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '付款人用户标识',
  `notify_url` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `version` bigint NULL DEFAULT NULL,
  `txn_time` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `USER_ID` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户唯一标识',
  `org_id` bigint NULL DEFAULT NULL,
  `out_trade_no` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `refund_operator` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `refund_time` datetime NULL DEFAULT NULL,
  `refunded_amount` bigint NULL DEFAULT NULL,
  `service_fee` int NULL DEFAULT NULL,
  PRIMARY KEY (`trade_no`) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_uid`(`USER_ID` ASC) USING BTREE,
  INDEX `idx_dealer_id`(`dealer_id` ASC) USING BTREE,
  INDEX `idx_crt_time`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '代收经销商付款订单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for gs_collection_dealer_account
-- ----------------------------
CREATE TABLE `gs_collection_dealer_account`  (
  `dealer_id` int NOT NULL AUTO_INCREMENT COMMENT '主键(渠道id)',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `balance` bigint NULL DEFAULT 0 COMMENT '当前余额 (单位：分)',
  `wxpay_sum` bigint NULL DEFAULT 0 COMMENT '微信充值总金额',
  `unionpay_sum` bigint NULL DEFAULT 0 COMMENT '银联充值总金额',
  `wxpay_num` bigint NULL DEFAULT 0 COMMENT '微信充值总笔数',
  `unionpay_num` bigint NULL DEFAULT 0 COMMENT '银联充值总笔数',
  `status` int NULL DEFAULT NULL COMMENT '1表示正常，2表示锁定  ',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `dealer_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '渠道名称',
  `outer_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '外部id',
  `version` bigint NULL DEFAULT 0,
  PRIMARY KEY (`dealer_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '代收经销商资金账户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for gs_collection_dealer_account_log
-- ----------------------------
CREATE TABLE `gs_collection_dealer_account_log`  (
  `log_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
  `dealer_id` int NOT NULL COMMENT '(渠道id)',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `dealer_balance` bigint NULL DEFAULT 0 COMMENT '代收经销商当前余额 (单位：分)',
  `amount` bigint NULL DEFAULT 0 COMMENT '金额',
  `IN_OR_OUT` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '1:收入 2:支出',
  `trade_no` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '系统单号',
  `channel_txn_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '支付渠道单号',
  `channel` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '支付渠道名称',
  `action` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `content` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `open_id` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`log_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '代收经销商资金账户日志表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
