
spring.jpa.database=ORACLE
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.Oracle12cDialect
################
server.port=8137
server.context-path=/
################ AOP
spring.aop.auto=true
spring.aop.proxy-target-class=true
################ SPRING MVC (WebMvcProperties)

# set fixed locale, e.g. en_UK
spring.mvc.locale=zh_CN
# set fixed date format, e.g. dd/MM/yyyy
spring.mvc.date-format=yyyy-MM-dd
spring.http.encoding.charset=UTF-8
# enable http encoding support
spring.http.encoding.enabled=true
# force the configured encoding
spring.http.encoding.force=true


########redis config
redis.address=**************
redis.password=1234567890
redis.port=16379
redis.maxIdel=1
redis.maxTotal=10
redis.timeout=3000
redis.debug=false

#??? - ????????????
sys-dm-db.db-url: ***************:55237
sys-dm-db.db-name: GS_OWS_ZY_GRAY
sys-dm-db.db-user: SYSDBA
sys-dm-db.db-password: Dameng@123456

spring.datasource.url=jdbc:dm://${sys-dm-db.db-url}/${sys-dm-db.db-name}?stringtype=unspecified&currentSchema=GS_OWS_ZY_TEST
spring.datasource.username=${sys-dm-db.db-user}
spring.datasource.password=${sys-dm-db.db-password}
spring.datasource.driverClassName=dm.jdbc.driver.DmDriver
spring.datasource.initialSize=5
spring.datasource.minIdle=5
spring.datasource.maxActive=15
spring.datasource.maxWait=60000
spring.datasource.timeBetweenEvictionRunsMillis=60000
spring.datasource.minEvictableIdleTimeMillis=300000
spring.datasource.validationQuery=SELECT 1 FROM DUAL
spring.datasource.testWhileIdle=true
spring.datasource.testOnBorrow=false
spring.datasource.testOnReturn=false
spring.datasource.poolPreparedStatements=true
spring.datasource.maxPoolPreparedStatementPerConnectionSize=20
spring.datasource.filters=stat,wall,log4j
spring.datasource.connectionProperties=druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
spring.datasource.useGlobalDataSourceStat=true

################ ?????SQL????
# ????SQL????
dm.sql.conversion.enabled=true
# ????????
dm.sql.division.protection.enabled=true
# ???????NONE, BASIC, STATISTICAL, INTELLIGENT
dm.sql.division.protection.strategy=INTELLIGENT
# ?????????????????false?
dm.sql.detailed.logging.enabled=false
# ?????????????
dm.sql.fallback.interceptor.enabled=false





