

spring.datasource.url=****************************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=1234567890
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.driver-class-name=net.sf.log4jdbc.sql.jdbcapi.DriverSpy
spring.datasource.hikari.minimum-idle=20
spring.datasource.hikari.maximum-pool-size=300
spring.datasource.hikari.pool-name=hikari-jdbc-pool
spring.datasource.hikari.idle-timeout=30000
spring.datasource.hikari.max-lifetime=7170000
spring.datasource.hikari.connection-timeout=60000
spring.datasource.hikari.register-mbeans=false
spring.datasource.hikari.data-source-properties.prepStmtCacheSize=250
spring.datasource.hikari.data-source-properties.prepStmtCacheSqlLimit=2048
spring.datasource.hikari.data-source-properties.cachePrepStmts=true
spring.datasource.hikari.data-source-properties.useServerPrepStmts=true

spring.jpa.database=MYSQL
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL5Dialect
################
server.port=8137
server.context-path=/
################ AOP
spring.aop.auto=true
spring.aop.proxy-target-class=true
################ SPRING MVC (WebMvcProperties)

# set fixed locale, e.g. en_UK
spring.mvc.locale=zh_CN
# set fixed date format, e.g. dd/MM/yyyy
spring.mvc.date-format=yyyy-MM-dd
spring.http.encoding.charset=UTF-8

# enable http encoding support
spring.http.encoding.enabled=true
# force the configured encoding
spring.http.encoding.force=true

########redis config
#redis.address=************
#redis.password=ndvR^eULiG&mlvek
#redis.port=26379
redis.address=**************
redis.password=1234567890
redis.port=6379
redis.database=52
redis.maxIdel=5
redis.maxTotal=10
redis.timeout=3000
redis.debug=true


spring.redis.host=**************
spring.redis.port=6379
spring.redis.database=11
spring.redis.password=1234567890

################ ?????SQL??????????
# ??????????????
dm.sql.conversion.enabled=true
dm.sql.division.protection.enabled=true
dm.sql.division.protection.strategy=INTELLIGENT
dm.sql.detailed.logging.enabled=true
dm.sql.fallback.interceptor.enabled=false





