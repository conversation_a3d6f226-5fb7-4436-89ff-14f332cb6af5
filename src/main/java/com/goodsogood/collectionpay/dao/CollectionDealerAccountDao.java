package com.goodsogood.collectionpay.dao;

import org.springframework.stereotype.Repository;

import com.goodsogood.collectionpay.dao.support.ExtJpaRepository;
import com.goodsogood.collectionpay.entity.CollectionDealerAccountEntity;


@Repository("collectionDealerAccountDao")
public interface CollectionDealerAccountDao extends ExtJpaRepository<CollectionDealerAccountEntity, String> {
	
	CollectionDealerAccountEntity findByDealerId(int dealerId);

}
