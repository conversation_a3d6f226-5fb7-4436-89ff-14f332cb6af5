package com.goodsogood.collectionpay.dao.support;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.NoRepositoryBean;


@NoRepositoryBean
public interface ExtJpaRepository <T, ID extends Serializable> extends JpaRepository<T,ID> {
	
	/**
	 * 直接插入数据库 
	 * @param entity
	 * @return
	 */
	public T create(T entity) ;
	
	public List<Map<String,Object>> findBySql(String sql ,Object[] args);
	
	public int updateBySql(String sql ,Object... args);
	
	public int updateBySql(String sql);
	
	public Map<String, Object> findOneBySql(String sql, Object[] args)  ;
	
	public Object queryUniqueResultBySql(String sql, Object[] args) ;
	
	public PageResult<Map<String, Object>> pageQuery(int pageIndex, int pageSize, Object[] params, String sql, String countSql);

	T update(T entity);

}
