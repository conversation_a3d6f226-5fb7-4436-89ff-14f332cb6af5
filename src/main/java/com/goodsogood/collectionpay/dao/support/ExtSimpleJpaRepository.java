package com.goodsogood.collectionpay.dao.support;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import javax.persistence.EntityManager;
import javax.persistence.Query;

import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.data.jpa.repository.support.JpaEntityInformation;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;


public class ExtSimpleJpaRepository<T, ID extends Serializable> extends SimpleJpaRepository<T, ID>
		implements ExtJpaRepository<T, ID> {

	private final EntityManager em;

	public ExtSimpleJpaRepository(JpaEntityInformation<T, ?> entityInformation, EntityManager em) {
		super(entityInformation, em);
		this.em = em;
	}

	public ExtSimpleJpaRepository(Class<T> domainClass, EntityManager em) {
		super(domainClass, em);
		this.em = em;
	}

	@Override
	public T create(T entity) {
		em.persist(entity);
		flush();
		return entity;
	}
	
	@Override
	public T update(T entity) {
		return em.merge(entity);
	}

	public List<Map<String, Object>> findBySql(String sql, Object[] args) {
		Query query = em.createNativeQuery(sql);
		query.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
		for (int i = 0; i < args.length; i++) {
			query.setParameter(i + 1, args[i]);
		}
		List<Map<String, Object>> datalists = query.getResultList();
		return datalists;
	}

	public int updateBySql(String sql, Object... args) {
		Query query = em.createNativeQuery(sql);
		query.unwrap(SQLQuery.class);
		for (int i = 0; i < args.length; i++) {
			query.setParameter(i + 1, args[i]);
		}
		return query.executeUpdate();
	}

	public int updateBySql(String sql) {
		return updateBySql(sql, new Object[] {});
	}

	/**
	 * 返回一条记录Map
	 * @param sql
	 * @param params
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public Map<String, Object> findOneBySql(String sql, Object[] args) {
		Query query = em.createNativeQuery(sql);
		query.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
		for (int i = 0; i < args.length; i++) {
			query.setParameter(i + 1, args[i]);
		}
		query.setFirstResult(0);
		query.setMaxResults(1);
		List<Map<String, Object>> datalists = query.getResultList();
		return datalists != null && datalists.size() > 0 ? datalists.get(0) : null;
	}

	public Object queryUniqueResultBySql(String sql, Object[] args) {
		Query query = em.createNativeQuery(sql);
		query.unwrap(SQLQuery.class);
		for (int i = 0; i < args.length; i++) {
			query.setParameter(i + 1, args[i]);
		}
		return query.getSingleResult();
	}

	@SuppressWarnings("unchecked")
	public PageResult<Map<String, Object>> pageQuery(int pageIndex, int pageSize, Object[] params, String sql,
			String countSql) {
		PageResult<Map<String, Object>> page = new PageResult<Map<String, Object>>();
		Query query = em.createNativeQuery(sql);
		query.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
		for (int i = 0; i < params.length; i++) {
			query.setParameter(i + 1, params[i]);
		}
		query.setFirstResult((pageIndex - 1) * pageSize);
		query.setMaxResults(pageSize);
		
		List<Map<String, Object>> datalists = query.getResultList();
		// 分页
		// 查询总数
		Query totalQuery = em.createNativeQuery(countSql);
		;
		for (int i = 0; i < params.length; i++) {
			totalQuery.setParameter(i + 1, params[i]);
		}
		Object totals_obj = totalQuery.getSingleResult();
		Long totals = Long.valueOf(totals_obj.toString());
		page.setTotal(totals.intValue());
		page.setList(datalists);
		page.setPageSize(pageSize);
		page.setPageIndex(pageIndex);
		// 封装页面数据参数

		return page;
	}

}
