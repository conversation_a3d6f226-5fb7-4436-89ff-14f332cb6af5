package com.goodsogood.collectionpay.dao.support;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PageResult<T> {
	
	private int total;
	
	private int pageSize;
	
	private int pageIndex;
	
	private List<T> list;
	
	private Map<String, Object> statData =new HashMap<>();

	/**
	 * @return the total
	 */
	public int getTotal() {
		return total;
	}

	/**
	 * @param total the total to set
	 */
	public void setTotal(int total) {
		this.total = total;
	}

	/**
	 * @return the pageSize
	 */
	public int getPageSize() {
		return pageSize;
	}

	/**
	 * @param pageSize the pageSize to set
	 */
	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}

	/**
	 * @return the pageIndex
	 */
	public int getPageIndex() {
		return pageIndex;
	}

	/**
	 * @param pageIndex the pageIndex to set
	 */
	public void setPageIndex(int pageIndex) {
		this.pageIndex = pageIndex;
	}

	/**
	 * @return the list
	 */
	public List<T> getList() {
		return list;
	}

	/**
	 * @param list the list to set
	 */
	public void setList(List<T> list) {
		this.list = list;
	}

	/**
	 * @return the statData
	 */
	public Map<String, Object> getStatData() {
		return statData;
	}

	/**
	 * @param statData the statData to set
	 */
	public void setStatData(Map<String, Object> statData) {
		this.statData = statData;
	}
	
	
	
	

}
