package com.goodsogood.collectionpay.dao;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.goodsogood.collectionpay.dao.support.ExtJpaRepository;
import com.goodsogood.collectionpay.entity.CollectionDealerTradeEntity;


@Repository("collectionDealerTradeDao")
public interface CollectionDealerTradeDao extends ExtJpaRepository<CollectionDealerTradeEntity, String> {
	
	CollectionDealerTradeEntity findByTradeNo(String tradeNo);
	
	CollectionDealerTradeEntity findByTradeNoAndDealerId(String tradeNo,int dealerId);

	List<CollectionDealerTradeEntity> findByOutTradeNoAndDealerId(String outTradeNo,int dealerId);

}
