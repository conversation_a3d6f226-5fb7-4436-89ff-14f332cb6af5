package com.goodsogood.collectionpay.dao;

import com.goodsogood.collectionpay.dao.support.ExtJpaRepository;
import com.goodsogood.collectionpay.entity.CollectionPayPageConfigEntity;

import java.util.List;

public interface CollectionPayPageConfigDao extends ExtJpaRepository<CollectionPayPageConfigEntity, String> {

    /**
     * 根据支付code
     * @param code
     * @return
     */
    CollectionPayPageConfigEntity findByCode (String code);

    /**
     * 根据渠道code 查询支付配置信息
     * @param channelCode
     * @return
     */
    List<CollectionPayPageConfigEntity> findByChannelCode (String channelCode);
}
