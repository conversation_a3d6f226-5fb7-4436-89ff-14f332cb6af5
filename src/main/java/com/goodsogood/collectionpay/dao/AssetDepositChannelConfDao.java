package com.goodsogood.collectionpay.dao;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.goodsogood.collectionpay.dao.support.ExtJpaRepository;
import com.goodsogood.collectionpay.entity.AssetDepositChannelConfEntity;

@Repository("assetDepositChannelConfDao")
public interface AssetDepositChannelConfDao extends ExtJpaRepository<AssetDepositChannelConfEntity, String>  {
	
	List<AssetDepositChannelConfEntity> findByChannel (String channel);
	
	List<AssetDepositChannelConfEntity> findByChannelAndOptionKey (String channel,String OptionKey);

}
