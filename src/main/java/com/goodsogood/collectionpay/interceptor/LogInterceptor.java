package com.goodsogood.collectionpay.interceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import com.alibaba.fastjson.JSON;
import com.goodsogood.collectionpay.api.RequestContext;
import com.goodsogood.collectionpay.consts.LogConfiger;
import com.goodsogood.collectionpay.util.CommonUtil;
import com.goodsogood.collectionpay.util.MethodUtil;
import com.goodsogood.collectionpay.util.UUIDUtils;

/**
 * 记录日志信息
 * 
 * @Description
 * <AUTHOR>
 * @time 2017年6月7日下午5:39:33
 */
@Component
public class LogInterceptor extends HandlerInterceptorAdapter {
	private static final Logger logger = LoggerFactory.getLogger(LogConfiger.APP);

	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
			throws Exception {
		
		RequestContext.init();
		String Autoidgen = request.getHeader("Autoidgen");
		if (!StringUtils.isEmpty(Autoidgen)) {
			MDC.put("req_id", Autoidgen);
			RequestContext.getRequest().setGateWayReqId(Long.valueOf(Autoidgen));
		} else {
			MDC.put("req_id", UUIDUtils.uuid32());
		}
		logger.debug("URI:{}", request.getRequestURI());
		logger.debug("IP:{}", CommonUtil.getIpAddr(request));
		logger.debug(request.getParameter("method"));
		logger.debug("request:");
		logger.debug(JSON.toJSONString(CommonUtil.getParameterMap(request), true));
		RequestContext.getRequest().setRequestUri(request.getRequestURI());
		RequestContext.getRequest().setMethod(request.getParameter("method"));
		return true;
	}

	@Override
	public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
			throws Exception {
		long t = System.currentTimeMillis();
		logger.debug(" {} cost:{} ms! ", request.getRequestURI(), (t - RequestContext.getRequest().getRequestTime()));
		RequestContext.clear();
		MDC.clear();
	}

}
