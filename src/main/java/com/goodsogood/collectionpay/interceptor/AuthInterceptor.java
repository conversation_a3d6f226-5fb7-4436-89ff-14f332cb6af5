package com.goodsogood.collectionpay.interceptor;

import com.goodsogood.collectionpay.consts.LogConfiger;
import com.goodsogood.collectionpay.dao.CollectionDealerAccountDao;
import com.goodsogood.collectionpay.util.MethodUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Enumeration;

public class AuthInterceptor extends HandlerInterceptorAdapter {

	private static final Logger logger = LoggerFactory.getLogger(LogConfiger.APP);
	
	
	@Autowired
	private CollectionDealerAccountDao collectionDealerAccountDao ;

	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
			throws Exception {
		HandlerMethod handlerMethod = (HandlerMethod) handler;
		if (MethodUtil.isSkipInterceptor(handlerMethod, AuthInterceptor.class)) {
			return true;
		}
        String url= request.getRequestURI();
        Enumeration enu=request.getParameterNames();
        while(enu.hasMoreElements()){
            String paraName=(String)enu.nextElement();
            System.out.println(paraName+": "+request.getParameter(paraName));
        }

        System.out.println(url);
		String dealerId = request.getParameter("dealerId");
//		Asserts.check(!StringUtils.isEmpty(dealerId) , ErrorCode.MISSING_PARAMETER, "缺少参数[dealerId]");
//		Asserts.check(CommonUtil.isIntNumber(dealerId) , ErrorCode.INVALID_PARAMETER,
//                "参数[dealerId]不是一个有效的int数字.取值范围[1~**********]");
//		CollectionDealerAccountEntity acct =
//				collectionDealerAccountDao.findByDealerId(Integer.valueOf(dealerId));
//		Asserts.check(acct!=null, ErrorCode.INVALID_PARAMETER, "无效的渠道id[dealerId]");
		return true;
	}

}
