package com.goodsogood.collectionpay.util;

import java.util.Base64;
import org.apache.commons.lang3.StringUtils;
import net.glxn.qrgen.QRCode;

public class QRCodeUtil {

	
	/**
	 * 生成二维码 
	 * @param content 内容
	 * @param width 宽度
	 * @param height 高度 
	 * @return 二维码的 base64 字符.
	 */
	public static String generateQRBase64(String content, int width, int height) {
		if (StringUtils.isEmpty(content)) {
			throw new IllegalArgumentException("二维码的内容不能为空");
		}
		if (width <= 0) {
			width = 200;
		}
		if (height <= 0) {
			height = 200;
		}
		String result = Base64.getEncoder().encodeToString(
				QRCode.from(content).withSize(Integer.valueOf(width), Integer.valueOf(height)).stream().toByteArray());
		return result;

	}
	
	
	public static String generateQRBase64HtmlPage(String QRBase64,String showAmt){
		String wechatTitlePng="data:image/png;base64,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";
		StringBuilder html=new StringBuilder();
		//html.append("<!DOCTYPE html><html><head><meta charset=\"utf-8\"><title>微信支付</title><style>    html {        font-size: calc(100vw/10);        }    html,     body {        width: 100%;        height: 100%;        position: relative;    }    body {        padding: 0;        margin: 0 auto;    }    .content {        padding-top: 2rem;        background-color: #c7c7c7;        height: 100%;    }    .wechat-pay {        width:  7rem;        height: 10rem;        border: 1px solid #ffffff;        margin: auto;    }    .title {        height: 1.33rem;        background: #ffffff url("+wechatTitlePng+")  no-repeat center;        background-size: 3.3rem 0.85rem;    }    .wechat-code {        height: 8.66rem;        background-color: #22AB39;    }    .money-display{        font-size: 0.57rem;        color: #ffffff;        text-align: center;        height: 2rem;        line-height: 2rem;        padding: 0.1rem 1rem;    }    .money-qrcode{        height: 4.6rem;        text-align: center;    }    .money-tip {        font-size: 0.4rem;             color: #ffffff;        text-align: center;    }    .image-set {        height: 4.2rem;        width: 4.2rem;    }</style></head><body><div class=\"content\"><div class=\"wechat-pay\"><div class=\"title\"></div><div class=\"wechat-code\"><div class=\"money-display\">￥"+showAmt+"</div><div class=\"money-qrcode\"><img class=\"image-set\" src=\"data:image/png;base64,"+QRBase64+"\"/></div><div class=\"money-tip\">请使用微信扫码支付</div></div></div></div></body></html>");
		html.append("<!DOCTYPE html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\"/><meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge,chrome=1\"/><meta name=\"renderer\" content=\"webkit\"><meta http-equiv=\"Expires\" content=\"0\"><meta http-equiv=\"Pragma\" content=\"no-cache\"><meta http-equiv=\"Cache-control\" content=\"no-cache\"><meta http-equiv=\"Cache\" content=\"no-cache\"><meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\"><meta name=\"viewport\" content=\"width=device-width, initial-scale=1\"><title>微信支付</title><style>        html {            font-size: calc(100vw/37.5);            }        html,         body {            width: 100%;            height: 100%;            position: relative;        }        body {            padding: 0;            margin: 0 auto;            background-color: #c7c7c7;        }        .content {            padding-top: 13.4rem;        }        .wechat-pay {            width:  26rem;            height: 37.5rem;            border: 1px solid #ffffff;            margin: auto;        }        .title {            height: 5rem;            background: #ffffff url("+wechatTitlePng+")  no-repeat center;            background-size: 12.4rem 3.2rem;        }        .wechat-code {            height: 32.5rem;            background-color: #22AB39;        }        .money-display{            font-size: 1.8rem;            color: #ffffff;            text-align: center;            height: 8rem;            line-height: 8rem;            padding: 0.1rem 1rem;        }        .money-qrcode{            height: 18rem;            text-align: center;        }        .money-tip {            font-size: 1.5rem;                 color: #ffffff;            text-align: center;        }        .image-set {            height: 16rem;            width: 16rem;        }</style></head><body><div class=\"content\"><div class=\"wechat-pay\"><div class=\"title\"></div><div class=\"wechat-code\"><div class=\"money-display\">￥"+showAmt+"</div><div class=\"money-qrcode\"><img class=\"image-set\" src='data:image/png;base64,"+QRBase64+"'/></div><div class=\"money-tip\">请使用微信扫码支付</div></div></div></div></body></html>");
		if (StringUtils.isEmpty(QRBase64)) {
			throw new IllegalArgumentException("二维码的内容不能为空");
		}
		return html.toString();
	}

}
