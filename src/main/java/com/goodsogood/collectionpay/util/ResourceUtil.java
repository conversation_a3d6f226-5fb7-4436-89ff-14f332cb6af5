package com.goodsogood.collectionpay.util;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URL;
import java.nio.charset.Charset;
import org.apache.commons.io.IOUtils;
import org.springframework.core.io.ClassPathResource;

public class ResourceUtil {
	private static final Charset UTF8 = Charset.forName("UTF-8");
	private ResourceUtil(){}
	
	
	/**
	 * 读取包中的文件
	 * @param path 文件路径; e.g : com/goodsogood/open/pay/schema/validation.properties
	 * @return
	 * @throws IOException
	 */
	public static String getResource(String path) throws IOException{
		final String s = path.startsWith("/") ? path : '/' + path;
		URL url = ResourceUtil.class.getResource(s);
		InputStream in = url.openStream();
		InputStreamReader reader = new InputStreamReader(in, UTF8);
		String text = IOUtils.toString(reader);
		in.close();
		reader.close();
		return text;
	}
	
	public static InputStream getClassPathResource(String path) throws IOException{
		ClassPathResource resource =new ClassPathResource(path);
		return resource.getInputStream();
	}
	
	public static InputStream getResource4InputStream(String path) throws IOException{
		final String s = path.startsWith("/") ? path : '/' + path;
		URL url = ResourceUtil.class.getResource(s);
		return url.openStream();
	}
	
	public static URL getResource4URL(String path) throws IOException{
		final String s = path.startsWith("/") ? path : '/' + path;
		return ResourceUtil.class.getResource(s);
	}

}
