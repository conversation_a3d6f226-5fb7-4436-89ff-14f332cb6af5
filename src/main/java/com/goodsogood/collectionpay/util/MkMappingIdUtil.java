package com.goodsogood.collectionpay.util;

import org.jboss.aerogear.security.otp.api.Base32;
import org.springframework.stereotype.Component;

import com.goodsogood.cipher.aes.AesCipher;
import com.goodsogood.collectionpay.model.MkMappingVO;

/**
 * 麦卡映射ID工具类
 * 具体加解密代码如下：
 * <pre>
 *  AesCipher testcipher = new AesCipher("testkey");
    byte[] encstr = testcipher.encryptWithHmac("testdata".getBytes("UTF-8"));
    
    AesCipher testcipher = new AesCipher("testkey");
    byte[] decstr = testcipher.decryptWithHmac(encstr);
 * <AUTHOR>
 *
 */
public class MkMappingIdUtil {

    private final static String key = "MK.MAPPING";
    
    /**
     * 根据麦卡福利社ID 获取对应的映射ID
     */
    public static MkMappingVO getByMkUserId(String mkUserId, String channelAppId) {
        
        try {
            StringBuilder content = new StringBuilder(); 
            content.append(mkUserId);
            content.append(",");
            content.append(channelAppId);
            
            AesCipher cipher = new AesCipher(key);
            byte[] encByte = cipher.encryptWithHmac(content.toString().getBytes("UTF-8"));
            String encStr = Base32.encode(encByte);
            
            MkMappingVO vo = new MkMappingVO();
            vo.setMkMappingId(encStr);
            vo.setChannelAppId(channelAppId);
            vo.setMkUserId(mkUserId);
            return vo;
        } catch(Exception e) {
            return null;
        }
    }
    
    public static MkMappingVO getByMkMappingId(String mkMappingId) {
        AesCipher cipher;
        try {
            cipher = new AesCipher(key);
            byte[] en = Base32.decode(mkMappingId);
            byte[] decstr = cipher.decryptWithHmac(en);
            String content = new String(decstr);
            
            String[] arr = content.split(",");
            String mkUserId = arr[0];
            String channelAppId = arr[1]; 
            
            MkMappingVO vo = new MkMappingVO();
            vo.setMkMappingId(mkMappingId);
            vo.setChannelAppId(channelAppId);
            vo.setMkUserId(mkUserId);
            return vo;
        } catch (Exception e) {
            return null;
        }
    }
    
    public static void main(String[] args) throws Exception {
//        String mkUserId = "79512829E2A646379C5D1876CC6A10B0";
//        String channelAppId = "2";
//        MkMappingVO vo = new MkMappingIdUtil().getByMkUserId(mkUserId, channelAppId);
//        System.out.println(vo);
        
        
//        MkMappingVO vo2 = new MkMappingIdUtil().getByMkMappingId("FAUF5XPC6JAS2CKHODY4FIWP55V7KGKKBVO53E43BOHCEY3HUZ4X6IKWSROYAXKV7EITKRKJUX4PT5Q6ZSUAUGQ");
//        System.out.println(vo2);
        
        /*
         * create table temp_b_mkmappingid(
         * user_id char(33) primary key , 
         * mk_mapping_id varchar(255)
         * )
         * 
         */
        
        //String appChannelId = "9";
        //FileInputStream fis = new FileInputStream(new File("D:\\upgrade\\20180507\\b_mkuserid.txt"));
//        String appChannelId = "6";
//        FileInputStream fis = new FileInputStream(new File("D:\\test\\mkuserid.txt"));
//        BufferedReader br = new BufferedReader(new InputStreamReader(fis, "utf-8"));
//        
//        System.out.println("===============start================");
//        String str = null;
//        while( (str = br.readLine()) != null) {
//            String mkUserId = str;
//            MkMappingVO vo = new MkMappingIdUtil().getByMkUserId(mkUserId, appChannelId);
//            StringBuilder sql = new StringBuilder();
//            sql.append("insert into temp_b_mkmappingid values(");
//            sql.append("'").append(vo.getMkUserId()).append("',");
//            sql.append("'").append(vo.getMkMappingId()).append("');");
//            System.out.println(sql);
//        }
//        System.out.println("===============end================");
//        br.close();
    }
    
}
