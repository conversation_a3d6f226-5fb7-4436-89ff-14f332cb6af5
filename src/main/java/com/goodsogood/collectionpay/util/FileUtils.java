package com.goodsogood.collectionpay.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileWriter;
import com.goodsogood.collectionpay.consts.ErrorCode;
import com.goodsogood.collectionpay.consts.LogConfiger;
import com.goodsogood.collectionpay.exception.AssetServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.UUID;

/**
 * 生成文件 目前用于017项目
 */
@Component
public class FileUtils {
    //生成017ftp文件目录
    //private final String fileTempDir="D:\\temp";
    //生成017ftp文件目录 正式环境地址
    private final String fileTempDir="/home/<USER>/appdata/tmp/collection-pay";
    private static final Logger logger = LoggerFactory.getLogger(LogConfiger.SYS_DATA);

    /**
     * 創建文件 是否成功
     * @param fileName
     */
    private File createFile(String fileName){
        fileName = fileTempDir + "\\"+fileName;
        return FileUtil.touch(fileName);
    }

    /**
     * 寫入文件內容
     * @param fileName
     */
    private void FileWriter(String fileName,String content){
        FileWriter writer = new FileWriter(fileName);
        writer.write(content);
    }

    /**
     * 根據訂單號生成文件名稱
     */
    private String createFileName(String userId,String orderId){
        return userId + "_" + orderId + ".txt";
    }


    /**
     * 生成文件內容
     */
    public void createFileContent(String userId,String orderId,String content){
        try {
            logger.info("开始生成文件3");
            String fileName = createFileName(userId, orderId);
            File file = createFile(fileName);
            if (file.exists()) {
                logger.info("开始生成文件4");
                FileWriter(file.getAbsolutePath(), content);
            }else {
                logger.info("创建文件========="+fileName+"==========失败");
            }
        }catch (Exception exception){
            logger.error("生成打包文件數據失敗",exception);
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, ""+orderId+"-生成ftp文件失败！");
        }
    }

    /**
     * 批量生成文件內容
     */
    public void createBatchFileContent(String content){
        try {
            String fileName = UUID.randomUUID().toString() + ".txt";
            File file = createFile(fileName);
            if (file.exists()) {
                FileWriter(file.getAbsolutePath(), content);
            }else {
                logger.info("批量创建文件========="+fileName+"==========失败");
            }
        }catch (Exception exception){
            logger.error("批量生成打包文件失败",exception);
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "批量生成打包文件失败！");
        }
    }



}
