package com.goodsogood.collectionpay.util;

import org.apache.http.util.TextUtils;

import com.goodsogood.collectionpay.consts.ErrorCode;
import com.goodsogood.collectionpay.exception.AssetServiceException;

public class Asserts {

    public static void check(final boolean expression, final String message) {
        if (!expression) {
            throw new IllegalStateException(message);
        }
    }
    
    public static void bizCheck(final boolean expression, final String message) {
        if (!expression) {
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR,message);
        }
    }
    
    public static void check(final boolean expression,final String errCode , final String message) {
        if (!expression) {
            throw new AssetServiceException(errCode,message);
        }
    }

    public static void check(final boolean expression, final String message, final Object... args) {
        if (!expression) {
            throw new IllegalStateException(String.format(message, args));
        }
    }
    
    public static void bizCheck(final boolean expression, final String message, final Object... args) {
        if (!expression) {
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR,CommonUtil.StringFormat(message,args));
        }
    }
    

    public static void check(final boolean expression, final String message, final Object arg) {
        if (!expression) {
            throw new IllegalStateException(String.format(message, arg));
        }
    }

    public static void notNull(final Object object, final String name) {
        if (object == null) {
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR,name);
        }
    }
    
    public static void notNull(final Object object,final String errCode, final String name) {
        if (object == null) {
            throw new AssetServiceException(errCode,name);
        }
    }

    public static void notEmpty(final CharSequence s, final String name) {
        if (TextUtils.isEmpty(s)) {
            throw new IllegalStateException(name + " is empty");
        }
    }
    
    public static void notEmpty(final CharSequence s, final String errCode,final String name) {
        if (TextUtils.isEmpty(s)) {
        	 throw new AssetServiceException(errCode,name);
        }
    }

    public static void notBlank(final CharSequence s, final String name) {
        if (TextUtils.isBlank(s)) {
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR,name);
        }
    }

}