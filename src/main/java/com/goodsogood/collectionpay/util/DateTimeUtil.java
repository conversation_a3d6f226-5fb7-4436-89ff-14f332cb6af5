package com.goodsogood.collectionpay.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;


public class DateTimeUtil {
	
	private DateTimeUtil(){}
	
	public static Long getUnixTimestamp(){
		return System.currentTimeMillis() / 1000;
	}
	
	/**
	 * 返回今天0点0分的时间戳
	 * 
	 * @return
	 */
	public static long getTodayBegin() {
		SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
		try {
			return (format.parse(format.format(new Date())).getTime()) / 1000;
		} catch (ParseException e) {
			return 0;
		}
	}

	public static String format(Date date){
		if(date==null){
			return null ;
		}
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		return dateFormat.format(date);
	}

    public static String formatICBCH5(Date date){
        if(date==null){
            return null ;
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        return dateFormat.format(date);
    }

	public static String format4ICBC(Date date){
		if(date==null){
			return null ;
		}
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
		return dateFormat.format(date);
	}

    public static String getFormatDateStr(Date date,String timeType){
        if(date==null){
            return null ;
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat(timeType);
        return dateFormat.format(date);
    }
	
	
	public static void main(String[] args) {
        Calendar nowTime = Calendar.getInstance();
        nowTime.add(Calendar.MINUTE, 5);
        Date expireDate=nowTime.getTime();
        System.out.println(getFormatDateStr(expireDate,"yyyyMMddHHmmss"));
//        System.out.println(getFormatDateStr(new Date(),"yyyyMMddHHmmss"));
//		System.out.println(DateTimeUtil.format4ICBC(new Date()));
//		System.out.println(DateTimeUtil.format4ICBC(new Date(System.currentTimeMillis() + (1000 * 60 * 20))));
		 ;
	}
	
	
}
