package com.goodsogood.collectionpay.util;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.servlet.http.HttpServletRequest;

import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;

public class CommonUtil {
	
	private static Pattern numberPattern = Pattern.compile("^[1-9]{1,1}[0-9]{0,10}$");

	public static String subNoSuffix(String cardNo) {
		return cardNo.substring(cardNo.length() - 4);
	}
	
	public static boolean isNumber(String str){
		return numberPattern.matcher(str).find();
	}
	
	public static boolean isIntNumber(String str){
		return isNumber(str) && str.length() <=10 && Long.valueOf(str).longValue() <=Integer.MAX_VALUE;
	}
	

	/**
	 * 获取用户真实IP地址，不使用request.getRemoteAddr()的原因是有可能用户使用了代理软件方式避免真实IP地址,
	 * 可是，如果通过了多级反向代理的话，X-Forwarded-For的值并不止一个，而是一串IP值
	 * 
	 * @return ip
	 */
	public static String getIpAddr(HttpServletRequest request) {
		String ip = request.getHeader("X-Forwarded-For");
		System.out.println("x-forwarded-for ip: " + ip);
		if (ip != null && ip.length() != 0 && !"unknown".equalsIgnoreCase(ip)) {
			// 多次反向代理后会有多个ip值，第一个ip才是真实ip
			if (ip.indexOf(",") != -1) {
				ip = ip.split(",")[0];
			}
		}

		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("x-forwarded-for");
			if (ip != null && ip.length() != 0 && !"unknown".equalsIgnoreCase(ip)) {
				// 多次反向代理后会有多个ip值，第一个ip才是真实ip
				if (ip.indexOf(",") != -1) {
					ip = ip.split(",")[0];
				}
			}
		}

		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("Proxy-Client-IP");
			System.out.println("Proxy-Client-IP ip: " + ip);
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("WL-Proxy-Client-IP");
			System.out.println("WL-Proxy-Client-IP ip: " + ip);
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("HTTP_CLIENT_IP");
			System.out.println("HTTP_CLIENT_IP ip: " + ip);
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("HTTP_X_FORWARDED_FOR");
			System.out.println("HTTP_X_FORWARDED_FOR ip: " + ip);
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("X-Real-IP");
			System.out.println("X-Real-IP ip: " + ip);
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getRemoteAddr();
			System.out.println("getRemoteAddr ip: " + ip);
		}
		System.out.println("获取客户端ip: " + ip);
		return ip;
	}

	public static Map getParameterMap(HttpServletRequest request) {
		 Enumeration<String> headers = request.getParameterNames();
		 Map<String,String> map = new HashMap<>();
		 while(headers.hasMoreElements()){
			String name = headers.nextElement();
			map.put(name, request.getParameter(name));
		 }
		return map;
	}

	public static boolean jsonFormatCheck(String json) {
		try {
			if (json.trim().startsWith("{")) {
				JSON.parseObject(json);
				return true;
			} else if (json.trim().startsWith("[")) {
				JSON.parseArray(json);
				return true;
			}
			return false;
		} catch (Exception e) {
			return false;
		}
	}

	/**
	 * 隐藏用户手机号码和身份证号码以*代替 。
	 * 
	 * @param str
	 * @return
	 */
	public static String userSensitiveInfoHide(String str) {
		if (!StringUtils.isEmpty(str)) {
			if (str.length() == 11) {
				return str.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
			} else if (str.length() >= 18) {
				return str.replaceAll("(\\d{4})\\d{10}(\\w{4})", "$1**********$2");
			}
		}
		return str;
	}

	private static String serverIp = null;

	public static String getServerIp() {
		if (serverIp != null)
			return serverIp;
		InetAddress ip = null;
		try {
			boolean bFindIP = false;
			Enumeration<NetworkInterface> netInterfaces = (Enumeration<NetworkInterface>) NetworkInterface
					.getNetworkInterfaces();
			while (netInterfaces.hasMoreElements()) {
				if (bFindIP) {
					break;
				}
				NetworkInterface ni = (NetworkInterface) netInterfaces.nextElement();
				// 遍历所有ip
				Enumeration<InetAddress> ips = ni.getInetAddresses();
				while (ips.hasMoreElements()) {
					ip = (InetAddress) ips.nextElement();
					if (ip.isSiteLocalAddress() && !ip.isLoopbackAddress() // 127.开头的都是lookback地址
							&& ip.getHostAddress().indexOf(":") == -1) {
						bFindIP = true;
						break;
					}
				}

			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		if (null != ip) {
			serverIp = ip.getHostAddress();
		}
		return serverIp;
	}

	public static String delHTMLTag(String htmlStr) {
		String regEx_script = "<script[^>]*?>[\\s\\S]*?<\\/script>"; // 定义script的正则表达式
		String regEx_style = "<style[^>]*?>[\\s\\S]*?<\\/style>"; // 定义style的正则表达式
		String regEx_html = "<[^>]+>"; // 定义HTML标签的正则表达式
		Pattern p_script = Pattern.compile(regEx_script, Pattern.CASE_INSENSITIVE);
		Matcher m_script = p_script.matcher(htmlStr);
		htmlStr = m_script.replaceAll(""); // 过滤script标签

		Pattern p_style = Pattern.compile(regEx_style, Pattern.CASE_INSENSITIVE);
		Matcher m_style = p_style.matcher(htmlStr);
		htmlStr = m_style.replaceAll(""); // 过滤style标签

		Pattern p_html = Pattern.compile(regEx_html, Pattern.CASE_INSENSITIVE);
		Matcher m_html = p_html.matcher(htmlStr);
		htmlStr = m_html.replaceAll(""); // 过滤html标签
		return htmlStr.trim(); // 返回文本字符串
	}

	public static String repeat(String str, int count, String split) {
		if (count > 1) {
			StringBuilder result = new StringBuilder();
			for (int i = 0; i < count; i++) {
				if (i > 0)
					result.append(split);
				result.append(str);
			}
			return result.toString();
		}
		return str;
	}
	
	public static String[] genStrArray(String itemStr, int length) {
		if (length > 0) {
			String[] result = new String[length];
			for (int i = 0; i < length; i++) {
				result[i] = itemStr;
			}
			return result;
		}
		return new String[] {};
	}

	public static boolean strContains(String str, String[] array) {
		if (str != null && array != null && array.length > 0) {
			for (int i = 0; i < array.length; i++) {
				if (array[i].equals(str)) {
					return true;
				}
			}
		}
		return false;
	}
	
	///是否有重复元素 
	public static boolean hasRepeatElement(Object[] arr){
		return arr.length!=(ifRepeat(arr).length);
	}
	
	//需要传入一个Object数组，然后返回去重后的数组
	public static Object[] ifRepeat(Object[] arr){
	        //用来记录去除重复之后的数组长度和给临时数组作为下标索引
	        int t = 0;
	        //临时数组
	        Object[] tempArr = new Object[arr.length];
	        //遍历原数组
	        for(int i = 0; i < arr.length; i++){
	            //声明一个标记，并每次重置
	            boolean isTrue = true;
	            //内层循环将原数组的元素逐个对比
	            for(int j=i+1;j<arr.length;j++){
	                //如果发现有重复元素，改变标记状态并结束当次内层循环
	                if(arr[i]==arr[j]){
	                    isTrue = false;
	                    break;
	                }
	            }
	            //判断标记是否被改变，如果没被改变就是没有重复元素
	            if(isTrue){
	                //没有元素就将原数组的元素赋给临时数组
	                tempArr[t] = arr[i];
	                //走到这里证明当前元素没有重复，那么记录自增
	                t++;
	            }
	        }
	        //声明需要返回的数组，这个才是去重后的数组
	        Object[]  newArr = new Object[t];
	        //用arraycopy方法将刚才去重的数组拷贝到新数组并返回
	        System.arraycopy(tempArr,0,newArr,0,t);
	        return newArr;
	    }

	public static String StringFormat(String message, Object... args) {
		System.out.println("message 1="+message);
		final String placeHolder = "{}";
		if (args != null && args.length > 0) {
			for (Object it : args) {
				if (it != null) {
					if ((message.indexOf(placeHolder)) > -1) {
						int p = message.indexOf(placeHolder);
						String p1 = p == 0 ? "" : message.substring(0, p);
						String p2 = message.substring(p + placeHolder.length(), message.length());
						message = p1 + it + p2;
					}
				}

			}
		}
		System.out.println("message 2="+message);
		return message;
	}

	
	public static void main(String[] args) throws InterruptedException {
	System.out.println(isNumber(" "));
		
	}
}
