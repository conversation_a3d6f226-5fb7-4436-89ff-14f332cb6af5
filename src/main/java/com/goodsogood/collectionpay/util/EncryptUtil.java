package com.goodsogood.collectionpay.util;

import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import java.io.UnsupportedEncodingException;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class EncryptUtil {

    private static final String KEY_ALGORITHM = "RSA";
    private static final String ENCODING = "UTF-8";
    private static final String SIGNATURE_ALGORITHM = "SHA256withRSA";
    public static final String PUBLIC_KEY = "publicKey";
    public static final String PRIVATE_KEY = "privateKey";
    public static final int KEY_SIZE = 2048;


    public static String md5(String key) {
        char[] hexDigits = {
                '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'
        };
        byte[] btInput = key == null ? "".getBytes() : key.getBytes();
        // 获得MD5摘要算法的 MessageDigest 对象
        MessageDigest mdInst = null;
        try {
            mdInst = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException ignored) {
        }
        // 使用指定的字节更新摘要
        assert mdInst != null;
        mdInst.update(btInput);
        // 获得密文
        byte[] md = mdInst.digest();
        // 把密文转换成十六进制的字符串形式
        int j = md.length;
        char[] str = new char[j * 2];
        int k = 0;
        for (byte byte0 : md) {
            str[k++] = hexDigits[byte0 >>> 4 & 0xf];
            str[k++] = hexDigits[byte0 & 0xf];
        }
        return new String(str);

    }


    /**
     * 加密
     */
    public static byte[] desEncrypt(byte[] datasource, String key) {
        try {
            SecureRandom random = new SecureRandom();
            DESKeySpec desKey = new DESKeySpec(key.getBytes());
            //创建一个密匙工厂，然后用它把DESKeySpec转换成
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
            SecretKey securekey = keyFactory.generateSecret(desKey);
            //Cipher对象实际完成加密操作
            Cipher cipher = Cipher.getInstance("DES");
            //用密匙初始化Cipher对象
            cipher.init(Cipher.ENCRYPT_MODE, securekey, random);
            //现在，获取数据并加密
            //正式执行加密操作
            return cipher.doFinal(datasource);
        } catch (Throwable e) {
            e.printStackTrace();
            log.error("aes 加密失败", e);
        }
        return null;
    }

    /**
     * 解密
     */
    public static byte[] desDecrypt(byte[] src, String key) {
        // DES算法要求有一个可信任的随机数源

        SecureRandom random = new SecureRandom();
        // 创建一个DESKeySpec对象
        DESKeySpec desKey = null;
        try {
            desKey = new DESKeySpec(key.getBytes());

            // 创建一个密匙工厂
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
            // 将DESKeySpec对象转换成SecretKey对象
            SecretKey securekey = keyFactory.generateSecret(desKey);
            // Cipher对象实际完成解密操作
            Cipher cipher = Cipher.getInstance("DES");
            // 用密匙初始化Cipher对象
            cipher.init(Cipher.DECRYPT_MODE, securekey, random);
            // 真正开始解密操作
            return cipher.doFinal(src);
        } catch (Exception e) {
            log.error("des 解密失败", e);
        }

        return null;
    }


    /**
     * SHA256WithRSA 签名
     */
    public static byte[] sign256(String data, PrivateKey privateKey) throws NoSuchAlgorithmException, InvalidKeySpecException,
            InvalidKeyException, SignatureException, UnsupportedEncodingException {
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initSign(privateKey);
        signature.update(data.getBytes(ENCODING));
        return signature.sign();
    }

    /**
     * SHA256WithRSA 验证签名
     */
    public static boolean verify256(byte[] date, byte[] sign, PublicKey publicKey) {
        if (date == null || sign == null || publicKey == null) {
            return false;
        }
        try {
            Signature signetCheck = Signature.getInstance(SIGNATURE_ALGORITHM);
            signetCheck.initVerify(publicKey);
            signetCheck.update(date);
            return signetCheck.verify(sign);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 二进制数据编码为BASE64字符串
     */
    public static String encodeBase64(byte[] bytes) {
        return java.util.Base64.getEncoder().encodeToString(bytes);
    }

    /**
     * BASE64 解码
     */
    public static byte[] decodeBase64(byte[] bytes) {
        return java.util.Base64.getDecoder().decode(bytes);
    }

    /**
     * 生成密钥对
     */
    public static Map<String, byte[]> generateKeyBytes() {
        try {
            KeyPairGenerator keyPairGenerator = KeyPairGenerator
                    .getInstance(KEY_ALGORITHM);
            keyPairGenerator.initialize(KEY_SIZE);
            KeyPair keyPair = keyPairGenerator.generateKeyPair();
            RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();
            RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
            Map<String, byte[]> keyMap = new HashMap<String, byte[]>();
            keyMap.put(PUBLIC_KEY, publicKey.getEncoded());
            keyMap.put(PRIVATE_KEY, privateKey.getEncoded());
            return keyMap;
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 还原公钥
     */
    public static PublicKey restorePublicKey(byte[] keyBytes) {
        X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(keyBytes);
        try {
            KeyFactory factory = KeyFactory.getInstance(KEY_ALGORITHM);
            return factory.generatePublic(x509EncodedKeySpec);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 还原私钥
     */
    public static PrivateKey restorePrivateKey(byte[] keyBytes) {
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(
                keyBytes);
        try {
            KeyFactory factory = KeyFactory.getInstance(KEY_ALGORITHM);
            PrivateKey privateKey = factory
                    .generatePrivate(pkcs8EncodedKeySpec);
            return privateKey;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void main(String[] args) throws Exception {
//        //des 密钥
//         String DES_KEY  = "1234qwer";
//
//        //des 加密
//        byte[] desEncryptByte = EncryptUtil.desEncrypt("2222".getBytes(StandardCharsets.UTF_8), DES_KEY);
//        String base64_1 = java.util.Base64.getEncoder().encodeToString(desEncryptByte);
//        System.out.println(base64_1);
//
//        byte[] desDecryptByte =  java.util.Base64.getDecoder().decode(base64_1.getBytes(StandardCharsets.UTF_8));
//        //des 解密
//        desDecryptByte = EncryptUtil.desDecrypt(desDecryptByte,DES_KEY);
//        System.out.println(new String(desDecryptByte,StandardCharsets.UTF_8));
//
//
        // 公私钥对
        PublicKey publicKey = EncryptUtil.restorePublicKey(org.apache.commons.codec.binary.Base64.decodeBase64("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlOH9WtWBB5UQnBtizhZZvZqxDzR+I0rzVR9IXhXE/DkcS5Y5I2LtqBRBaKftLqnEcGjawX9rZZ/XRvO+Gxdbf2WPAJjpRRwDDbm2r/FMIYXuFGdzoryX6z7ohf6pa3PWtMKxwbANEtc1f/PPrpXp03ZCva8eVlvTeil71+PPoF23sgGiJnk0nZFO4Xfr0XrxuBPCNTmGGqpAyW1BIGMhBka8aXpKKZ8XtjF2y3dP17S8MLYRsuBPu/frbAtowK6EnbfJys3q/zJCLAGtR+3wv5rooirADF+2lodR9jgN2wN0ErWSZ6NbvNfy5Su9f6flHkKZiisvNyNXwfvsTlXeAQIDAQAB"));
        PrivateKey privateKey = EncryptUtil.restorePrivateKey(org.apache.commons.codec.binary.Base64.decodeBase64("MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCU4f1a1YEHlRCcG2LOFlm9mrEPNH4jSvNVH0heFcT8ORxLljkjYu2oFEFop+0uqcRwaNrBf2tln9dG874bF1t/ZY8AmOlFHAMNubav8Uwhhe4UZ3OivJfrPuiF/qlrc9a0wrHBsA0S1zV/88+ulenTdkK9rx5WW9N6KXvX48+gXbeyAaImeTSdkU7hd+vRevG4E8I1OYYaqkDJbUEgYyEGRrxpekopnxe2MXbLd0/XtLwwthGy4E+79+tsC2jAroSdt8nKzer/MkIsAa1H7fC/muiiKsAMX7aWh1H2OA3bA3QStZJno1u81/LlK71/p+UeQpmKKy83I1fB++xOVd4BAgMBAAECggEAcTksI9gauFl0uENbcL8uoDL0VEvnYY+uXh3cOzDzDZ2hFxq3eWHSuvAsjhqnFcWlzpbvRhhQ5Igaf6r9+DxbKRdjGWEwhY44BEgn1sX6z8O6u2mxVyF1p0HAM6bn0P/XWMB3zoTEbH+eACC64FrB6D8Xool62NIckdrag+SMEbgKdNRnSFztjRTyT5ASgMtv6YMi6EVkhGMv5vPYzRy0FQULXlpxS/jHD8Dv8MHmexgbbGTTpuzPpJKVfne7+qNTqhP02PUzbNQpY6xdjahUebqJUaQnjwfhieII8TGqXZDGJdzH4aF7XCi2aNRLXR3u2eARwP3suq32RWfClhjZAQKBgQDPJDP+ZmFpjEGHTSAkNvW5nBA0CMZ3h29jilnbb3UrBatJx7htNJ6RJebyZ/ovLyWAPosHGca/RqPODbdDVAqWfq0RmUbFMsGPiwrNcs7rl7e0JoG5jrUIlaOL0lkPV3nhj3VSceNn7NJm9rRfhqOA+QWtzTnOQxegYKw8T+E6PQKBgQC3//YX+UVKz41rguhpQMmCdajtCVCOe9mzsSBiJv+KvqZLP67hcbQ+hwumEy/BlpGVgJcHlD+p2IMSWb2dkE+zicIoTpGvht/cAj1MglUq8wyHA6+iij5cDtettxanpn1463GnRapvl5+LWSrAQZ6BR8Knr2JkXFz9jA55rpjjFQKBgGaqSOLUF3/atje7MC0C2HNwOE+x8bwYckrhrXunGGmEW8sro+rsY5MJOBf5FPUB5xJS7VFNo0X2WwWroqNKKh1BF8JbN8pRR8I/ZdFCD/CQt8ogM7emVW+1yuNoFCAYeR4knj+Ilu9RX08NA4U9FBi+U+Q6jWX/ttwUTgluhfD5AoGAOQFD7Ql9PkAvQC++cV1xEMoDG+zW2E3mlJU7wyzx3rpSKt4AADiJ7xVHtpwbH47Wa8r6/44eazB46yaCaF5dUvJLBNHSsN7IXBEVjK6UVEoYh3vM3j6WM08l4tvmWOpr1Rqci4Y513jSJQnrBMVPph3lYK4lfZJf/O13TpuZIkECgYAYjwXVfvK6S6xzI0hvqCPxipB81qhhCFUPBcL+7Uz/V0rfKQ+KUXDAhWUZf4AYrRYbYpMOuFmvQpAe2UDQRlfI8OMUpFNS+EtOJpeLoMS2mSp7eZY+0sfTZli7CJ7l/9HrZLjgGUjt84PQxVq+MCl2pSAbLIM5GNnVKu9mmpb2Nw=="));
        // Map<String, byte[]> keyMap = generateKeyBytes();
        //publicKey = restorePublicKey(keyMap.get(PUBLIC_KEY));
        //privateKey = restorePrivateKey(keyMap.get(PRIVATE_KEY));

        // System.out.println("PUBLIC_KEY : "+ Base64.encodeBase64String(keyMap.get(PUBLIC_KEY)));
        // System.out.println("PRIVATE_KEY : " +Base64.encodeBase64String(keyMap.get(PRIVATE_KEY)));

        System.out.println("siyao=" + EncryptUtil.encodeBase64(privateKey.getEncoded()));
        //待签名字符
        String src = "acq_charge=50&bank_type=ICBC&charset=UTF-8&fee_type=CNY&mch_id=***************&merchant_acc=***************&nonce_str=**************************&other_charge=50&pay_result=0&result_code=0&settlement_amount=900&sign_type=RSA_1_256&sp_charge=50&status=0&time_end=**************&total_fee=1000&transaction_id=2018071720551313212313132&version=2.0&wallet_id=**********";
        //签名
        byte[] sign256 = EncryptUtil.sign256(src, privateKey);
        String sign = EncryptUtil.encodeBase64(sign256);
        System.out.println("sign=" + sign);
//        //验签
//        boolean result = EncryptUtil.verify256(src, EncryptUtil.decodeBase64(sign.getBytes(StandardCharsets.UTF_8)), publicKey);
//        System.out.println(result);

    }
}
