package com.goodsogood.collectionpay.util;

import org.springframework.stereotype.Component;

import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * DES加解密工具
 * <AUTHOR>
 * @date 2019-06-28 9:56
 * @since 1.0.5
 *
 * <AUTHOR>
 * @date 2020-11-12
 * @since 3.0.1
 **/
@Component
public class DESCoderUtil {

    //用于desc加密补位,渠道前缀后面接渠道accessKey
    public static final String channel_prefix = "yuangwang_ppmd_pay";

    /**
     * 加密方法
     * content 加密文本
     * @return  re key 传递的加密key，  value 传递的密文
     */
    public static Map<String,String> encrypt(String content) throws Exception{
        Map<String,String> re = new HashMap<>();
        //生成随机hash
        Integer randomHash = Math.abs(DESCoderUtil.SDBMHash(DESCoderUtil.getRandomStr(16)));
        //根据渠道获得加盐Hash
        Integer keyHash = DESCoderUtil.findKey(randomHash);
        //根据加盐Hash生成key
        String key = DESCoderUtil.appedZero(String.valueOf(keyHash),8);
        String ciphertext = DESUtil.encryption(content, key);
        //根据加密报文和随机hash获取传递
        String transferKey = DESCoderUtil.findTransferKey(randomHash,ciphertext);
        //URL编码，编译特殊符号
        ciphertext = URLEncoder.encode(ciphertext, "utf-8");
        re.put("transferKey",transferKey);
        re.put("ciphertext",ciphertext);
        return re;
    }

    /**
     * 解密方法
     *  transferKey  传递的加密KEY
     *  content  密文
     */
    public static String decode(String transferKey,String content) throws Exception{
        //URL解码
        content = URLDecoder.decode(content, "utf-8");
        String realkey = DESCoderUtil.findRealKey(transferKey,content);
        String body = DESUtil.decryption(content, realkey);
        return body;
    }

    /**
     * 生成加密key
     * @param randomHash  随机Hash
     * @return	生成加密key
     */
    public static Integer findKey(Integer randomHash) throws Exception{
        //约定的偏移量(测试环境与正式环境不一样,由固守方提供)
        Integer salt = Math.abs(SDBMHash(channel_prefix));
        //使用随机生成的hash减去约定偏移量取绝对值，作为真正加密KEY
        Integer keyHash = Math.abs(randomHash-salt);
        return keyHash;
    }

    /**
     * 根据报文密文获取传递的加密key
     * @param randomHash    随机Hash
     * @param encrypt	    报文密文
     * @return	生成加密key
     */
    public static String findTransferKey(Integer randomHash,String encrypt) throws Exception{
        //hash密文
        Integer encryptHash =  Math.abs(SDBMHash(encrypt));
        //传递key为加密后报文
        Integer r = randomHash - encryptHash;
        return r.toString();
    }

    /**
     * 根据报文密文和传递key获取解密key
     * @param transferKey    传递Hash
     * @param encrypt	    报文密文
     * @return	生成加密key
     */
    public static String findRealKey(String transferKey,String encrypt) throws Exception{
        //hash密文
        Integer encryptHash =  Math.abs(SDBMHash(encrypt));
        //随机key
        Integer randomHash = Integer.valueOf(transferKey) + encryptHash;
        //约定的偏移量(测试环境与正式环境不一样,由固守方提供)
        Integer salt = Math.abs(SDBMHash(channel_prefix));
        //使用随机生成的hash减去约定偏移量取绝对值，作为真正加密KEY
        Integer keyHash = Math.abs(randomHash-salt);
        //根据加盐Hash生成key
        String key = appedZero(String.valueOf(keyHash),8);
        return key;
    }

    /**
     * SDBM算法
     */
    public static int SDBMHash(String str) {
        int hash = 0;
        for (int i = 0; i < str.length(); i++) {
            hash = str.charAt(i) + (hash << 6) + (hash << 16) - hash;
        }
        return (hash & 0x7FFFFFFF);
    }

    /**
     * 不足8位补零
     * @param str
     * @param length
     * @return
     */
    public static String appedZero(String str, int length) {
        return str.length() > length ? str.substring(0, length) : str.length() < length ? String.format("%" + length + "s", str).replace(" ", "0") : str;
    }

    /**
     * 默认8位补零
     * @param str
     * @return
     */
    public static String appedZero(String str){
        return appedZero(str, 8);
    }

    /**
     *  获取随机数字符串
     * @param length    获取字符串长度
     * @return          返回随机字符串
     */
    public static String getRandomStr(int length){
        String str="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random=new Random();
        StringBuilder sb = new StringBuilder();
        for(int i=0;i<length;i++){
            int number=random.nextInt(62);
            sb.append(str.charAt(number));
        }
        return sb.toString();
    }

    public static void main(String[] args) throws Exception {
        String decode = decode("885166514", "PCNZ4LK6%2FKHzhLctcY1CCMTHsGLZfydwqsZtQeVCk%2Frsz087tXHMEB7HamfiXC9MANfNkcPwj7BPi46bzsa4MNdJPLbLKASOP%2FhYp6v0QYsJJB9zqGjRq1LEHendusTx5ODipOHxdrm0qb%2FmXNY822LjuLMFdOMOrCR5hpQ0RqGWHLOm5dgcnCZWQrUtVMK20s71y0%2Foe%2FHUYTBG25Fe%2B%2FRAyvE7XE2xwmq%2BYhUvwtXOiDOefveT%2BdnIJP9ORolPBKyhCXFeplq9eRwhhdPyR29iqUbwqdpB0uLkuktpXvkZmP0Nzr%2BKO35l94utQJmiPdYItRff34PLxz7I0%2FyGVOm2P0NfFVoggXyA5QVcXifX50xwPBQ2houw5CWpfDeJjz1fXzwx%2FeqY12eGz4yuK4wVEgxa1TmYTx1g666dRPr%2B8iD8XRerDpBzB%2BWxNjm4NSNC9uiFTp2mUyG5%2FT253kts6naFeRtOGW247vs3cFAtLhiRTJVybbvYBl4531PWvht9niERXuPQMrDErbnncQ%3D%3D");
        System.out.println(decode);


    }
}
