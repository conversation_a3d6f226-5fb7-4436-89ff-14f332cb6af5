package com.goodsogood.collectionpay.util;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import com.alibaba.fastjson.JSON;

/**
 * key,value encoded的map
 * @Description
 * <AUTHOR>
 * @time 2017年7月21日下午2:05:33
 */
public class URLEncodedMap<K,V>  extends HashMap<K,V> {
 
	private static final long serialVersionUID = 8621481806550617255L;
	private String enc="utf-8";
	
	/**
	 * @param enc the enc to set
	 */
	public void setEnc(String enc) {
		this.enc = enc;
	}
	
	@Override
	public V put(K key, V value) {
		if(key instanceof java.lang.String){
			try {
				key = (K) URLEncoder.encode(key.toString(), enc);
			} catch (UnsupportedEncodingException e) {
				e.printStackTrace();
			}
		}
		if(value instanceof java.lang.String){
			try {
				value = (V) URLEncoder.encode(value.toString(), enc);
			} catch (UnsupportedEncodingException e) {
				e.printStackTrace();
			}
		}
	  return super.put(key,value);
	}
	
	
	
	public URLEncodedMap<K,V> convert(Map map){
		URLEncodedMap<K,V> newMap =new URLEncodedMap<K,V>();
		Set<Map.Entry<K, V>> set = map.entrySet();
		for(Map.Entry<K, V> entry :set){
			K key = entry.getKey();
			V value = entry.getValue() ;
			if(value instanceof java.lang.String){
				try {
					value = (V) URLEncoder.encode(value.toString(), enc);
					newMap.put(key, value);
				} catch (UnsupportedEncodingException e) {
					e.printStackTrace();
				}
			}
		}
		return newMap ; 
	}
	
	public static void main(String[] args) {
		Map map =new HashMap<>() ;
		map.put("url", "21321354L>OP&*^&^%#@##@=789");
		map.put("name", "张三");
		map.put("age", "18");
		
		
		URLEncodedMap map2 =new URLEncodedMap<>() ;
		System.out.println(JSON.toJSONString(map2.convert(map)));
		
	}

}
