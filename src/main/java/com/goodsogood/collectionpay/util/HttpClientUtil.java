package com.goodsogood.collectionpay.util;

import com.alibaba.fastjson.JSON;
import com.goodsogood.collectionpay.consts.LogConfiger;
import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

/* 
 * 利用HttpClient进行post请求的工具类 
 */
public class HttpClientUtil {
	
	private static final Logger logger = LoggerFactory.getLogger(LogConfiger.APP);
	

	private HttpClientUtil() {
	}

	private static CloseableHttpClient httpClient;

	private static final int MAX_TOTAL = 200;
	private static final int MAX_PERROUTE = 1000;

	static {
		PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
		cm.setMaxTotal(MAX_TOTAL);
		cm.setDefaultMaxPerRoute(MAX_PERROUTE);
		httpClient = HttpClients.custom().setConnectionManager(cm).build();
	}


	public static String doPostWithXml(String url, String body) throws IOException {
		HttpPost httpPost = new HttpPost(url);
		RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(10000).setConnectTimeout(10000).build();// 设置请求和传输超时时间
		httpPost.setConfig(requestConfig);
		StringEntity entity = new StringEntity(body, Consts.UTF_8);
		httpPost.setEntity(entity);
		httpPost.setHeader("Content-Type", "application/xml; charset=utf-8");
		HttpResponse response = httpClient.execute(httpPost);
		HttpEntity resEntity = response.getEntity();
		if (resEntity != null) {
			return EntityUtils.toString(resEntity, Consts.UTF_8);
		}
		httpPost.reset();
		return null;
	}

	public static String doPostWithXmlMock(String url, String body) throws IOException {
		return ResourceUtil.getResource("/com/goodsogood/fastpay/resp/2.xml");
	}

	public static String doPost(String url, Map<String, Object> map, Map<String, String> headerMap, String charset) throws Exception {
		HttpPost httpPost = null;
		String body = null;
		try {
			httpPost = new HttpPost(url);
			// 设置参数
			List<NameValuePair> list = new ArrayList<NameValuePair>();
			
			if(map!=null && !map.isEmpty()){
				Iterator iterator = map.entrySet().iterator();
				while (iterator.hasNext()) {
					Entry<String, Object> elem = (Entry<String, Object>) iterator.next();
					if(elem != null){
						list.add(new BasicNameValuePair(elem.getKey(), elem.getValue().toString()));
					}
					
				}
			}
			
			if(headerMap!=null){
				Iterator headerIt = headerMap.entrySet().iterator();
				while (headerIt.hasNext()) {
					Entry<String, String> elem = (Entry<String, String>) headerIt.next();
					httpPost.addHeader(elem.getKey(), elem.getValue());
				}
			}
			
			if (list.size() > 0) {
				UrlEncodedFormEntity entity = new UrlEncodedFormEntity(list, charset);
				httpPost.setEntity(entity);
			}
			HttpResponse response = httpClient.execute(httpPost);
			logger.info("Response StatusLine={}",response.getStatusLine());
			
			if (response != null) {
				HttpEntity resEntity = response.getEntity();
				logger.info("Response body ContentLength={}",resEntity.getContentLength());
				if (resEntity != null) {
					body = EntityUtils.toString(resEntity, charset);
				}
			}
			httpPost.completed();
		} catch (Exception ex) {
			throw ex ; 
		} finally {
			httpPost.reset();
		}
		return body;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static String sendGet(String url, Map<String, String> map) throws RuntimeException {
		HttpGet httpGet = null;
		try {
			// 设置参数
			List<NameValuePair> list = new ArrayList<NameValuePair>();
			if (map != null) {
				Iterator iterator = map.entrySet().iterator();
				while (iterator.hasNext()) {
					Entry<String, String> elem = (Entry<String, String>) iterator.next();
					list.add(new BasicNameValuePair(elem.getKey(), elem.getValue()));
				}
			}
			String str = EntityUtils.toString(new UrlEncodedFormEntity(list, Consts.UTF_8));
			httpGet = new HttpGet(url + "?" + str);
			RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(10000).setConnectTimeout(10000)
					.build();// 设置请求和传输超时时间
			httpGet.setConfig(requestConfig);
			HttpResponse response = httpClient.execute(httpGet);
			if (response != null) {
				HttpEntity resEntity = response.getEntity();
				if (resEntity != null) {
					return EntityUtils.toString(resEntity, Consts.UTF_8);
				}
			}
		} catch (Exception ex) {
			throw new RuntimeException(ex);
		} finally {
			httpGet.reset();
		}
		return "";
	}
	
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static String sendGet(String url, Map<String, Object> map,Map<String, String> header) throws RuntimeException {
		HttpGet httpGet = null;
		try {
			// 设置参数
			List<NameValuePair> list = new ArrayList<NameValuePair>();
			if (map != null && !map.isEmpty()) {
				Iterator iterator = map.entrySet().iterator();
				while (iterator.hasNext()) {
					Entry<String, Object> elem = (Entry<String, Object>) iterator.next();
					if(elem!=null)
					list.add(new BasicNameValuePair(elem.getKey(), elem.getValue().toString()));
				}
			}
			String str = EntityUtils.toString(new UrlEncodedFormEntity(list, Consts.UTF_8));
			httpGet = new HttpGet(url + "?" + str);
			logger.info("sendGet full url={}",url + "?" + str);
			Iterator headerIt = header.entrySet().iterator();
			while (headerIt.hasNext()) {
				Entry<String, String> elem = (Entry<String, String>) headerIt.next();
				httpGet.addHeader(elem.getKey(), elem.getValue());
			}
		
			RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(10000).setConnectTimeout(10000)
					.build();// 设置请求和传输超时时间
			httpGet.setConfig(requestConfig);
			HttpResponse response = httpClient.execute(httpGet);
			logger.info("HttpResponse Status={}",response.getStatusLine());
			if (response != null) {
				HttpEntity resEntity = response.getEntity();
				if (resEntity != null) {
					return EntityUtils.toString(resEntity, Consts.UTF_8);
				}
			}
		} catch (Exception ex) {
			throw new RuntimeException(ex);
		} finally {
			httpGet.reset();
		}
		return "";
	}

	/**
	 * 创建网关上的代理任务
	 * 
	 * @return
	 */
	public static String createProxyTask(String url, Map<String, Object> paraMap, Map<String, String> headerMap) {
		HttpPost httpPost = null;
		try {
			httpPost = new HttpPost(url);
			StringEntity entity = new StringEntity(JSON.toJSONString(paraMap, true), ContentType.APPLICATION_JSON);
			httpPost.setEntity(entity);
			HttpResponse response = httpClient.execute(httpPost);
			if (response != null) {
				HttpEntity resEntity = response.getEntity();
				if (resEntity != null) {
					return EntityUtils.toString(resEntity, Consts.UTF_8);
				}
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			httpPost.reset();
		}
		return null;
	}

	public static String postWithJson(String url_param, String param) {
		logger.debug("json:{}", param);
		StringBuilder result = new StringBuilder();// 返回的结果
		BufferedReader in = null;// 读取响应输入流
//        PrintWriter out = null;
		try {
			// 创建URL对象
			URL url = new URL(url_param);
			// 打开URL连接
			java.net.HttpURLConnection httpConn = (java.net.HttpURLConnection) url
					.openConnection();
			// 设置属性
			httpConn.setRequestProperty("Content-Type",
					"application/json");
			// 设置POST方式
			httpConn.setDoInput(true);
			httpConn.setDoOutput(true);
			httpConn.setRequestMethod("POST");
			httpConn.setUseCaches(false);
			httpConn.setInstanceFollowRedirects(true);
			// 获取HttpURLConnection对象对应的输出流
			DataOutputStream out = new DataOutputStream(httpConn.getOutputStream());
			out.write(param.getBytes(StandardCharsets.UTF_8));
			// flush输出流的缓冲
			out.flush();
			out.close();
			// 定义BufferedReader输入流来读取URL的响应，设置编码方式
			in = new BufferedReader(new InputStreamReader(httpConn
					.getInputStream(), StandardCharsets.UTF_8));
			String line;
			// 读取返回的内容
			while ((line = in.readLine()) != null) {
				result.append(line);
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (in != null) {
					in.close();
				}
			} catch (IOException ex) {
				ex.printStackTrace();
			}
		}
		return result.toString();
	}

	public static String postWithStr(String url_param, String param) {
		logger.debug("json:{}", param);
		StringBuilder result = new StringBuilder();// 返回的结果
		BufferedReader in = null;// 读取响应输入流
		try {
			// 创建URL对象
			URL url = new URL(url_param);
			// 打开URL连接
			java.net.HttpURLConnection httpConn = (java.net.HttpURLConnection) url
					.openConnection();
			// 设置属性
			httpConn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
			// 设置POST方式
			httpConn.setDoInput(true);
			httpConn.setDoOutput(true);
			httpConn.setRequestMethod("POST");
			httpConn.setUseCaches(false);
			httpConn.setInstanceFollowRedirects(true);
			// 获取HttpURLConnection对象对应的输出流
			DataOutputStream out = new DataOutputStream(httpConn.getOutputStream());
			out.write(param.getBytes(StandardCharsets.UTF_8));
			// flush输出流的缓冲
			out.flush();
			out.close();
			//定义BufferedReader输入流来读取URL的响应，设置编码方式
			in = new BufferedReader(new InputStreamReader(httpConn
					.getInputStream(), StandardCharsets.UTF_8));
			String line;
			// 读取返回的内容
			while ((line = in.readLine()) != null) {
				result.append(line);
			}
		} catch (Exception e) {
			logger.error("postWithStr发生异常",e);
		} finally {
			try {
				if (in != null) {
					in.close();
				}
			} catch (IOException ex) {
				logger.error("postWithStr发生异常1",ex);
			}
		}
		return result.toString();
	}

}