package com.goodsogood.collectionpay.util;

import com.alibaba.fastjson.JSON;
import com.goodsogood.collectionpay.consts.SignType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.util.StringUtils;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.SignatureException;
import java.security.spec.InvalidKeySpecException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class SignUtil {
    public static String getSignValue(Map<String, Object> map, SignType signType, String encryptKey) {
        return getSignValue(map, signType, null, encryptKey);
    }


    public static String getSignValue(Map<String, Object> map, SignType signType, String key, String encryptKey) {

        String signValues = getSortSignString(map);

        if (!StringUtils.isEmpty(key) && !StringUtils.isEmpty(encryptKey)) {
            signValues = signValues.concat("&").concat(key).concat("=").concat(encryptKey);
        }


        if (Objects.isNull(signType)) {
            signType = SignType.MD5;
        }

        if (signType.equals(SignType.MD5)) {
            return EncryptUtil.md5(signValues);
        }

        if (signType.equals(SignType.RSA256)) {
            PrivateKey privateKey = EncryptUtil.restorePrivateKey(Base64.decodeBase64(encryptKey));

            try {
                return EncryptUtil.encodeBase64(byte2Hex(EncryptUtil.sign256(signValues, privateKey)).getBytes(StandardCharsets.UTF_8));
            } catch (NoSuchAlgorithmException | InvalidKeySpecException | InvalidKeyException | SignatureException | UnsupportedEncodingException e) {
                log.error("RSA256 签名失败", e);
            }
        }


        return null;
    }

    public static String getSortSignString(Map<String, Object> map) {
        TreeMap<String, Object> sortMap = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
        sortMap.putAll(map);
        String signValues = sortMap.entrySet().stream()
                .filter(entry -> Objects.nonNull(entry.getValue()))
                .map(entry -> String.join("=", entry.getKey(),
                        (entry.getValue().getClass().isArray() || entry instanceof List) ?
                                JSON.toJSONString(entry.getValue()) : entry.getValue().toString()))
                .collect(Collectors.joining("&"));
        log.info("sign signKey:{}", signValues);
        return signValues;

    }

    public static void main(String[] args) {
        Map<String, Object> mapStr = new HashMap<>();
        mapStr.put("merch_serial", "MA0792008310002072");
        mapStr.put("cust_merch_id", "CMB001");
        mapStr.put("memo1", "330111199909099999");
        mapStr.put("cust_name", "张三");
        mapStr.put("cust_phone", "13000000000");
        mapStr.put("fee_amt", "1.00");
        mapStr.put("trn_amt", "0.00");
        mapStr.put("status", "N");
        mapStr.put("cust_mail", "<EMAIL>");

        System.out.println(getSortSignString(mapStr));
    }


    public static String getSignValue(Map<String, Object> map, String key, String val) {
        return getSignValue(map, SignType.MD5, key, val);
    }


    /**
     * 16进制处理
     */
    private static String byte2Hex(byte[] bytes) {
        StringBuilder stringBuffer = new StringBuilder();
        String temp = null;
        for (int i = 0; i < bytes.length; i++) {
            temp = Integer.toHexString(bytes[i] & 0xFF);
            if (temp.length() == 1) {
                //1得到一位的进行补0操作
                stringBuffer.append("0");
            }
            stringBuffer.append(temp);
        }
        return stringBuffer.toString();
    }


    /*
     * 十六进制转byte[]数组
     */
    public static byte[] hexStr2bytes(String hexStr) {
        if (StringUtils.isEmpty(hexStr)) {
            return null;
        }
        if (hexStr.length() % 2 != 0) {//长度为单数
            hexStr = "0" + hexStr;//前面补0
        }
        char[] chars = hexStr.toCharArray();
        int len = chars.length / 2;
        byte[] bytes = new byte[len];
        for (int i = 0; i < len; i++) {
            int x = i * 2;
            bytes[i] = (byte) Integer.parseInt(String.valueOf(new char[]{chars[x], chars[x + 1]}), 16);
        }
        return bytes;
    }

}
