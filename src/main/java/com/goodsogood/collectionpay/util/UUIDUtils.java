package com.goodsogood.collectionpay.util;

import java.util.UUID;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 30/10/2016.
 */
public class UUIDUtils {
    /**
     * 32位UUID
     */
    public static String uuid32() {
        // 创建 GUID 对象
        UUID uuid = UUID.randomUUID();
        // 得到对象产生的ID
        String a = uuid.toString();
        // 转换为大写
        a = a.toUpperCase();
        return a.replaceAll("-", "");
    }

    /**
     * 32位UUID
     */
    public static String uuid() {
        // 创建 GUID 对象
        UUID uuid = UUID.randomUUID();
        // 得到对象产生的ID
        return uuid.toString();
    }
}
