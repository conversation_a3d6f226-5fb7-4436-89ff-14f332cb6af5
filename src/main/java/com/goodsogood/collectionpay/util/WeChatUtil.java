package com.goodsogood.collectionpay.util;

import org.apache.commons.lang3.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * 日期Util类
 */
public class WeChatUtil {
    static final Map<String, String> map = new HashMap<>();
    private static final String hexDigits[] = {"0", "1", "2", "3", "4", "5",
            "6", "7", "8", "9", "a", "b", "c", "d", "e", "f"};

    static {
        map.put("%3B", ";");
        map.put("%2F", "/");
        map.put("%3F", "?");
        map.put("%3A", ":");
        map.put("%40", "@");
        map.put("%26", "&");
        map.put("%3D", "=");
        map.put("%2B", "+");
        map.put("%24", "$");
        map.put("%2C", ",");
        map.put("%23", "#");
//        map.put("-", "-");
//        map.put("_", "_");
//        map.put(".", ".");
        map.put("%21", "!");
        map.put("%7E", "~");
//        map.put("*", "*");
        map.put("%27", "'");
        map.put("%28", "(");
        map.put("%29", ")");
    }

    @SuppressWarnings("rawtypes")
	public static String createSign(SortedMap<Object, Object> parameters, String key) {
        StringBuffer sb = new StringBuffer();
        Set es = parameters.entrySet();//所有参与传参的参数按照accsii排序（升序）
        Iterator it = es.iterator();
        while (it.hasNext()) {
            Map.Entry entry = (Map.Entry) it.next();
            String k = (String) entry.getKey();
            Object v = entry.getValue();
            if (null != v && !"" .equals(v)
                    && !"sign" .equals(k) && !"key" .equals(k)) {
                sb.append(k + "=" + v + "&");
            }
        }
        sb.append("key=" + key);
        String sign = MD5Encode(sb.toString(), "utf-8");
        return sign.toUpperCase();
    }

    public static final String javascriptURIFormat(String str) {
        if (StringUtils.isEmpty(str)) {
            return str;
        }
        Iterator<String> i = map.keySet().iterator();
        while (i.hasNext()) {
            String k = i.next();
            str = str.replaceAll(k, map.get(k));
        }
        str = str.replaceAll("\\+", "%20");
        return str;
    }

    public static String createSign(Map<String, String> parameters) {
        StringBuffer sb = new StringBuffer();

        SortedSet<String> keys = new TreeSet<>(parameters.keySet());

        try {
            for (String key : keys) {
                String value = parameters.get(key);
                if (sb.length() == 0) {
                    sb.append(key + "=" + javascriptURIFormat(URLEncoder.encode(value, "utf-8")));
                } else {
                    sb.append("&" + key + "=" + javascriptURIFormat(URLEncoder.encode(value, "utf-8")));
                }
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

//        System.out.println(sb);

        String sign = MD5Encode(sb.toString(), "utf-8");
//        System.out.println(sign);
        return sign;
    }

    private static String byteArrayToHexString(byte b[]) {
        StringBuffer resultSb = new StringBuffer();
        for (int i = 0; i < b.length; i++)
            resultSb.append(byteToHexString(b[i]));

        return resultSb.toString();
    }

    private static String byteToHexString(byte b) {
        int n = b;
        if (n < 0)
            n += 256;
        int d1 = n / 16;
        int d2 = n % 16;
        return hexDigits[d1] + hexDigits[d2];
    }

    public static String MD5Encode(String origin, String charsetname) {
        String resultString = null;
        try {
            resultString = new String(origin);
            MessageDigest md = MessageDigest.getInstance("MD5");
            if (charsetname == null || "" .equals(charsetname))
                resultString = byteArrayToHexString(md.digest(resultString
                        .getBytes()));
            else
                resultString = byteArrayToHexString(md.digest(resultString
                        .getBytes(charsetname)));
        } catch (Exception exception) {
        }
        return resultString;
    }

    public static Map<String, String> signJSAPITicket(String jsapi_ticket, String url) {
        Map<String, String> ret = new HashMap<String, String>();
        String nonce_str = create_nonce_str();
        String timestamp = create_timestamp();
        String string1;
        String signature = "";

        //注意这里参数名必须全部小写，且必须有序
        string1 = "jsapi_ticket=" + jsapi_ticket +
                "&noncestr=" + nonce_str +
                "&timestamp=" + timestamp +
                "&url=" + url;
        System.out.println(string1);

        try {
            MessageDigest crypt = MessageDigest.getInstance("SHA-1");
            crypt.reset();
            crypt.update(string1.getBytes("UTF-8"));
            signature = byteToHex(crypt.digest());
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        ret.put("url", url);
        ret.put("jsapi_ticket", jsapi_ticket);
        ret.put("nonceStr", nonce_str);
        ret.put("timestamp", timestamp);
        ret.put("signature", signature);

        return ret;
    }

    private static String byteToHex(final byte[] hash) {
        Formatter formatter = new Formatter();
        for (byte b : hash) {
            formatter.format("%02x", b);
        }
        String result = formatter.toString();
        formatter.close();
        return result;
    }

    private static String create_nonce_str() {
        return UUID.randomUUID().toString();
    }

    private static String create_timestamp() {
        return Long.toString(System.currentTimeMillis() / 1000);
    }
}