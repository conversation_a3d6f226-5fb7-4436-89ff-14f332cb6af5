package com.goodsogood.collectionpay.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import com.goodsogood.collectionpay.annotation.Clear;
import com.goodsogood.collectionpay.annotation.ClearAll;
import com.goodsogood.collectionpay.consts.LogConfiger;

public class MethodUtil {
	private static final Logger logger = LoggerFactory.getLogger(LogConfiger.APP);
	
	/**
	 * 是否跳过当前的拦截器检查 
	 * @return true:跳过
	 */
	public static boolean isSkipInterceptor(HandlerMethod handlerMethod,Class<? extends HandlerInterceptorAdapter > currInterceptorClazz){
		ClearAll ClearAll = handlerMethod.getBean().getClass().getAnnotation(ClearAll.class);
		if(ClearAll != null){
			logger.info("class上有注解@ClearAll ,跳过 Interceptor:{}",currInterceptorClazz.getName());
			return true ; 
		}
		ClearAll = handlerMethod.getMethodAnnotation(ClearAll.class);
		if(ClearAll != null){
			logger.info("Method上有注解@ClearAll,跳过 Interceptor:{}",currInterceptorClazz.getName());
			return true ; 
		}
		Clear ClearFlag = handlerMethod.getMethodAnnotation(Clear.class);
		if(ClearFlag!=null){
			Class<? extends HandlerInterceptorAdapter>[] interceptors = ClearFlag.value();
			for(Class<? extends HandlerInterceptorAdapter> clazz : interceptors){
				if(clazz.getName().equals(currInterceptorClazz.getName())){
					return true ; 
				}
			}
		}
		return false ; 
	}

}
