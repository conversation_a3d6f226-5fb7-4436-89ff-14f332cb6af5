package com.goodsogood.collectionpay.util;

import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import java.io.File;
import java.io.FileOutputStream;
import java.util.List;
import java.util.Map;

/**
 * Excel 导出
 * 
 * @Description
 * <AUTHOR>
 * @time 2018年10月23日上午9:36:13
 */
public class ExcelUtil {

	private ExcelUtil() {
	}
	
	public static void appendToExcel(SXSSFWorkbook wb,List<Map<String, Object>> records){
		SXSSFSheet xssfSheet = wb.getSheetAt(0);
		CellStyle cellStyle = wb.createCellStyle();
		short textFormat = wb.createDataFormat().getFormat("#,##0.00");//保留两位小数且不用科学计数法
		cellStyle.setDataFormat(textFormat);
		
		for (int i = 0; i < records.size(); i++) {
			SXSSFRow itemRow = xssfSheet.createRow((xssfSheet.getLastRowNum() + 1));
			Map<String, Object> record = records.get(i);
			record.forEach((k, v) -> {
				SXSSFCell itemc1 = itemRow.createCell(itemRow.getLastCellNum() <0 ? 0 :itemRow.getLastCellNum());
				
				/*if(v!=null && v instanceof java.lang.Double){
					java.lang.Double doubleVal = (Double) v; 
					System.out.println("设置单元格格式为文本 v="+String.valueOf(v));
					itemc1.setCellStyle(cellStyle);
					itemc1.setCellType(CellType.STRING);
					itemc1.setCellValue(doubleVal);
				}else*/
				{
					itemc1.setCellValue(v.toString());
				}
			
			});
		}
	}
	
	public static void writeExcelFile(String file,SXSSFWorkbook wb) throws Exception{
		wb.write(new FileOutputStream(new File(file)));
		wb.close();
	}

	public static SXSSFWorkbook generateExcel(String title, List<Map<String, Object>> records) throws Exception {

		SXSSFWorkbook  xssfWorkbook = new SXSSFWorkbook(300);
		SXSSFSheet xssfSheet = xssfWorkbook.createSheet();
		SXSSFRow titleRow = xssfSheet.createRow(0);

        String[] titleColumns = title.split(",");

		CellStyle titleStyle = xssfWorkbook.createCellStyle();  
		Font f  = xssfWorkbook.createFont();      
		f.setFontHeightInPoints((short) 15);// 字号   
		f.setBold(true);// 加粗   
		titleStyle.setFont(f);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);// 左右居中
		titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);// 上下居中 
		  
		for (int i = 0; i < titleColumns.length; i++) {
			SXSSFCell c1 = titleRow.createCell(i);
			c1.setCellStyle(titleStyle);
			c1.setCellValue(titleColumns[i]);
		}
		//CellStyle cellStyle = xssfWorkbook.createCellStyle();
		//short textFormat = xssfWorkbook.createDataFormat().getFormat("#,##0.00");//保留两位小数且不用科学计数法
		//cellStyle.setDataFormat(textFormat);
		
		for (int i = 0; i < records.size(); i++) {
			SXSSFRow itemRow = xssfSheet.createRow((i + 1));
			Map<String, Object> record = records.get(i);
			record.forEach((k, v) -> {
				SXSSFCell itemc1 = itemRow.createCell(itemRow.getLastCellNum() <0 ? 0 :itemRow.getLastCellNum());
					itemc1.setCellValue(v.toString());
			});
		}
		return xssfWorkbook;
	}

}
