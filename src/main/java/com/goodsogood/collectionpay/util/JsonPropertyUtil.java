package com.goodsogood.collectionpay.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/***
 * 
 * @Description
 * <AUTHOR>
 * @time 2018年7月18日上午11:27:00
 */
public class JsonPropertyUtil {
	
	private static Pattern pattern = Pattern.compile("\"(.*?)\"");
	
	
	/**
	 * 清除json串某些字段的值,防止敏感信息打印到日志中..
	 * @param jsonStr
	 * @param property
	 * @return
	 */
	public static String removeProperties(String jsonStr, String... property) {
		/// String json
		/// ="{\"code\":0,\"data\":{\"appId\":\"8FA5649750B64E3694D2DB7D7D538776\",\"appPrivateKey\":\"MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAOFxTXLyNQcmb4/5SXCnnjrcVtNp7x9wYZEildALCkb9l5J/8j3w1wDhWlj4edpjS6zABy0PxFtNYEHqJjBehYHjXqkZZ/PQ2keuQTfe8co8qWU3zC5YJx6Q12E43LPRroMSP1z6AIQ8Jwg+0PMAesBbClol6ihaV7IXMC+iG4PnAgMBAAECgYEAxwRd6c52D6TXsdLRCVl/rtia8bG//hIYn6ZXeLx2SytNpm+66wF03HsUZhycfbVQteUjsJ0Nq8hH5IiPQ/39fn8ilH3eLK8pOtyjga7x5zPmfWLr+//IAWvyRHM7626onjnzCsEI64z9hG1pC0zdorIngpGWaASCrZqwQtkSnPkCQQD7SYZyuctSWZkzZRm5nwa+mnVSb7hqXSNlLOi6CyVQ6sSHrAt5/kfMtSxY/OcRqISDvDUFKjGI2EwHnDVhXePrAkEA5auxVT8qxqDQlBRy/GiStrPIp42bncvjKVlBULJZgQH9P3L3Esc4HNY7BOpODu7EZJuQQxh/lilkauhbbzws9QJAPKeLr2ePX0pHQ3OU2Qw8qYdI6GQVJH9kysgjSLzl/fkdBQ6PGpw5In1cvgIVmFqP2eh099EucbSb7X5UGDN8jwJBAL1/zo+h0O+WnMKxpLZyqispZ/QnuTCXm9TwGh41Kfhx5bzGZOoPilLwyD2j/cgAZfcFDCDwQ8VoXbx//4FmS0kCQQDF047x2Uc40zzNSvHOdr9FWBEYRiWH+PNXpQUAdFYFEMGW4dyRqJ1mKVJA0oRyp8u9RjCeQPMyboZa69zAUQ7+\",\"appPublicKey\":\"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDhcU1y8jUHJm+P+Ulwp5463FbTae8fcGGRIpXQCwpG/ZeSf/I98NcA4VpY+HnaY0uswActD8RbTWBB6iYwXoWB416pGWfz0NpHrkE33vHKPKllN8wuWCcekNdhONyz0a6DEj9c+gCEPCcIPtDzAHrAWwpaJeooWleyFzAvohuD5wIDAQAB\",\"appSecretKey\":\"GS20170627100319403eIhcrj69\",\"channelAccountEnabled\":0,\"createTime\":**********,\"domainWhiteList\":\"\",\"ipWhiteList\":\"************,*************,*************,************,************,**************,*************,************,*************,**************,************,**************,************,*************,************,************,**************,*************,************,************,************,************\",\"lockStatus\":\"0\",\"merchantId\":\"91D730C7E15547AD87D8FA7AC3F42764\",\"merchantName\":\"麦卡福利社\",\"mobilePhone\":\"***********\",\"needUserAuth\":\"0\",\"push\":1,\"pushClientId\":\"100002\",\"rebate\":0,\"tradeNotifyUrl\":\"merchant.goodsogood.com\",\"updateTime\":**********},\"message\":\"success\",\"timestamp\":*************}";

		try {
			Matcher m = pattern.matcher(jsonStr);
			boolean removeThis = false;
			lable :	while (m.find()) {
				if (removeThis) {
					removeThis = false;
					jsonStr = jsonStr.replace(m.group(), "\"*******\"");
					continue;
				}

				 for (String str : property) {
					if (m.group().equals("\"" + str + "\"")) {
						removeThis = true;
						continue lable;
					}
				}
			}

		} catch (Exception e) {
		}

		return jsonStr;
	}
	
	public static void main(String[] args) {
		String json ="{\"code\":0,\"data\":{\"appId\":\"8FA5649750B64E3694D2DB7D7D538776\",\"appPrivateKey\":\"MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAOFxTXLyNQcmb4/5SXCnnjrcVtNp7x9wYZEildALCkb9l5J/8j3w1wDhWlj4edpjS6zABy0PxFtNYEHqJjBehYHjXqkZZ/PQ2keuQTfe8co8qWU3zC5YJx6Q12E43LPRroMSP1z6AIQ8Jwg+0PMAesBbClol6ihaV7IXMC+iG4PnAgMBAAECgYEAxwRd6c52D6TXsdLRCVl/rtia8bG//hIYn6ZXeLx2SytNpm+66wF03HsUZhycfbVQteUjsJ0Nq8hH5IiPQ/39fn8ilH3eLK8pOtyjga7x5zPmfWLr+//IAWvyRHM7626onjnzCsEI64z9hG1pC0zdorIngpGWaASCrZqwQtkSnPkCQQD7SYZyuctSWZkzZRm5nwa+mnVSb7hqXSNlLOi6CyVQ6sSHrAt5/kfMtSxY/OcRqISDvDUFKjGI2EwHnDVhXePrAkEA5auxVT8qxqDQlBRy/GiStrPIp42bncvjKVlBULJZgQH9P3L3Esc4HNY7BOpODu7EZJuQQxh/lilkauhbbzws9QJAPKeLr2ePX0pHQ3OU2Qw8qYdI6GQVJH9kysgjSLzl/fkdBQ6PGpw5In1cvgIVmFqP2eh099EucbSb7X5UGDN8jwJBAL1/zo+h0O+WnMKxpLZyqispZ/QnuTCXm9TwGh41Kfhx5bzGZOoPilLwyD2j/cgAZfcFDCDwQ8VoXbx//4FmS0kCQQDF047x2Uc40zzNSvHOdr9FWBEYRiWH+PNXpQUAdFYFEMGW4dyRqJ1mKVJA0oRyp8u9RjCeQPMyboZa69zAUQ7+\",\"appPublicKey\":\"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDhcU1y8jUHJm+P+Ulwp5463FbTae8fcGGRIpXQCwpG/ZeSf/I98NcA4VpY+HnaY0uswActD8RbTWBB6iYwXoWB416pGWfz0NpHrkE33vHKPKllN8wuWCcekNdhONyz0a6DEj9c+gCEPCcIPtDzAHrAWwpaJeooWleyFzAvohuD5wIDAQAB\",\"appSecretKey\":\"GS20170627100319403eIhcrj69\",\"channelAccountEnabled\":0,\"createTime\":**********,\"domainWhiteList\":\"\",\"ipWhiteList\":\"************,*************,*************,************,************,**************,*************,************,*************,**************,************,**************,************,*************,************,************,**************,*************,************,************,************,************\",\"lockStatus\":\"0\",\"merchantId\":\"91D730C7E15547AD87D8FA7AC3F42764\",\"merchantName\":\"麦卡福利社\",\"mobilePhone\":\"***********\",\"needUserAuth\":\"0\",\"push\":1,\"pushClientId\":\"100002\",\"rebate\":0,\"tradeNotifyUrl\":\"merchant.goodsogood.com\",\"updateTime\":**********},\"message\":\"success\",\"timestamp\":*************}";
		//System.out.println(json.replace("8FA5649750B64E3694D2DB7D7D538776", ""));
		System.out.println(JsonPropertyUtil.removeProperties(json,"appId","appPrivateKey","appPublicKey","appSecretKey","ipWhiteList"));
		;
	}

}
