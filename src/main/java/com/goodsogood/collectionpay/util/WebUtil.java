package com.goodsogood.collectionpay.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.MalformedURLException;
import java.net.NetworkInterface;
import java.net.URL;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.servlet.http.HttpServletRequest;

import com.alibaba.fastjson.JSON;

public class WebUtil {
	
	public static File downloadFile(String httpUrl) throws IOException{
		//将接口返回的对账单下载地址传入urlStr
		//String urlStr = "http://dwbillcenter.alipay.com/downloadBillFile.resource?bizType=X&userId=X&fileType=X&bizDates=X&downloadFileName=X&fileId=X";
		//指定希望保存的文件路径
		
		String userDir = System.getProperty("user.dir");

		File file = new File(userDir + File.separator +
				UUIDUtils.uuid32()+".zip");
		
		URL url = null;
		HttpURLConnection httpUrlConnection = null;
		InputStream fis = null;
		FileOutputStream fos = null;
		try {
		    url = new URL(httpUrl);
		    httpUrlConnection = (HttpURLConnection) url.openConnection();
		    httpUrlConnection.setConnectTimeout(5 * 1000);
		    httpUrlConnection.setDoInput(true);
		    httpUrlConnection.setDoOutput(true);
		    httpUrlConnection.setUseCaches(false);
		    httpUrlConnection.setRequestMethod("GET");
		    httpUrlConnection.setRequestProperty("Charsert", "UTF-8");
		    httpUrlConnection.connect();
		    fis = httpUrlConnection.getInputStream();
		    System.out.println(file.getAbsolutePath());
		    byte[] temp = new byte[1024];
		    int b;
		    fos = new FileOutputStream(new File(file.getAbsolutePath()));
		    while ((b = fis.read(temp)) != -1) {
		        fos.write(temp, 0, b);
		        fos.flush();
		    }
		} catch (MalformedURLException e) {
		    e.printStackTrace();
		} catch (IOException e) {
		    e.printStackTrace();
		} finally {
		    try {
		        if(fis!=null) fis.close();
		        if(fos!=null) fos.close();
		        if(httpUrlConnection!=null){
		        	httpUrlConnection.disconnect();
		        }
		    } catch (IOException e) {
		        e.printStackTrace();
		    }
		}
		
	/*	byte[] fileBytes=FileUtils.readFileToByteArray(billFile);
		Map<String,byte[]> zips = unZipMultipleFile(fileBytes);
		
		Set<String> KEYS = zips.keySet();
		
		for(String key:KEYS){
			System.out.println(key);
			System.out.println(new String(zips.get(key),"gbk"));
			;
		}
		*/
		return file;
	}
	
	
	public static Map<String,String> getHeaders(HttpServletRequest request){
		Map<String,String> headers =new HashMap<String, String>();
		Enumeration<String> names= request.getHeaderNames();
		while(names.hasMoreElements()){
			String name =names.nextElement() ;
			headers.put(name, request.getHeader(name));
		}
		return headers ;
	}
	
	
	public static String getArgs(HttpServletRequest request){
		 Enumeration<String> headers = request.getParameterNames();
		 Map<String,String> map = new HashMap<>();
		 while(headers.hasMoreElements()){
			String name = headers.nextElement();
			map.put(name, request.getParameter(name));
		 }
		return JSON.toJSONString(map,true);
	}


	public static String subNoSuffix(String cardNo) {
		return cardNo.substring(cardNo.length() - 4);
	}

	/**
	 * 获取用户真实IP地址，不使用request.getRemoteAddr()的原因是有可能用户使用了代理软件方式避免真实IP地址,
	 * 可是，如果通过了多级反向代理的话，X-Forwarded-For的值并不止一个，而是一串IP值
	 * 
	 * @return ip
	 */
	public static String getIpAddr(HttpServletRequest request) {
		String ip = request.getHeader("X-Forwarded-For");
		System.out.println("x-forwarded-for ip: " + ip);
		if (ip != null && ip.length() != 0 && !"unknown".equalsIgnoreCase(ip)) {
			// 多次反向代理后会有多个ip值，第一个ip才是真实ip
			if (ip.indexOf(",") != -1) {
				ip = ip.split(",")[0];
			}
		}

		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("x-forwarded-for");
			if (ip != null && ip.length() != 0 && !"unknown".equalsIgnoreCase(ip)) {
				// 多次反向代理后会有多个ip值，第一个ip才是真实ip
				if (ip.indexOf(",") != -1) {
					ip = ip.split(",")[0];
				}
			}
		}

		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("Proxy-Client-IP");
			System.out.println("Proxy-Client-IP ip: " + ip);
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("WL-Proxy-Client-IP");
			System.out.println("WL-Proxy-Client-IP ip: " + ip);
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("HTTP_CLIENT_IP");
			System.out.println("HTTP_CLIENT_IP ip: " + ip);
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("HTTP_X_FORWARDED_FOR");
			System.out.println("HTTP_X_FORWARDED_FOR ip: " + ip);
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("X-Real-IP");
			System.out.println("X-Real-IP ip: " + ip);
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getRemoteAddr();
			System.out.println("getRemoteAddr ip: " + ip);
		}
		System.out.println("获取客户端ip: " + ip);
		return ip;
	}
	


	/**
	 * 隐藏用户手机号码和身份证号码以*代替 。
	 * 
	 * @param str
	 * @return
	 */
	public static String userSensitiveInfoHide(String str) {
		if (!org.springframework.util.StringUtils.isEmpty(str)) {
			if (str.length() == 11) {
				return str.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
			} else if (str.length() >= 18) {
				return str.replaceAll("(\\d{4})\\d{10}(\\w{4})", "$1**********$2");
			}
		}
		return str;
	}

	private static String serverIp = null;

	public static String getServerIp() {
		if (serverIp != null) {
			return serverIp;
		}
		InetAddress ip = null;
		try {
			boolean bFindIP = false;
			Enumeration<NetworkInterface> netInterfaces = (Enumeration<NetworkInterface>) NetworkInterface
					.getNetworkInterfaces();
			while (netInterfaces.hasMoreElements()) {
				if (bFindIP) {
					break;
				}
				NetworkInterface ni = (NetworkInterface) netInterfaces.nextElement();
				// 遍历所有ip
				Enumeration<InetAddress> ips = ni.getInetAddresses();
				while (ips.hasMoreElements()) {
					ip = (InetAddress) ips.nextElement();
					if (ip.isSiteLocalAddress() && !ip.isLoopbackAddress() // 127.开头的都是lookback地址
							&& ip.getHostAddress().indexOf(":") == -1) {
						bFindIP = true;
						break;
					}
				}

			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		if (null != ip) {
			serverIp = ip.getHostAddress();
		}
		return serverIp;
	}
	
	
	
	/***
	 * 检查list中 存在重复
	 * @param obj
	 * @param list
	 * @return true:存在 ; false :不存在!
	 */
	public static boolean repeatedInList(List list){
		List list2 =new ArrayList<>();
		
		for (int i = 0; i < list.size(); i++) {
			if(!list2.contains(list.get(i))){
				list2.add(list.get(i));
			}
		}
		return list.size()!=list2.size();
	}


	public static String delHTMLTag(String htmlStr) {
		String regEx_script = "<script[^>]*?>[\\s\\S]*?<\\/script>"; // 定义script的正则表达式
		String regEx_style = "<style[^>]*?>[\\s\\S]*?<\\/style>"; // 定义style的正则表达式
		String regEx_html = "<[^>]+>"; // 定义HTML标签的正则表达式
		Pattern p_script = Pattern.compile(regEx_script, Pattern.CASE_INSENSITIVE);
		Matcher m_script = p_script.matcher(htmlStr);
		htmlStr = m_script.replaceAll(""); // 过滤script标签

		Pattern p_style = Pattern.compile(regEx_style, Pattern.CASE_INSENSITIVE);
		Matcher m_style = p_style.matcher(htmlStr);
		htmlStr = m_style.replaceAll(""); // 过滤style标签

		Pattern p_html = Pattern.compile(regEx_html, Pattern.CASE_INSENSITIVE);
		Matcher m_html = p_html.matcher(htmlStr);
		htmlStr = m_html.replaceAll(""); // 过滤html标签
		return htmlStr.trim(); // 返回文本字符串
	}
	
	public static boolean match(String regex,String input){
		return Pattern.compile(regex).matches(regex, input);
	}
	
	
	public static String replaceNullToBlank(Object obj){
		if(null ==obj)
		{
			return "";
		}
		return obj.toString() ;
	}
	
	public static String replaceNullToBlank(Object obj,String defaultValue){
		if(null ==obj)
		{
			return defaultValue;
		}
		return obj.toString() ;
	}
	
	private static final int  ZIP_BUFFER_SIZE = 10 * 1024;
	
	public static void fileZip(java.io.File srcFile, OutputStream out) throws RuntimeException {
		long start = System.currentTimeMillis();
		ZipOutputStream zos = null;
		try {
			zos = new ZipOutputStream(out);
			byte[] buf = new byte[ZIP_BUFFER_SIZE];
			zos.putNextEntry(new ZipEntry(srcFile.getName()));
			int len;
			FileInputStream in = new FileInputStream(srcFile);
			while ((len = in.read(buf)) != -1) {
				zos.write(buf, 0, len);
			}
			zos.closeEntry();
			in.close();
			long end = System.currentTimeMillis();
			System.out.println("压缩完成，耗时：" + (end - start) + " ms");
		} catch (Exception e) {
			throw new RuntimeException("zip error from ZipUtils", e);
		} finally {
			if (zos != null) {
				try {
					zos.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}
	
	
	public static boolean isNumber(String str){
		Pattern pattern =Pattern.compile("^[0-9]{0,1}[0-9]{0,}$");
		return pattern.matcher(str).find();
	}
	

	
	public static String readInputStreamAsString(InputStream inputStream,
			int length,String charsetName) throws IOException {
			if (inputStream != null) {
			byte[] streamBytes = new byte[length];
			inputStream.read(streamBytes);
			return new String(streamBytes,charsetName);
			}
			return null;
		}
			
	
	public static void main(String[] args) {
	}
	

}
