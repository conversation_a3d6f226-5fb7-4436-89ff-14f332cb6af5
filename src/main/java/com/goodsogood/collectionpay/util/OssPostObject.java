package com.goodsogood.collectionpay.util;
import java.io.BufferedReader;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;

import javax.activation.MimetypesFileTypeMap;

import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.aliyun.oss.OSSClient;
import com.goodsogood.collectionpay.consts.LogConfiger;

/**
 * Created by yushuting on 16/4/17.
 */
@Component
public class OssPostObject {
	
	private static final Logger logger = LoggerFactory.getLogger(LogConfiger.APP);
	
    //	@Value("${bill.tempfile.endpoint}")
    private String ossEndpoint = "your_endpoint";  // 如: http://oss-cn-shanghai.aliyuncs.com
    private String ossAccessId = "DFTBZA4KpoWWGUdi";  // 你的访问AK信息
    private String ossAccessKey = "5uBQ6gYt6PEa3hqRdHnkMJLrhFFdIk";  // 你的访问AK信息
    private String bucket = "gsg-tempfile";  // 你之前创建的bucket，确保这个bucket已经创建

    public String PostObject(String fileName) throws Exception {
    	logger.info("billFilePost file={}",fileName);
    	long t=System.currentTimeMillis() ;
    	
    	File file = new File(fileName);
    	
        String urlStr = ossEndpoint.replace("http://", "http://"+bucket+"."); // 提交表单的URL为bucket域名

        LinkedHashMap<String, String> textMap = new LinkedHashMap<String, String>();
        // key
        String objectName = file.getName();
        textMap.put("key", objectName);
        // Content-Disposition
        textMap.put("Content-Disposition", "attachment;filename="+file.getName());
        // OSSAccessKeyId
        textMap.put("OSSAccessKeyId", ossAccessId);
        SimpleDateFormat format =new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.000'Z'");
        Date expiratDate = new Date(System.currentTimeMillis()+(1*60*1000)) ;
        String expiration =  format.format(expiratDate);
        logger.info("Expiration={}",expiration);
        // policy
        String policy = "{\"expiration\": \""+expiration+"\",\"conditions\": [[\"content-length-range\", 0, 104857600]]}";
        String encodePolicy = java.util.Base64.getEncoder().encodeToString(policy.getBytes());
        textMap.put("policy", encodePolicy);
        // Signature
        String signaturecom = com.aliyun.oss.common.auth.ServiceSignature.create().computeSignature(ossAccessKey, encodePolicy);
        textMap.put("Signature", signaturecom);

        Map<String, String> fileMap = new HashMap<String, String>();
        fileMap.put("file", file.getAbsolutePath());

        String ret = formUpload(urlStr, textMap, fileMap);
        logger.info("[" + bucket + "] post_object:" + objectName);
        logger.info("post reponse:" + ret);
        long t2=System.currentTimeMillis() ;
        logger.info("上传账单文件 {} 到 OSS 成功!文件大小={},过期时间为:{},耗时:{} ms!",file.getAbsolutePath(),
        		FileUtils.byteCountToDisplaySize(file.length()),DateTimeUtil.format(expiratDate),(t2-t));
      //  String AccessUrl = getAccessUrl(file.getName()) ;
      //  logger.info("AccessUrl:{}" , AccessUrl);
    	return "http://tempfile.goodsogood.com/"+file.getName();//+AccessUrl.substring(AccessUrl.indexOf("?"));
    	
    }
    
    public void deleteObject(String key){
    	String endpoint = ossEndpoint;
    	// 阿里云主账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM账号进行API访问或日常运维，请登录 https://ram.console.aliyun.com 创建RAM账号。
    	String accessKeyId = ossAccessId;
    	String accessKeySecret = ossAccessKey;
    	String bucketName = bucket;
    	OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
    	 ossClient.deleteObject(bucketName, key);
    	logger.info("DeleteObject key={}",key);
    	// 关闭OSSClient。
    	ossClient.shutdown();
    }
    
    /***
     * 生成一个有效时间为1分钟的访问链接
     * 
     * @param objName
     * @return
     */
    private String getAccessUrl(String objName){
    	// Endpoint以杭州为例，其它Region请按实际情况填写。
    	String endpoint = ossEndpoint;
    	// 阿里云主账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM账号进行API访问或日常运维，请登录 https://ram.console.aliyun.com 创建RAM账号。
    	String accessKeyId = ossAccessId;
    	String accessKeySecret = ossAccessKey;
    	String bucketName = bucket;
    	String objectName = objName;
    	// 创建OSSClient实例。
    	OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
    	// 设置URL过期时间为1小时。
    	Date expiration = new Date(new Date().getTime() + 60 * 1000);
    	// 生成以GET方法访问的签名URL，访客可以直接通过浏览器访问相关内容。
    	URL url = ossClient.generatePresignedUrl(bucketName, objectName, expiration);
    	// 关闭OSSClient。
    	ossClient.shutdown();
    	return url.toString();
    }
    
    

    private static String formUpload(String urlStr, Map<String, String> textMap, Map<String, String> fileMap) throws Exception {
        String res = "";
        HttpURLConnection conn = null;
        String BOUNDARY = "9431149156168";
        try {
            URL url = new URL(urlStr);
            conn = (HttpURLConnection) url.openConnection();
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(30000);
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("User-Agent",
                    "Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-CN; rv:*******)");
            conn.setRequestProperty("Content-Type",
                    "multipart/form-data; boundary=" + BOUNDARY);

            OutputStream out = new DataOutputStream(conn.getOutputStream());
            // text
            if (textMap != null) {
                StringBuffer strBuf = new StringBuffer();
                Iterator iter = textMap.entrySet().iterator();
                int i = 0;
                while (iter.hasNext()) {
                    Map.Entry entry = (Map.Entry) iter.next();
                    String inputName = (String) entry.getKey();
                    String inputValue = (String) entry.getValue();
                    if (inputValue == null) {
                        continue;
                    }
                    if (i == 0) {
                        strBuf.append("--").append(BOUNDARY).append(
                                "\r\n");
                        strBuf.append("Content-Disposition: form-data; name=\""
                                + inputName + "\"\r\n\r\n");
                        strBuf.append(inputValue);
                    } else {
                        strBuf.append("\r\n").append("--").append(BOUNDARY).append(
                                "\r\n");
                        strBuf.append("Content-Disposition: form-data; name=\""
                                + inputName + "\"\r\n\r\n");

                        strBuf.append(inputValue);
                    }

                    i++;
                }
                out.write(strBuf.toString().getBytes());
            }

            // file
            if (fileMap != null) {
                Iterator iter = fileMap.entrySet().iterator();
                while (iter.hasNext()) {
                    Map.Entry entry = (Map.Entry) iter.next();
                    String inputName = (String) entry.getKey();
                    String inputValue = (String) entry.getValue();
                    if (inputValue == null) {
                        continue;
                    }
                    File file = new File(inputValue);
                    String filename = file.getName();
                    String contentType = new MimetypesFileTypeMap().getContentType(file);
                    if (contentType == null || contentType.equals("")) {
                        contentType = "application/octet-stream";
                    }

                    StringBuffer strBuf = new StringBuffer();
                    strBuf.append("\r\n").append("--").append(BOUNDARY).append(
                            "\r\n");
                    strBuf.append("Content-Disposition: form-data; name=\""
                            + inputName + "\"; filename=\"" + filename
                            + "\"\r\n");
                    strBuf.append("Content-Type: " + contentType + "\r\n\r\n");

                    out.write(strBuf.toString().getBytes());

                    DataInputStream in = new DataInputStream(new FileInputStream(file));
                    int bytes = 0;
                    byte[] bufferOut = new byte[1024];
                    while ((bytes = in.read(bufferOut)) != -1) {
                        out.write(bufferOut, 0, bytes);
                    }
                    in.close();
                }
                StringBuffer strBuf = new StringBuffer();
                out.write(strBuf.toString().getBytes());
            }

            byte[] endData = ("\r\n--" + BOUNDARY + "--\r\n").getBytes();
            out.write(endData);
            out.flush();
            out.close();

            // 读取返回数据
            StringBuffer strBuf = new StringBuffer();
            BufferedReader reader = new BufferedReader(new InputStreamReader(
                    conn.getInputStream()));
            String line = null;
            while ((line = reader.readLine()) != null) {
                strBuf.append(line).append("\n");
            }
            res = strBuf.toString();
            reader.close();
            reader = null;
        } catch (Exception e) {
            System.err.println("发送POST请求出错: " + urlStr);
            throw e;
        } finally {
            if (conn != null) {
                conn.disconnect();
                conn = null;
            }
        }
        return res;
    }
    
    public static void main(String[] args) {

    	String str="http://gsg-tempfile.oss-cn-hangzhou.aliyuncs.com/dex2jar-2.0.zip?Expires=1540193091&OSSAccessKeyId=DFTBZA4KpoWWGUdi&Signature=vRYXybVXOP8ZtYhIrgdcwQrKNDc%3D";
		
         System.out.println(str.substring(str.indexOf("?")));    	
    	
	}


}