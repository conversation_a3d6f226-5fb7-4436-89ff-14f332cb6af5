package com.goodsogood.collectionpay.util;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.StandardWatchEventKinds;
import java.nio.file.WatchEvent;
import java.nio.file.WatchKey;
import java.nio.file.WatchService;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.goodsogood.collectionpay.consts.LogConfiger;


/***
 * 文件监听
 * 
 * @Description
 * <AUTHOR>
 * @time 2017年11月8日下午5:31:47
 */
public class FileWatchUtil {

	private static Logger logger = LoggerFactory.getLogger(LogConfiger.APP);

	private FileWatchUtil() {
	}



	public static void watch(Path path, WatchCallback caller) throws IOException {
		watch(path, caller, null);
	}

	public static void watch(Path path, WatchCallback caller, WatchEvent.Kind<?>... events) throws IOException {
		Path myDir = path;
		logger.info("=========监听文件：" + path + "======");
		File watchFile = myDir.toFile();
		String targetFile = null;
		if (!watchFile.isDirectory()) {
			myDir = watchFile.getParentFile().toPath();
			targetFile = watchFile.getName();
		} 
		WatchService watcher = myDir.getFileSystem().newWatchService();

		if (events != null && events.length > 0) {
			myDir.register(watcher, events);
		} else {
			myDir.register(watcher, StandardWatchEventKinds.ENTRY_MODIFY, StandardWatchEventKinds.ENTRY_CREATE,
					StandardWatchEventKinds.ENTRY_DELETE);
		}
		Listener listener = new Listener(watcher, targetFile, caller);
		new Thread(listener).start();
	}

	static class Listener implements Runnable {
		private WatchService service;
		private WatchCallback caller;
		private String targetFile;

		public Listener(WatchService service, String targetFile, WatchCallback caller) {
			this.service = service;
			this.caller = caller;
			this.targetFile = targetFile;
		}

		public void run() {
			try {
				while (true) {
					WatchKey watchKey = service.take();
					List<WatchEvent<?>> watchEvents = watchKey.pollEvents();
					for (WatchEvent<?> event : watchEvents) {
						if (targetFile != null) {
							if (targetFile.equals(event.context().toString()))
								caller.call(event);
						} else {
							caller.call(event);
						}
					}
					watchKey.reset();
				}
			} catch (Exception e) {
				e.printStackTrace();
			} finally {
				try {
					service.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}

		}
	}

}
