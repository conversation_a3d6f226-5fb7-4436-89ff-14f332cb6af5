package com.goodsogood.collectionpay.util;

import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import org.apache.commons.codec.binary.Base64;

public class HMAC_SHA256 {
 
	private static final String HMAC_SHA1_ALGORITHM = "HmacSHA256";
	
	private static String byteArrayToHexString(byte[] b) {  
	      StringBuilder hs = new StringBuilder();     
	      String stmp;      
	  for (int n = 0; b!=null && n < b.length; n++) {   
	         stmp = Integer.toHexString(b[n] & 0XFF);    
	         if (stmp.length() == 1)           
	         hs.append('0');      
	         hs.append(stmp);    
	    }      
	   return hs.toString().toLowerCase();   
	 } 
 
	/**
	 * 使用 HMAC-SHA1 签名方法对data进行签名
	 * 
	 * @param data
	 *            被签名的字符串
	 * @param key
	 *            密钥     
	 * @return 
                      加密后的字符串
	 */
	/*public static byte[] genHMAC(String data, String key) {
		byte[] result = null;
		try {
			//根据给定的字节数组构造一个密钥,第二参数指定一个密钥算法的名称  
			SecretKeySpec signinKey = new SecretKeySpec(key.getBytes(), HMAC_SHA1_ALGORITHM);
			//生成一个指定 Mac 算法 的 Mac 对象  
			Mac mac = Mac.getInstance(HMAC_SHA1_ALGORITHM);
			//用给定密钥初始化 Mac 对象  
			mac.init(signinKey);
			//完成 Mac 操作   
			byte[] rawHmac = mac.doFinal(data.getBytes());
			return rawHmac;
 
		} catch (NoSuchAlgorithmException e) {
			System.err.println(e.getMessage());
		} catch (InvalidKeyException e) {
			System.err.println(e.getMessage());
		}
			return null;
	}*/
	
	public static String sha256_HMAC(String message, String secret) {     
		   String hash = "";      
		   try {         
		       Mac sha256_HMAC = Mac.getInstance("HmacSHA256");     
		       SecretKeySpec secret_key = new SecretKeySpec(secret.getBytes(), "HmacSHA256");      
		       sha256_HMAC.init(secret_key);       
		       byte[] bytes = sha256_HMAC.doFinal(message.getBytes());   
		       hash = byteArrayToHexString(bytes);         
		       System.out.println(hash);    
		       } catch (Exception e) {     
		       System.out.println("Error HmacSHA256 ===========" + e.getMessage());   
		     }       
		    return hash;   
		   }     
	
	/**
	 * 测试
	 * @param args
	 */
	public static void main(String[] args) {
		//String data="data=0eUw2Nk2isX+IRlttM7eKomZCAfJlocPw2lG4uVrc0qIIbhOusoy0Pp5hl5sWKRxrp4+/YVTJKqFGlS+dB+0/ApQ9/yVvQmmDdIx4kdUw487S4KruUxMy14qkrqSreTHKkGww71pUX8Xe6jBBZHkGTeibyOWEiaxp3UUNJs7Qxo0rWAct4+0ntTdniNMalsQbKQ7AvhuwWJY+c9HSgMtizJ7IlkhzfOC9SyUr7353G/fd1GX7p2Q+j22b7YceAyZyIgsdEyX24z0tPeh6b49pKJpThf792UJxkxCDq3rdbAfAVpmf+erQ8qBHgD+KElyB5ywHBkTNLxZEW1tzMmd7C76ANmrQgY8oOz7e91DTjbuc8z4Qr8QsWub0+mUNrlGvbYeCfc2+emkeo1moVZXujLof8JmrCZUSstrg9qE1kEptj0SvnELk9RYEAYqXe15O0fLWz/ywRgCmerpWdqRViWp+YV4+9QrI1bCRlWRp+7Xuc8hNCsIojaOxK0Xpn4gbde1+dX1SYOJu9RqIMDRdTzfllUwzsGexLu9sb3NphodW7brNG5l/lG4k5vs23565DJgbr/zvXYTslLwHkr+RkVYSyhjr0xPF1ZQQHzZdzdN4DiliRAlATE27BDHSTvV&mess=12313&timestamp=123457&sign_type=sha256&key=78f9b4fad3481fbce1df0b30eee58577";
		//String data ="121213";
		//String genHMAC = byteArrayToHexString(genHMAC(data, "78f9b4fad3481fbce1df0b30eee58577"));
		//System.out.println(genHMAC);
		//System.out.println(sha256_HMAC(data,"78f9b4fad3481fbce1df0b30eee58577"));  
	}
}