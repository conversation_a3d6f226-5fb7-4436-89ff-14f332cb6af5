package com.goodsogood.collectionpay.util;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.util.CollectionUtils;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

import static org.springframework.beans.BeanUtils.instantiate;

/**
 * <AUTHOR>
 * @describe bean工具类
 */
public class BeanUtil {

    private BeanUtil() {
    }

    public static <T> T copy(Object source, Class<T> targetClazz, String... ignoreProperties) {
        return copy(source, targetClazz, null, ignoreProperties);
    }

    public static <T> T copy(Object source, Class<T> targetClazz, CopyCallBack copyCallBack, String... ignoreProperties) {
        if (source == null || targetClazz == null) {
            return null;
        }
        T target = instantiate(targetClazz);
        BeanUtils.copyProperties(source, target, ignoreProperties);

        if (Objects.nonNull(copyCallBack)) {
            copyCallBack.doCopy(source, target);
        }
        return target;
    }

    public interface CopyCallBack<T> {
        void doCopy(Object source, T t);
    }

    public static <T> List<T> copyList(List<?> source, Class<T> targetClazz, String... ignoreProperties) {
        return copyList(source, targetClazz, null, ignoreProperties);
    }

    public static <T> List<T> copyList(List<?> source, Class<T> targetClazz, CopyCallBack copyCallBack, String... ignoreProperties) {
        if (CollectionUtils.isEmpty(source) || targetClazz == null) {
            return null;
        }
        return source.stream().map(o -> BeanUtil.copy(o, targetClazz, copyCallBack, ignoreProperties)).collect(Collectors.toList());
    }

    private static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<String>();
        for (PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            }
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

    public static void copyPropertiesIgnoreNull(Object src, Object target) {
        BeanUtils.copyProperties(src, target, getNullPropertyNames(src));
    }

    public static Map<String, Object> transBean2Map(Object obj, String... ignoreNames) {
        return transBean2Map(obj, null, ignoreNames);
    }

    /**
     * Bean --> Map 1: 利用Introspector和PropertyDescriptor 将Bean --> Map
     *
     * @param obj 需要转换的对象
     */
    public static Map<String, Object> transBean2Map(Object obj, NameCallback nameCallback, String... ignoreNames) {

        if (obj == null) {
            return null;
        }

        Map<String, Object> map = new HashMap<>(16);
        List<String> ignoreNameList = Arrays.asList(ignoreNames);

        try {

            BeanInfo beanInfo = Introspector.getBeanInfo(obj.getClass());
            PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
            for (PropertyDescriptor property : propertyDescriptors) {
                String key = property.getName();
                // 过滤class属性
                if (!"class".equals(key)) {
                    // 得到property对应的getter方法
                    Method getter = property.getReadMethod();
                    Object value = getter.invoke(obj);
                    if (Objects.nonNull(value) && !ignoreNameList.contains(key)) {
                        if (Objects.nonNull(nameCallback)) {
                            map.put(nameCallback.convertName(key), value);
                            continue;
                        }

                        map.put(key, value);
                    }
                }
            }
        } catch (Exception ignored) {

        }

        return map;
    }

    public interface NameCallback {
        /**
         * name 回调
         *
         * @param name name
         * @return 新的name
         */
        String convertName(String name);
    }

}
