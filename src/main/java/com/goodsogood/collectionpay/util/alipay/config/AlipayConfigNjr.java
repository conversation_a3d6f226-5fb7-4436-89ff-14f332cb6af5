package com.goodsogood.collectionpay.util.alipay.config;

/* *
 *类名：AlipayConfig
 *功能：基础配置类
 *详细：设置帐户有关信息及返回路径
 *版本：3.4
 *修改日期：2016-03-08
 *说明：
 *以下代码只是为了方便商户测试而提供的样例代码，商户可以根据自己网站的需要，按照技术文档编写,并非一定要使用该代码。
 *该代码仅供学习和研究支付宝接口使用，只是提供一个参考。
 */

public class AlipayConfigNjr {

    private static final String ALIPAYPARTNER = "2088621468123508";
    /**
     * 网关支付密钥：自己私自生成
     */
    private static final String ALIPAYPRIVATEKEY = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAN69kv2s3RwXnnXFWqpzX1R3SNJxhfxtSDKNkUmgtFe/GwJZrKyI4pnzYW/Hz1bE15/STvGkv6WZLmYv8dbUL510EHqI3x+fY/ArvnMfU4GV7qbxTV2kxRpxpQcz7NRxcRxo31DkTCVFyisn8GXU/66B380DHJpt5ubmopl6ZeWjAgMBAAECgYAenLFJ+er9hGV7zB7U1Dxi0G4Kth1y/tXwVLKGCvceJB6jKQ3pPHrgK6nVggGF7FGyHmZJIZpROQc+dhJnswvD91us4Pi2BMaLSp9vN1aVwJnD3ubsxDl+5xxwdEJ17WTEiA2ENyciOet5wMi9I6ZtACsMtx0bMZgjxafJazscmQJBAPPtojxigzTDoL+4XTBpbOMvzO0hLgJpZ3rWdFgqMFc5RILwrqZWe/UTSck2qQScKN6eNBOV/VvhvMq/EQ56P20CQQDpw4J5LaduOEhXDk3nPvnOL9EocgAxbcWmE+h5ntLVN6WCrhQ5md/4TQt8CxzmHaQ0Ccd8npCZ+7B+3i/4gr9PAkEArVZBgl2S1SzrDzJI7n0MRokSO7PnSQlz4mOAu6IHRgRI/i9gPI2aenYhad3wz3jUxhVv9jcQmnlYOk76JOqUJQJAKzPG2T5mwygDtpMeMq6g3EpaDW+EOZ5f+OGVBWUk8eLm48/oTPzzN6CFjtc+8AhqYHOuOonTj6HEbK4Dj5oaYQJAEDZK1xIaehsqIvTCd9ZXho8RB9NZtzsUn1gwqcmPyLWjoG4kZZHYqRaC06Yu1cSw+7pXAKdM4NRfQXFi9Qt1Rw==";
    
    /**
     * 支付宝公钥
     */
    private static final String ALIPAYPUBLICKEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCnxj/9qwVfgoUh/y2W89L6BkRAFljhNhgPdyPuBV64bfQNN1PjbCzkIM6qRdKBoLPXmKKMiFYnkd6rAoprih3/PrQEB/VsW8OoM8fxn67UDYuyBTqA23MML9q1+ilIZwBC2AQ2UBVOrFXfFl75p6/B5KsiNG9zpgmLCUYuLkxpLQIDAQAB";
    //private static final String ALIPAYKEY = "3zf82dzzdlagv4fmqqqcsxc0tgvaj0nk";
    
    //
    //private static final String GSGAPPID = "2016121404261943";       // 惠工钱包
    //private static final String GSGPRIVATEKEY = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAMEZm2xpoiwE5hrNK+JaLcW9dTihZ6pjRHLy076Ecplo6TJFR15+TFAk5E9wKUKwhQ8vih7BMRqJ3oiYPzPGYJegtyGQogTekUwYXFgLpluxnpG9hMH/d+RqKaKKBCUz52TLP2vpVdgswHunYLgaZ1v/CGC+Yw4Hd9BuJAtQPHf7AgMBAAECgYAZZrHwT/D6U2aljenA69XrliGOYoUNf+NoL5M1E50zmv5aE4zw3DJTtJan8Hws/qVyOvGzW/yGni48z7I4sFbSQLGEeOYCYwiJevhUwS7OWT07KmMHwSZ7dEk//t8QRDosqMeeXLmEHtQrwlZAxlER11aR/ifrFfoPdwH98/elmQJBAPAyV/HJRezWkWVZB0yzUY7MWCYdSxMxoqsp2Dokftty0+XeL0vLSKg8SxkSkOS5q2OzCq31uIagTYzFbc6SzX8CQQDNzgKm8Gd9zXsySoCZTyrMkeTLRn+8qpuUg7dsJDYXDdMtLIYo2AJ028WyzMx5iThFUxln0miKdWpwfE+0/MuFAkEAr1QwOb+vmQeurgnkeCpUmCW1Gm07hdEvaj1rJlXJe6o/3oTmhS7n5FXOg4i31x1oa6gfXYgFn8+Xi+/5DVa/RQJAV1QAWhgOwhoMGaX1dZhbP9z0abEriMIYLGfL0C55aUTrg/Ktpu0p9a4/8IPoi/XHE9uUHzLwOzhhhwOoytKuBQJALPFe9X/lBsSnZox4nsXtTZp2yOYKiZxnaOkTQUul1A5x+g4DEKdrzYto6VwoTH1BcOVlTG58R91yOw2w/8Szlg==";
//↓↓↓↓↓↓↓↓↓↓请在这里配置您的基本信息↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓

    // 合作身份者ID，签约账号，以2088开头由16位纯数字组成的字符串，查看地址：https://b.alipay.com/order/pidAndKey.htm

    // 收款支付宝账号，以2088开头由16位纯数字组成的字符串，一般情况下收款账号就是签约账号

    //商户的私钥,需要PKCS8格式，RSA公私钥生成：https://doc.open.alipay.com/doc2/detail.htm?spm=a219a.7629140.0.0.nBDxfy&treeId=58&articleId=103242&docType=1

    // 支付宝的公钥,查看地址：https://b.alipay.com/order/pidAndKey.htm


    // 服务器异步通知页面路径  需http://格式的完整路径，不能加?id=123这类自定义参数，必须外网可以正常访问
    //public static String notify_url = "http://test.cheneibao.com:8080/carcare/alipay/promotionCqzghOrderCallback";
    //public static String notify_url = "http://api.goodsogood.com/union/pay/callback/alipay";
    
    // 页面跳转同步通知页面路径 需http://格式的完整路径，不能加?id=123这类自定义参数，必须外网可以正常访问
    //public static String return_url = "http://test.cheneibao.com/cqzgh/#!/buyinsurancepaysuccess";

    // 签名方式
    public static String sign_type = "RSA";

//	// 调试用，创建TXT日志文件夹路径，见AlipayCore.java类中的logResult(String sWord)打印方法。
//	public static String log_path = "C:\\";

    // 字符编码格式 目前支持utf-8
    public static String input_charset = "utf-8";

    // 支付类型 ，无需修改
    public static String payment_type = "1";

    // 调用的接口名，无需修改
    public static String service = "alipay.wap.create.direct.pay.by.user";


//↑↑↑↑↑↑↑↑↑↑请在这里配置您的基本信息↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑

}

