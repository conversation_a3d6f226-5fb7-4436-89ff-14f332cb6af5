package com.goodsogood.collectionpay.util;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class ReflectionUtils {

	// class Field名称缓存
	private static final Map<String, String[]> classFieldNameCache = new ConcurrentHashMap<String, String[]>();
	
	// class Field缓存
	private static final Map<String, Field[]> classFieldCache = new ConcurrentHashMap<String, Field[]>();

	private ReflectionUtils() {
	}
	
	/**
	 * 获取父类的泛型类型 
	 * @param clazz
	 * @return
	 */
	public static Type getSuperClassParameterizedType(Class<?> clazz) {
		Type type = clazz.getGenericSuperclass();
		ParameterizedType parameterizedType = (ParameterizedType) type;
		return parameterizedType.getActualTypeArguments()[0];
	}
	
	/**
	 * 获取父类的泛型类型 
	 * @param clazz
	 * @return
	 */
	public static Type getSuperClassParameterizedType(Class<?> clazz,int index) {
		Type type = clazz.getGenericSuperclass();
		ParameterizedType parameterizedType = (ParameterizedType) type;
		return parameterizedType.getActualTypeArguments()[index];
	}

	/**
	 * 获取当前类及其父类中的所有属性名称
	 * 
	 * @param clazz
	 * @return
	 */
	public static String[] getFields(Class<?> clazz) {
		String[] fields = classFieldNameCache.get(clazz.getName());
		if (fields != null) {
			return fields;
		}
		List<String> fname = new ArrayList<>();
		// 循环到父类不是 Object
		for (; clazz != Object.class; clazz = clazz.getSuperclass()) {
			try {
				Field[] Fields = clazz.getDeclaredFields();
				for (Field each : Fields) {
					fname.add(each.getName());
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		fields = new String[fname.size()];
		fname.toArray(fields);
		classFieldNameCache.put(clazz.getName(), fields);
		return fields;
	}
	
	
	
	/**
	 * 获取当前类及其父类中的所有Field
	 * 
	 * @param clazz
	 * @return
	 */
	public static Field[] getFieldList(Class<?> clazz) {
		Field[] fields = classFieldCache.get(clazz.getName());
		if (fields != null) {
			return fields;
		}
		List<Field> fname = new ArrayList<>();
		// 循环到父类不是 Object
		for (; clazz != Object.class; clazz = clazz.getSuperclass()) {
			try {
				Field[] Fields = clazz.getDeclaredFields();
				for (Field each : Fields) {
					fname.add(each);
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		fields = new Field[fname.size()];
		fname.toArray(fields);
		classFieldCache.put(clazz.getName(), fields);
		return fields;
	}
	

	/**
	 * 获取当前类及其父类中的指定属性的值
	 * 
	 * @param clazz
	 * @return
	 */
	public static Object getFieldValue(Object object, String fieldName) {
		// 根据 对象和属性名通过取 Field对象
		Field field = getDeclaredField(object, fieldName);

		field.setAccessible(true);
		try {
			return field.get(object);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	
	public static void setFieldValue(Object object, Field field,String fieldVal) {
		  try{
			  field.setAccessible(true);
			  field.set(object, fieldVal );
    	  }catch (Exception e) {
    		  System.out.println(" ReflectionUtils setFieldValue err:field={},fieldVal={}");
		}
	}
	
	public static void setFieldValue(Object object, Field field,Object fieldVal) {
		  try{
			  field.setAccessible(true);
			  field.set(object, fieldVal );
  	  }catch (Exception e) {
  		  System.out.println(" ReflectionUtils setFieldValue err:field={},fieldVal={}");
		}
	}


	public static Field getDeclaredField(Object object, String fieldName) {
		Field field = null;

		Class<?> clazz = object.getClass();

		for (; clazz != Object.class; clazz = clazz.getSuperclass()) {
			try {
				field = clazz.getDeclaredField(fieldName);
				return field;
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return null;
	}
	
	private static final Logger logger = LoggerFactory.getLogger(ReflectionUtils.class);
	
	public static Map<String, Object> objectToMap(Object obj) throws Exception {    
        if(obj == null){    
            return null;    
        }   
  
        Map<String, Object> map = new HashMap<String, Object>();  
        Field[] declaredFields = obj.getClass().getDeclaredFields();    
        for (Field field : declaredFields) {    
            field.setAccessible(true);  
            map.put(field.getName(), field.get(obj));  
        }    
        Map<String, Object> map2 = new LinkedHashMap<String, Object>();  
        List<Map.Entry<String, Object>> infoIds = new ArrayList<Map.Entry<String, Object>>(map.entrySet());
        // 对所有传入参数按照字段名的 ASCII 码从小到大排序（字典序）
        Collections.sort(infoIds, new Comparator<Map.Entry<String, Object>>()
        {

            @Override
            public int compare(Map.Entry<String, Object> o1, Map.Entry<String, Object> o2)
            {
                return (o1.getKey()).toString().compareTo(o2.getKey());
            }
        });
        
        for (Map.Entry<String, Object> item : infoIds)
        {
        	map2.put(item.getKey(), item.getValue());

        }
        return map2;  
    }   
	


	public static void main(String[] args) throws Exception {}

}
