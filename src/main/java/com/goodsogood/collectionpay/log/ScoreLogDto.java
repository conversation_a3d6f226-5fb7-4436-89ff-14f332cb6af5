package com.goodsogood.collectionpay.log;


/**
 * 积分同步日志 
 * @Description
 * <AUTHOR>
 * @time 2017年11月30日上午11:46:58
 */
public class ScoreLogDto {
	private String logId; // required
	private int scoreTargetType; // required
	private String scoreTargetId; // required
	private int createTime; // required
	private int score; // required
	private String logDescription; // required
	private int deadlineDate; // required
	private int sourceType; // required
	private String businessTableName; // required
	private String businessId; // required
	private int operateType; // required
	private int experiencePoint; // required
	private String indexCode; // required
	private String scoreTargetName; // required
	
	
	public ScoreLogDto(){
		
	}

	public String getLogId() {
		return logId;
	}

	public void setLogId(String logId) {
		this.logId = logId;
	}

	public int getScoreTargetType() {
		return scoreTargetType;
	}

	public void setScoreTargetType(int scoreTargetType) {
		this.scoreTargetType = scoreTargetType;
	}

	public String getScoreTargetId() {
		return scoreTargetId;
	}

	public void setScoreTargetId(String scoreTargetId) {
		this.scoreTargetId = scoreTargetId;
	}

	public int getCreateTime() {
		return createTime;
	}

	public void setCreateTime(int createTime) {
		this.createTime = createTime;
	}

	public int getScore() {
		return score;
	}

	public void setScore(int score) {
		this.score = score;
	}

	public String getLogDescription() {
		return logDescription;
	}

	public void setLogDescription(String logDescription) {
		this.logDescription = logDescription;
	}

	public int getDeadlineDate() {
		return deadlineDate;
	}

	public void setDeadlineDate(int deadlineDate) {
		this.deadlineDate = deadlineDate;
	}

	public int getSourceType() {
		return sourceType;
	}

	public void setSourceType(int sourceType) {
		this.sourceType = sourceType;
	}

	public String getBusinessTableName() {
		return businessTableName;
	}

	public void setBusinessTableName(String businessTableName) {
		this.businessTableName = businessTableName;
	}

	public String getBusinessId() {
		return businessId;
	}

	public void setBusinessId(String businessId) {
		this.businessId = businessId;
	}

	public int getOperateType() {
		return operateType;
	}

	public void setOperateType(int operateType) {
		this.operateType = operateType;
	}

	public int getExperiencePoint() {
		return experiencePoint;
	}

	public void setExperiencePoint(int experiencePoint) {
		this.experiencePoint = experiencePoint;
	}

	public String getIndexCode() {
		return indexCode;
	}

	public void setIndexCode(String indexCode) {
		this.indexCode = indexCode;
	}

	public String getScoreTargetName() {
		return scoreTargetName;
	}

	public void setScoreTargetName(String scoreTargetName) {
		this.scoreTargetName = scoreTargetName;
	}

}
