package com.goodsogood.collectionpay.log;

import java.util.Date;

import org.apache.solr.client.solrj.beans.Field;

import com.goodsogood.collectionpay.util.CommonUtil;



public class SolrLog {

	@Field
	private String log_id;
	@Field
	private String req_id;
	@Field
	private Date time;
	@Field
	private String module_name;
	@Field
	private String level;
	@Field
	private String logger_name;
	@Field
	private String class_name;
	@Field
	private String message;
	@Field
	private String thread_name;
	@Field
	private String server = CommonUtil.getServerIp();

	public String getServer() {
		return server;
	}

	public String getReq_id() {
		return req_id;
	}

	public void setReq_id(String req_id) {
		this.req_id = req_id;
	}



	public Date getTime() {
		return time;
	}

	public void setTime(Date time) {
		this.time = time;
	}

	public void setServer(String server) {
		this.server = server;
	}

	public String getModule_name() {
		return module_name;
	}

	public void setModule_name(String module_name) {
		this.module_name = module_name;
	}

	public String getLevel() {
		return level;
	}

	public void setLevel(String level) {
		this.level = level;
	}

	public String getClass_name() {
		return class_name;
	}

	public void setClass_name(String class_name) {
		this.class_name = class_name;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getLogger_name() {
		return logger_name;
	}

	public void setLogger_name(String logger_name) {
		this.logger_name = logger_name;
	}

	public String getThread_name() {
		return thread_name;
	}

	public void setThread_name(String thread_name) {
		this.thread_name = thread_name;
	}

	public String getLog_id() {
		return log_id;
	}

	public void setLog_id(String log_id) {
		this.log_id = log_id;
	}

}
