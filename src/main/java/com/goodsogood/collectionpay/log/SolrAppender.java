/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements. See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache license, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the license for the specific language governing permissions and
 * limitations under the license.
 */
package com.goodsogood.collectionpay.log;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;

import org.apache.logging.log4j.core.Appender;
import org.apache.logging.log4j.core.Filter;
import org.apache.logging.log4j.core.Layout;
import org.apache.logging.log4j.core.LogEvent;
import org.apache.logging.log4j.core.appender.AbstractAppender;
import org.apache.logging.log4j.core.appender.AbstractOutputStreamAppender;
import org.apache.logging.log4j.core.appender.AppenderLoggingException;
import org.apache.logging.log4j.core.config.plugins.Plugin;
import org.apache.logging.log4j.core.config.plugins.PluginBuilderAttribute;
import org.apache.logging.log4j.core.config.plugins.PluginBuilderFactory;
import org.apache.logging.log4j.core.config.plugins.validation.constraints.Required;
import org.apache.solr.client.solrj.SolrClient;
import org.apache.solr.client.solrj.impl.HttpSolrClient;
import org.apache.solr.client.solrj.response.UpdateResponse;
import org.slf4j.MDC;

import com.goodsogood.collectionpay.util.UUIDUtils;
import com.google.common.util.concurrent.ThreadFactoryBuilder;

/**
 * 一个自定义的log4j Appender，用于将打印的日志上传至solr服务器。
 * 
 * @Description
 * <AUTHOR>
 * @time 2017年6月23日上午11:51:56
 */
@Plugin(name = "Solr", category = "Core", elementType = Appender.ELEMENT_TYPE, printObject = true)
public final class SolrAppender extends AbstractAppender {
	
	private static final ThreadFactory threadFactory = new ThreadFactoryBuilder()
	        .setNameFormat("SolrAppender LogSendThread threadPool-%d")
	        .setDaemon(true)
	        .build();
	
	private static final  ExecutorService threadPool = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors()*2,threadFactory);
	
	private final int delay = 2000 ;

	private final static LinkedBlockingQueue<SolrLog> logQueue = new LinkedBlockingQueue<SolrLog>(3000);

	protected SolrAppender(final String name, final Filter filter, final Layout<? extends Serializable> layout,
			final String solrServer, final String moduleName) {
		super(name, filter, layout, false);
		this.solrServer = solrServer;
		this.moduleName = moduleName;
		new LogSendThread().start();
	}

	class LogSendThread extends Thread {
	
		@Override
		public void run() {
			for (;;) {
				try {
					final List<SolrLog> logbuffer = new ArrayList<SolrLog>();
					while (logQueue.size() > 0) {
						logbuffer.add(logQueue.take());
					}
					if (logbuffer.size() > 0) {
						threadPool.submit(new Runnable() {
							public void run() {
								uploadLogs(logbuffer);
							}
						});
					}
					Thread.sleep(delay);
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
			}
		}
		
		final SolrClient client  = new HttpSolrClient.Builder(solrServer).build();
		private void uploadLogs(List<SolrLog> logbuffer) {
			try {
				client.addBeans(logbuffer);
				UpdateResponse commitResp = client.commit();
				System.out.println("SolrAppender uploadLogs! " + commitResp.getStatus() + ","
						+ commitResp.getRequestUrl() + "," + commitResp.getResponse()+",logbuffer size="+logbuffer.size());
			} catch (Exception e) {
				System.err.println("Log upload Solr Server["+solrServer+"] failed! " + e.getMessage()+","+Thread.currentThread());
			} finally {
					logbuffer.clear();
			}
		}
	}

	@Override
	public void append(final LogEvent event) {
		try {
			SolrLog log = new SolrLog();
			log.setLog_id(UUIDUtils.uuid32());
			log.setReq_id(MDC.get("req_id") != null ? MDC.get("req_id").toString() : "");
			log.setLevel(event.getLevel().name());
			log.setClass_name(event.getSource().toString());
			log.setMessage(new String(getLayout().toByteArray(event), "utf-8"));
			log.setModule_name(moduleName);
			log.setTime(new Date(event.getTimeMillis()));
			log.setLogger_name(event.getLoggerName());
			///log.setThread_name(event.getThreadName());
			logQueue.put(log);
		} catch (final AppenderLoggingException | UnsupportedEncodingException | InterruptedException ex) {
			super.error(ex.getMessage(), ex);
		}
	}

	@PluginBuilderFactory
	public static <B extends Builder<B>> B newBuilder() {
		return new Builder<B>().asBuilder();
	}

	/**
	 * Builds ConsoleAppender instances.
	 * 
	 * @param <B>
	 *            This builder class
	 */
	public static class Builder<B extends Builder<B>> extends AbstractOutputStreamAppender.Builder<B>
			implements org.apache.logging.log4j.core.util.Builder<SolrAppender> {

		@PluginBuilderAttribute
		@Required
		private String solrServer;

		@PluginBuilderAttribute
		@Required
		private String moduleName;

		public B setSolrServer(final String solrServer) {
			this.solrServer = solrServer;
			return asBuilder();
		}

		public B setModuleName(final String moduleName) {
			this.moduleName = moduleName;
			return asBuilder();
		}

		@Override
		public SolrAppender build() {
			final Layout<? extends Serializable> layout = getOrCreateLayout();
			return new SolrAppender(getName(), getFilter(), layout, solrServer, moduleName);
		}
	}

	protected String solrServer;
	protected String moduleName;

	public String getModuleName() {
		return moduleName;
	}

	public void setModuleName(String moduleName) {
		this.moduleName = moduleName;
	}

	public String getSolrServer() {
		return solrServer;
	}

	public void setSolrServer(String solrServer) {
		this.solrServer = solrServer;
	}

}
