package com.goodsogood.collectionpay.consts;

/**
 * @Description 交易类型
 * <AUTHOR>
 * @time 2017年7月25日下午3:53:52
 */
public enum TradeTypeCode {
	
	MCH_GOLDBEAN_EXCHANGE(110003),//商家兑换金豆 
	/**
	 *线上，线下支付
	 */
	TRADE(100001), 
	/**
	 * 交易退款
	 */
	TRADE_REFUND(100002), 
	/**
	 *  开放平台第三方商家提款
	 */
	WITHDRAWALS(100003), 
	/**
	 * 个人用户间转账
	 */
	P2P_TRANSFER(100004), 
	/**
	 * 单位间转账
	 */
	COMPANY_TRANSFER(100005), 
	/**
	 *  单位发福利
	 */
	COMPANY_USER_TRANSFER(100006), 
	
	/**
	 *  单位发福利
	 */
	REDPACKET(100007),
	
	/**
	 *  用户充值
	 */
	USER_RECHARGE(200001), 
	/**
	 * 单位充值
	 */
	COMPANY_RECHARGE(200002), 
	/**
	 *  人工上账
	 */
	ASSET_OPERATE(200003),
	
	
	FINICE_GIFT(200004), //财务赠送
	
	//批量现金转账
	CASH_COMPANY_BATCH_TRANS (3000),
	CASH_COMPANY_BATCH_TRANS_ITEM (3001),
	
	//企业现金账户线下银行转账
	CASH_COMPANY_OFFLINE_DEPOSIT_TRANS(3002), 
	//企业现金账户在线支付充值
	CASH_COMPANY_ONLINE_DEPOSIT_TRANS(3003), 
	//分账
	CASH_COMPANY_EXCHANGE_TRANS(3004),
	CASH_USER_WITHDRAWALS(3005),
	
	//代收款
	COLLECTION_WX(4001),//微信
	COLLECTION_UNIONPAY(4002),//银联
    COLLECTION_MERCH(4003),//招商银行
    COLLECTION_CCB(4004),//建设银行
    COLLECTION_ALLINPAY(4005),//通联支付
    COLLECTION_ICBC(4006),//通联支付
    COLLECTION_DIGITAL(4008),//中行数字人民币
    COLLECTION_DIGITAL_CBC(4009),//建行数字人民币
    COLLECTION_H5PAY_ICBC(4010),//工行H5支付
    COLLECTION_H5PAY_ICBC_UNION(4011),//工行H5支付
    COLLECTION_H5PAY_ICBC_JFT(4012),//工行H5支付

    COLLECTION_SANXIAN(4013),//三峡付
	COLLECTION_WX_H5(4014),//微信H5支付
	BOC_RENAISSANCE(401601)//中行复兴一号支付
    ;
	
	TradeTypeCode(int value) {
		this.value = value;
	}

	private int value;

	public int getValue() {
		return value;
	}

	public void setValue(int value) {
		this.value = value;
	}

	public static TradeTypeCode findByValue(int value) {
		switch (value) {
		case 100001:
			return TRADE;
		case 100002:
			return TRADE_REFUND;
		case 100003:
			return WITHDRAWALS;
		case 100004:
			return P2P_TRANSFER;
		case 100005:
			return COMPANY_TRANSFER;
		case 100006:
			return COMPANY_USER_TRANSFER;
		case 100007:
			return REDPACKET;
		case 200001:
			return USER_RECHARGE;
		case 200002:
			return COMPANY_RECHARGE;
		case 200003:
			return ASSET_OPERATE;
		case 200004:
			return FINICE_GIFT;
		case 110003:
			return MCH_GOLDBEAN_EXCHANGE;
		case 3000:
			return CASH_COMPANY_BATCH_TRANS;
		case 3001:
			return CASH_COMPANY_BATCH_TRANS_ITEM ;
		case 3002:
			return CASH_COMPANY_OFFLINE_DEPOSIT_TRANS ;
		case 3003:
			return CASH_COMPANY_ONLINE_DEPOSIT_TRANS ;
		case 3004:
			return CASH_COMPANY_EXCHANGE_TRANS ;
		case 3005:
			return CASH_USER_WITHDRAWALS ;
		case 4001:
			return COLLECTION_WX ;
		case 4002:
			return COLLECTION_UNIONPAY ;
			
		default:
			return null;
		}
	}
	
	
}