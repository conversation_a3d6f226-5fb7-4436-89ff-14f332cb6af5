package com.goodsogood.collectionpay.consts;


/***
 * 1 绑定；0 解绑；（填写一位数字即可）
幂等交易；
如果商户结算账户数量达到上限时，有更换结算账户需求，此时需要先解绑存量的结算账户，再绑定新的结算账户。
 * @Description
 * <AUTHOR>
 * @time 2018年7月23日下午5:10:33
 */
public enum BankCardBindStatus {
	
	BINDED(1), UN_BINDED(0);


	BankCardBindStatus(int value) {
		this.value = value;
	}
	private int value;

	public int getValue() {
		return value;
	}

	public void setValue(int value) {
		this.value = value;
	}

	public static BankCardBindStatus findByValue(int value) {
		switch (value) {
		case 0:
			return UN_BINDED;
		case 1:
			return BINDED;
		default:
			return null;
		}
	}

}
