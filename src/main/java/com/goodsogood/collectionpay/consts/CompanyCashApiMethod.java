package com.goodsogood.collectionpay.consts;
/***
 *企业现金账户接口
 * @Description
 * <AUTHOR>
 * @time 2018年9月7日下午2:29:32
 */
public interface CompanyCashApiMethod {

	// 企业现金账户存款-撤销
	public static final String DepositCancel = "company.cashaccount.deposit.revoke";

	// 企业现金账户存款
	public static final String depositConfirm = "company.cashaccount.deposit";

	// 查询企业现金账户的账单
	public static final String accountBill = "company.cashaccount.bill";

	// 企业现金账户创建
	public static final String accountCreate = "company.cashaccount.create";
	// 查询企业现金账户信息
	public static final String accountInfo = "company.cashaccount.info";
	
	/**
	 * 在线冲值下单
	 */
	public static final String onlineRechargePrepay = "company.cashaccount.online.recharge.prepay"; 
	
	
	/***
	 * 存款订单信息查询
	 */
	public static final String depositOrderQuery = "company.cashaccount.deposit.query"; 
	

	/***
	 * 兑换
	 */
	public static final String exchangeTrans = "company.cashaccount.exchange";

	/***
	 * 批量转账
	 */
	public static final String batchTrans = "company.cashaccount.batch.trans";

	/***
	 * 批量转账查询
	 */
	public static final String queryBatchTrans = "company.cashaccount.batch.trans.query";

	/***
	 * 批量转账查询
	 */
	public static final String queryBatchTransDetail = "company.cashaccount.batch.trans.detail.query";

	/***
	 * 批量转账状态查询
	 */
	public static final String batchTransQuery = "company.cashaccount.batch.trans.query";

}
