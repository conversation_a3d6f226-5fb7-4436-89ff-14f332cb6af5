package com.goodsogood.collectionpay.consts;


/***
 * 企业现金账户流水类型
 * @Description
 * <AUTHOR>
 * @time 2018年8月28日下午3:14:09
 */
public class CompanyCashAcountFlowType {
	
	public static final String DEPOSIT="存款";
	public static final String DEPOSIT_CANCEL="存款撤销";
	public static final String SERVICE_FEE="手续费";
	public static final String COMPANY_C_FEE="购买企业福利";
	public static final String BATCH_TRANSFER = "批量打款";
	public static final String TRANSFER_CANCEL = "打款撤回";
	
	public static final String WITHDRAWALS = "提现";
	public static final String WITHDRAWALS_FAIL = "提现失败";

}
