package com.goodsogood.collectionpay.consts;

public class ErrorCode {
	public static final String ACCESS_FORBIDDEN = "20001";
	public static final String NO_SUCH_INTERFACE_SUPPORTED = "20002";
	public static final String HTTP_REQUEST_METHOD_NOT_SUPPORTED = "20012";
	public static final String CLIENT_IP_NOT_ALLOWED = "20003";
	public static final String REPLAY_ATTACKS = "20004";
	// 限流设置触发 
	public static final String RATE_LIMIT_TRIGGERING = "20005";
	//并发锁获取失败
	public static final String CONCURRENT_LOCK_GET_FAIL = "20006";
	
	public static final String MISSING_PARAMETER = "40001";
	public static final String INVALID_PARAMETER = "40002";
	public static final String BUSINESS_ERROR = "40004";
	
	public static final String BUSINESS_ERROR_TRADE_HAS_SUCCESS = "40004100";
	public static final String BUSINESS_ERROR_TRADE_HAS_CLOSE = "40004101";
	public static final String BUSINESS_ERROR_TRADE_NO_EXISTED = "40004102";
	public static final String BUSINESS_ERROR_TRADE_NOT_EXIST = "40004103"; 
	public static final String BUSINESS_ERROR_PAY_PWD_ERROR = "40004104";   
	public static final String BUSINESS_ERROR_TRADE_STATUS_ERROR = "40004105";  
	public static final String BUSINESS_ERROR_TRADE_INVALID_TRADE = "40004106"; 
	public static final String BUSINESS_ERROR_BUYER_BALANCE_NOT_ENOUGH = "40004107";  
	public static final String BUSINESS_ERROR_PAYMENT_FAIL = "40004108"; 
	public static final String BUSINESS_ERROR_SELLER_BEEN_BLOCKED = "40004109"; 
	public static final String BUSINESS_ERROR_SELLER_BALANCE_NOT_ENOUGH = "40004110"; 
	public static final String BUSINESS_ERROR_TRADE_HAS_FINISHED = "40004111"; 
	public static final String BUSINESS_ERROR_REFUND_AMT_NOT_EQUAL_TOTAL="40004112";//退款金额不等于了交易总金额 
	public static final String BUSINESS_ERROR_TRADE_REFUND_FEE_ERR="********";//退款金额无效
	public static final String BUSINESS_ERROR_TRADE_REFUND_DISCORDANT_REPEAT_REQUEST="********"; //退款请求号重复
	public static final String BUSINESS_ERROR_PAYMENT_AUTH_CODE_INVALID="********";//付款码失效
	public static final String BUSINESS_ERROR_PAYMENT_AUTH_CODE_EXPRESSED ="********";//付款码过期了
	public static final String BUSINESS_ERROR_TRANSFER_IN_ACCOUNT_NO_EXISTED ="********";//转账失败，收款人账户不存在。
	public static final String BUSINESS_ERROR_TRADE_NOT_REFUNDABLE ="********";//订单不支持退款
	
	public static final String BUSINESS_ERROR_TRADE_SETTLED ="********";//交易已经结算。
	public static final String BUSINESS_ERROR_TRADE_SETTLE_FEE_ERR ="********";//结算金额无效
	
	public static final String PAYMENT_AUTH_CODE_INVALID ="40005";//支付授权码无效
	
	/**
	 * 浙商银行快捷支付错误 
	 */
	public static final String CZ_FAST_PAY_ERR="50000"; 
	//没有参加充100送100活动资格
	public static final String CZ_FAST_PAY_ACTIVITY_NO_QUALIFY_ERR="50001";
	
	public static final String SERVICE_UNAVAILABLE="20000";
	
	
}
