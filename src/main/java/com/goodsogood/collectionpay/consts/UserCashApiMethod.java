package com.goodsogood.collectionpay.consts;

public interface UserCashApiMethod {
	
	// 查询个人现金账户的账单
	public static final String accountBill = "user.cashaccount.bill";
	
	// 个人现金账户创建
	public static final String accountCreate = "user.cashaccount.create";
	
	// 个人现金账户创建
	public static final String accountBatchCreate = "user.cashaccount.batch.create";
	
	
	// 查询个人现金账户信息
	public static final String accountInfo = "user.cashaccount.info";
	
	// 绑定银行卡
	public static final String bindBankCard = "user.cashaccount.bankcard.bind";
	
	// 解绑银行卡
	public static final String disbindBankCard = "user.cashaccount.bankcard.disbind";
	
	// 查询银行卡列表
	public static final String bankCardList = "user.cashaccount.bankcard.list";
	
	///提现
	public static final String withdrawalsCreate = "user.cashaccount.withdrawals.create";
	
	///提现列表
	public static final String withdrawalsList= "user.cashaccount.withdrawals.list";
	

}
