package com.goodsogood.collectionpay.consts;

/**
 * @Description 充值渠道 // 1 支付宝 2 微信 3 银联 4 礼品卡充值 5 易宝,6 浙商银行,6 线下转账汇款
 * <AUTHOR>
 * @time 2017年7月25日下午3:53:52
 */
public enum RechargeChannelType {
	ALIPAY(1), WEIXIN(2), UNIONPAY(3), GIFTCARD(4), YEEPAY(5), CZBANK(6), OFFLINE_TRANSFER(7);
	public final static String[] NAME = new String[]{ "支付宝" , "微信" , "银联" , "礼品卡" , "易宝","浙商银行信用卡" ,"线下转账汇款"} ;

	RechargeChannelType(int value) {
		this.value = value;
	}
	private int value;

	public int getValue() {
		return value;
	}

	public void setValue(int value) {
		this.value = value;
	}

	public static RechargeChannelType findByValue(int value) {
		switch (value) {
		case 1:
			return ALIPAY;
		case 2:
			return WEIXIN;
		case 3:
			return UNIONPAY;
		case 4:
			return GIFTCARD;
		case 5:
			return YEEPAY;
		case 6:
			return CZBANK;
		case 7:
			return OFFLINE_TRANSFER;
		default:
			return null;
		}
	}
}