package com.goodsogood.collectionpay.consts;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.goodsogood.collectionpay.util.ResourceUtil;

/***
 * 
 * @Description 银行代码数据字典
 * <AUTHOR>
 * @time 2018年8月14日上午10:17:50
 */
public class BankTypeDict {
	private static final Logger logger = LoggerFactory.getLogger(BankTypeDict.class);
	private static final Charset UTF8 = Charset.forName("UTF-8");
	private static final Map<String, String> bankNameMap = new HashMap<>();
	private static final Map<String, String> cardTypeMap = new HashMap<>();
	static {
		try {
			InputStream in = ResourceUtil.getClassPathResource("config/bankcard_type.csv");
			String str = IOUtils.toString(new InputStreamReader(in, UTF8));
			String[] lines = str.split("\n");
			for (String line : lines) {
				String[] col = line.split(",");
				String bankName = col[0];
				String prefix = col[1];
				String type = col[2];
				bankNameMap.put(prefix, bankName);
				cardTypeMap.put(prefix, type);
			}
		} catch (IOException e) {
			logger.error("BankDict init err:", e);

		}
	}

	public static String getBankName(String cardNo) {
		String name = null;
		for (int i = 11; i > 4 && name == null; i--) {
			name = bankNameMap.get(cardNo.substring(0, i));
			if (name != null)
				return name.replace("\r", "");
		}
		return name;
	}

	public static String getBankcardType(String cardNo) {
		String type = null;
		for (int i = 11; i > 4 && type == null; i--) {
			type = cardTypeMap.get(cardNo.substring(0, i));
			if (type != null)
				return type.replace("\r", "");
		}
		return type;
	}
	
	public static void main(String[] args) {
		String str="**********";
		System.out.println(str.substring(0, 5));
	}

}
