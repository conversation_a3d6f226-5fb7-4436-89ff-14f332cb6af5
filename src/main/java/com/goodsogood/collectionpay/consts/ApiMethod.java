package com.goodsogood.collectionpay.consts;

public class ApiMethod {

	// 下单
	public static final String TRADE_CREATE = "collection.trade.create";

	// 查询
	public static final String TRADE_QUERY = "collection.trade.query";

	public static final String TRADE_REFUND = "collection.trade.refund";

    /**
     * 手支重新更新结果
     */
    public static final String MANUAL_UPDATE = "manual.update";

	public static final String TRADE_LIST_QUERY = "collection.trade.list";

	public static final String TRADE_LIST_EXPORT = "collection.trade.list.export";

	public static final String TRADE_REFUND_LIST_QUERY = "collection.trade.refund.list";
	public static final String TRADE_REFUND_LIST_EXPORT = "collection.trade.refund.list.export";

}

