package com.goodsogood.collectionpay.consts;

public class MchAccountFlowBusinessType {
	/**
	 * 1:交易收款
	 */
	public static final String TRADE = "1";
	/**
	 * 2：交易退款
	 */
	public static final String TRADE_REFUND = "2";
	/**
	 * 3：收取交易服务费
	 */
	public static final String TRADE_SERVICE_CHARGE = "3";
	/**
	 * 4:退还服务费
	 */
	public static final String TRADE_SERVICE_CHARGE_REFUND = "4";
	/**
	 * 5:提款
	 */
	public static final String FUND_EXTRACT = "5";

	// 金豆兑换
	public static final String GOLDBEAN = "6";
}