package com.goodsogood.collectionpay.consts;

import com.google.common.collect.ImmutableMap;
import lombok.Getter;

/**
 * 签名方式
 *
 * <AUTHOR>
 **/
@Getter
public enum SignType {
    MD5("MD5", "MD5"),
    RSA256("RSA256", "RSA256"),
    ;

    private String type;
    private String desc;

    public final static ImmutableMap<String, SignType> SIGN_TYPE_MAP = ImmutableMap.of(
            SignType.MD5.getType(), SignType.MD5,
            SignType.RSA256.getType(), SignType.RSA256
    );


    SignType(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
