package com.goodsogood.collectionpay.consts;

public interface CompanyCashDepositConsts {
	// 0:等待支付;1:支付成功;2:财务已确认并已上账;3:已退款
	public static final int ORDER_STATUS_WAIT_PAY = 0;
	public static final int ORDER_STATUS_PAY_SUCCESS = 1;
	public static final int ORDER_STATUS_FINANCE_CONFIRM_DEPOSIT = 2;
	public static final int ORDER_STATUS_FINANCE_DEPOSIT_REVOKE = 3;

	// 0:在线支付;1:线下汇款
	public static final int PAY_WAY_ONLINE = 0;
	public static final int PAY_WAY_OFFLINE = 1;
}
