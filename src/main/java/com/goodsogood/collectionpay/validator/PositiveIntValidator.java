package com.goodsogood.collectionpay.validator;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import org.apache.commons.lang3.StringUtils;

import com.goodsogood.collectionpay.annotation.PositiveInt;

public class PositiveIntValidator implements ConstraintValidator<PositiveInt, String> {
	
	private int max ;

	@Override
	public void initialize(PositiveInt constraintAnnotation) {
		max = constraintAnnotation.max();

	}

	@Override
	public boolean isValid(String value, ConstraintValidatorContext context) {
		return StringUtils.isEmpty(value) || ( StringUtils.isNumeric(value) && value.length() <=11 && Long.valueOf(value).longValue() >= 0L && Long.valueOf(value).longValue() <= max );
	}

}
