package com.goodsogood.collectionpay.validator;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import org.apache.commons.lang3.StringUtils;

import com.goodsogood.collectionpay.annotation.Money;

public class MoneyValidator implements ConstraintValidator<Money, String> {

	@Override
	public void initialize(Money constraintAnnotation) {
		// TODO Auto-generated method stub

	}

	@Override
	public boolean isValid(String value, ConstraintValidatorContext context) {
		
		return StringUtils.isEmpty(value) || ( StringUtils.isNumeric(value) && Long.valueOf(value).longValue() > 0L );
	}

}
