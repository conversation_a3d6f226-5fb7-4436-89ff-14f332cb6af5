package com.goodsogood.collectionpay.conf;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * Redis 锁的连接池配置
 * 
 * @Description
 * <AUTHOR>
 * @time 2017年9月21日上午9:52:57
 */
//@Configuration
public class RedisLockConfiger {

	public final static long DEFAULT_TRY = 2000L; //默认尝试时间(毫秒) 
	public final static long SHORT_TRY = 1000L ;  //短尝试时间(毫秒) 

	@Value("${lock.redis.address}")
	private String address;
	@Value("${lock.redis.password}")
	private String password;
	@Value("${lock.redis.port}")
	private int port;
	@Value("${lock.redis.maxIdel}")
	private int maxIdel;
	@Value("${lock.redis.maxTotal}")
	private int maxTotal;
	@Value("${lock.redis.timeout}")
	private int timeout;

	/**
	 * @return the address
	 */
	public String[] getAddress() {
		return this.address.split(",");
	}

	/**
	 * @return the password
	 */
	public String getPassword() {
		return password;
	}

	/**
	 * @return the port
	 */
	public int getPort() {
		return port;
	}

	/**
	 * @return the maxIdel
	 */
	public int getMaxIdel() {
		return maxIdel;
	}

	/**
	 * @return the maxTotal
	 */
	public int getMaxTotal() {
		return maxTotal;
	}

	/**
	 * @return the timeout
	 */
	public int getTimeout() {
		return timeout;
	}

}
