package com.goodsogood.collectionpay.conf;

import java.io.BufferedInputStream;
import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardWatchEventKinds;
import java.nio.file.WatchEvent;
import java.util.Properties;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.goodsogood.collectionpay.consts.LogConfiger;
import com.goodsogood.collectionpay.util.FileWatchUtil;
import com.goodsogood.collectionpay.util.WatchCallback;

/**
 * 配置文件加载器
 * <AUTHOR>
 *
 */
public class MessageLoader {
    
    static final Logger logger = LoggerFactory.getLogger(LogConfiger.APP);
    
    private static  String configFile = "classpath:config/application-message-zh-cn.properties";
    
    private final static Properties properties = new Properties();
    
    private static MessageLoader instance = null;
    
    private static synchronized void initInstance() {
        if (instance == null) {
            instance = new MessageLoader();
            load();
			try {
				Path myDir = Paths.get(org.springframework.util.ResourceUtils.getURL(configFile).toURI());
				FileWatchUtil.watch(myDir, new WatchCallback() {
					@Override
					public void call(WatchEvent<?> event) {
						logger.info("--Refresh file -" + event.context() + "," + event.kind());
						load();
					}

				}, StandardWatchEventKinds.ENTRY_MODIFY);
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
    		
        }
    }
    
    public static String getMessage(String key) {

        if (key == null || "".equals(key)) {
            return "";
        }

        if (instance == null) {
            initInstance();
        }
        
        return properties.getProperty(key);
    }
    
    public static String getMessage(String key,Object ... args) {

        if (key == null || "".equals(key)) {
            return "";
        }

        if (instance == null) {
            initInstance();
        }
        return  String.format(properties.getProperty(key), args);
    }
    
    private static void load() {
        
        BufferedReader br = null;
        InputStream input = null;
        try {
        	 input = new BufferedInputStream(new FileInputStream(org.springframework.util.ResourceUtils.getURL(configFile).toString()));
            //input = MessageLoader.class.getClassLoader().getResourceAsStream(configFile);
            org.springframework.util.ResourceUtils.getURL(configFile);
            
            if(input == null) {
                logger.error("properties file {} don't exist:", configFile);
                return;
            }
            
//            File f = new File(configFile);
//            if(!f.exists()) {
//                logger.error("properties file {} don't exist:", configFile);
//                return;
//            }
//            input = new FileInputStream(configFile);
            
            br = new BufferedReader(new InputStreamReader(input, "UTF-8"));
            properties.load(br);
            logger.info("*********************message-zh-cn.properties result*********************");
            for (Object key : properties.keySet()) {
                String keyStr = key.toString();
                String value = properties.getProperty(keyStr);
                logger.info("spring parse config properties key is {}, value is {}",keyStr,value);
            }
            logger.info("*********************message-zh-cn.properties result*********************");
        } catch (IOException e) {
            logger.error("load properties fail :", e);
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    //ignore
                }
            }
            if (input != null) {
                try {
                    input.close();
                } catch (IOException e) {
                    //ignore
                }
            }
        }
    }
}
