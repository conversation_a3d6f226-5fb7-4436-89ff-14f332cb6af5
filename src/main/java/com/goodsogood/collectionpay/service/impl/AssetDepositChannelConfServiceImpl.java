package com.goodsogood.collectionpay.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.goodsogood.collectionpay.dao.AssetDepositChannelConfDao;
import com.goodsogood.collectionpay.entity.AssetDepositChannelConfEntity;
import com.goodsogood.collectionpay.service.AssetDepositChannelConfService;


@Service("assetDepositChannelConfService")
public class AssetDepositChannelConfServiceImpl implements AssetDepositChannelConfService {
	
	@Autowired
	private AssetDepositChannelConfDao assetDepositChannelConfDao;

	@Override
	public Map<String, String> findOptionsByChannel(String channel) {
		
		List<AssetDepositChannelConfEntity> opts = assetDepositChannelConfDao.findByChannel(channel);
		if(opts.isEmpty()){
			return null ; 
		}
		Map<String, String> Option = new HashMap<>();
		
		for(AssetDepositChannelConfEntity item : opts){
			Option.put(item.getOptionKey(), item.getOptionValue());
		}
		return Option;
	}

	@Override
	public Map<String, String> findOptionsByChannelAndKey(String group, String key) {
		
		List<AssetDepositChannelConfEntity> opts = assetDepositChannelConfDao.findByChannelAndOptionKey(group,key);
		if(opts.isEmpty()){
			return null ; 
		}
		Map<String, String> Option = new HashMap<>();
		
		for(AssetDepositChannelConfEntity item : opts){
			Option.put(item.getOptionKey(), item.getOptionValue());
		}
		return Option;
	}

}
