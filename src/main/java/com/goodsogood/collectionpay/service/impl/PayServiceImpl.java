package com.goodsogood.collectionpay.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.github.kevinsawicki.http.HttpRequest;
import com.goodsogood.collectionpay.consts.*;
import com.goodsogood.collectionpay.dao.CollectionDealerAccountDao;
import com.goodsogood.collectionpay.dao.CollectionDealerAccountLogDao;
import com.goodsogood.collectionpay.dao.CollectionDealerTradeDao;
import com.goodsogood.collectionpay.dao.CollectionPayPageConfigDao;
import com.goodsogood.collectionpay.dao.support.PageResult;
import com.goodsogood.collectionpay.entity.CollectionDealerAccountEntity;
import com.goodsogood.collectionpay.entity.CollectionDealerAccountLogEntity;
import com.goodsogood.collectionpay.entity.CollectionDealerTradeEntity;
import com.goodsogood.collectionpay.entity.CollectionPayPageConfigEntity;
import com.goodsogood.collectionpay.exception.AssetServiceException;
import com.goodsogood.collectionpay.model.PayPageConfigVo;
import com.goodsogood.collectionpay.model.request.*;
import com.goodsogood.collectionpay.model.response.ExportResponse;
import com.goodsogood.collectionpay.model.response.TradeCreateResponse;
import com.goodsogood.collectionpay.model.response.UnionpayOrderQueryResponse;
import com.goodsogood.collectionpay.redis.RedisPool;
import com.goodsogood.collectionpay.service.*;
import com.goodsogood.collectionpay.unionpay.sdk.AcpService;
import com.goodsogood.collectionpay.unionpay.sdk.LogUtil;
import com.goodsogood.collectionpay.util.*;
import com.icbc.api.DefaultIcbcClient;
import com.icbc.api.IcbcApiException;
import com.icbc.api.IcbcConstants;
import com.icbc.api.request.CardbusinessAggregatepayB2cOnlineOrderqryRequestV1;
import com.icbc.api.request.QrcodeQueryRequestV2;
import com.icbc.api.request.QrcodeQueryRequestV2.QrcodeQueryRequestV2Biz;
import com.icbc.api.response.CardbusinessAggregatepayB2cOnlineOrderqryResponseV1;
import com.icbc.api.response.QrcodeQueryResponseV2;
import com.icbc.api.utils.IcbcSignature;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.Jedis;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


@Service("payService")
public class PayServiceImpl implements PayService {

	private static final Logger logger = LoggerFactory.getLogger(LogConfiger.APP);

    /**
     * 支付配置列表的key
     */
    private final String PAYCONFIGLISTKEY="PAYCONFIGLISTKEY";

	@Autowired
	private AssetDepositChannelConfService assetDepositChannelConfService ;
	@Autowired
	private CollectionDealerTradeDao collectionDealerTradeDao ;
	@Autowired
	private TradeNoGenerater tradeNoGenerater ;
	@Autowired
	private CollectionDealerAccountDao collectionDealerAccountDao ;
	@Autowired
	private CollectionDealerAccountLogDao collectionDealerAccountLogDao ;
	@Autowired
	private NotifyService notifyService;
    @Autowired
    private FileServices fileServices;

    @Autowired
    private SanXianService sanXianService;

    @Autowired
    private CollectionPayPageConfigDao collectionPayPageConfigDao;

    @Autowired
    private RedisPool redisPool ;

    @Autowired
    private ABCBankService ABCBankService;

    @Autowired
    private  SybPayService sybPayService ;

    @Autowired
    private OssPostObject ossPostObject ;

    @Autowired
    private WenXinServices wenXinServices;

    @Autowired
    private AlipayService alipayService;

    @Autowired
    private MerchantsServices merchantsServices;

    @Autowired
    private CCBService ccbService;

    @Autowired
    private ICBCBankService icbcBankService;

	@Autowired
	private WeiXinH5Services weiXinH5Services;

	@Autowired
	private BocRenaissanceServices bocRenaissanceServices;

	@SuppressWarnings("unchecked")
	@Override
	@Transactional
	public TradeCreateResponse tradeCreate(TradeCreateRequest request,
                                           HttpServletRequest req, HttpServletResponse rep) throws Exception {
		TradeCreateResponse resp = new TradeCreateResponse();
		if(!org.springframework.util.StringUtils.isEmpty(request.getOutTradeNo())){
			List<CollectionDealerTradeEntity>  existedList =
					this.collectionDealerTradeDao.findByOutTradeNoAndDealerId(request.getOutTradeNo(),
                            Integer.parseInt(request.getDealerId()));
			if (existedList.size()>0) {
				throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "业务单号已存在!");
			}
		}
		//如果是复兴一号要从单独处理
		if(request.getChannel().startsWith("BOC_RENAISSANCE_")){
			request.setChannelNew(request.getChannel());
			String resetChannel = StrUtil.subBefore(request.getChannel(), "_", true);
			request.setChannel(resetChannel);
		}

		CollectionDealerTradeEntity order = new CollectionDealerTradeEntity();
		order.setOutTradeNo(request.getOutTradeNo());
		Map<String, String> config = assetDepositChannelConfService.findOptionsByChannel(request.getChannel());
		if (config==null || config.isEmpty()) {
			throw new AssetServiceException(ErrorCode.BUSINESS_ERROR,
					"未找到支付渠道配置:" + request.getChannel());
		}
		if (!Boolean.parseBoolean(config.get("enabled"))) {
			throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, request.getChannel()+"-支付渠道不可用.");
		}
		logger.info("Channel config={}",JSON.toJSONString(config, true));
		///中行公众号支付
		if (request.getChannel().startsWith("BOC_WXPAY_")) {
            wenXinServices.createResponseBOC(resp,request,order,config);
        }

        ///中行数字人民币支付
        if (request.getChannel().startsWith("BOC_DIGITAL_")) {
            wenXinServices.createResponseBOCDIGITAL(resp,request,order,config);
        }

        ///中行-支付宝支付
        if (request.getChannel().startsWith("BOC_WEB_")) {
            wenXinServices.createResponseBOCWEB(resp,request,order,config);
        }

		// 工商银行微信-沙坪坝区=ICBC_WX_SPB
		else if (request.getChannel().startsWith("ICBC_WX_")){
            wenXinServices.createResponseCBC(resp,request,order,config);
        }

        // 工商银行 H5支付宝支付
        else if (request.getChannel().startsWith("ICBC_H5_")){
            wenXinServices.createResponseCBCH5Alipay(resp,request,order,config);
        }

        // 工商银行 H5支付 目前支持微信支付
        else if (request.getChannel().startsWith("ICBC_H5PAY_")){
            wenXinServices.createResponseCBCH5(resp,request,order,config);
        }

        // 工商下单聚合支付 实现微信公众号与微信小程序 支付宝生活号等
        else if (request.getChannel().startsWith("ICBC_UNIONPAY_")){
            icbcBankService.createResponseCBCUNIONPAY(resp,request,order,config);
        }

		// 农业银行网上支付平台- 云阳县党费缴纳专用 : ABC_WX_CQYUNYANG
		else if (request.getChannel().startsWith("ABC_WX_")){
            wenXinServices.createResponseABC(resp,request,order,config);
        }
		// 通联支付
		else if (request.getChannel().startsWith("ALLINPAY_WX_")){
            wenXinServices.createResponseALLINPAY(resp,request,order,config);
        }
		//银联支付
        else if (request.getChannel().startsWith("UNIONPAY")){
            wenXinServices.createResponseUNIONPAY(resp,request,order,config);
        }
		//通用的微信支付
		else if(request.getChannel().startsWith("WXPAY_")){
           return wenXinServices.createResponseWXPAY(resp,request,order,config);
        }
		//通用的支付宝
        else if(request.getChannel().startsWith("ALIPAY_")){
           return alipayService.createResponse(resp,request,order,config);
        }

        //通用的支付宝手机端H5
        else if(request.getChannel().startsWith("ALIPAYWEB_")){
            return alipayService.createResponseWeb(resp,request,order,config);
        }

        //通用的支付宝-钉钉适配
        else if(request.getChannel().startsWith("DingDing_")){
            return alipayService.createDingDingResponse(resp,request,order,config);
        }
		//招商银行
        else if(request.getChannel().startsWith("MERCH_")){
            return merchantsServices.createResponseMerchants(resp,request,order,config);
        }
        //招商银行数字人民币
        else if(request.getChannel().startsWith("MERCHDIGITAL_")){
            return merchantsServices.createResponseMERCHDIGITAL(resp,request,order,config);
        }
        //中国建设银行H5支付
        else if(request.getChannel().startsWith("CCB_H5_")){
            return ccbService.buildOrder(resp,request,order,config);
        }
        //建行数字人民币支付
        else if (request.getChannel().startsWith("CBC_DIGITAL_")) {
           return ccbService.createResponseCBCDIGITAL(resp,request,order,config);
        }
        //三峡付
        else if (request.getChannel().startsWith("SAN_XIAN_PAY_")) {
            return sanXianService.buildOrder(resp,request,order,config);
        }
        //工行聚富通
        else if (request.getChannel().startsWith("ICBC_JFT_")){
            icbcBankService.createResponseJFTPAY(resp,request,order,config,req);
        }else if(request.getChannel().startsWith("WX_H5_PAY_")){
			weiXinH5Services.createPayOrder(resp,request,order,config);
		}
		//中行复兴一号
		if(request.getChannel().startsWith("BOC_RENAISSANCE_")){
			bocRenaissanceServices.buildOrder(resp,request,order,config);
		}
		return resp;
	}

	private UnionpayOrderQueryResponse queryUnionPayOrder(Map<String, String> config,
                                                          String tradeNo,String txnTime){
		String queryUrl=config.get("acpsdk.singleQueryUrl");
		String merId = config.get("merId");
		String version = config.get("acpsdk.version");
		String signMethod = config.get("acpsdk.signMethod");
		String frontUrl = config.get("acpsdk.frontUrl");
		String backUrl = config.get("acpsdk.backUrl");
		String frontTransUrl = config.get("acpsdk.frontTransUrl");
		String signCertPath = config.get("acpsdk.signCert.path");
		String signCertPwd = config.get("acpsdk.signCert.pwd");
		String encoding = "UTF-8";
		Map<String, String> data = new HashMap<String, String>();
		/***银联全渠道系统，产品参数，除了encoding自行选择外其他不需修改***/
		data.put("version", version);                 //版本号
		data.put("encoding", encoding);               //字符集编码 可以使用UTF-8,GBK两种方式
		data.put("signMethod", signMethod); //签名方法
		data.put("txnType", "00");                             //交易类型 00-默认
		data.put("txnSubType", "00");                          //交易子类型  默认00
		data.put("bizType", "000201");                         //业务类型 B2C网关支付，手机wap支付

		/***商户接入参数***/
		data.put("merId", merId);                  //商户号码，请改成自己申请的商户号或者open上注册得来的777商户号测试
		data.put("accessType", "0");                           //接入类型，商户接入固定填0，不需修改

		/***要调通交易以下字段必须修改***/
		data.put("orderId", tradeNo);                 //****商户订单号，每次发交易测试需修改为被查询的交易的订单号
		data.put("txnTime", txnTime);                 //****订单发送时间，每次发交易测试需修改为被查询的交易的订单发送时间

		/**请求参数设置完毕，以下对请求参数进行签名并发送http post请求，接收同步应答报文------------->**/

		Map<String, String> reqData = AcpService.sign(data,encoding);//报文中certId,signature的值是在signData方法中获取并自动赋值的，只要证书配置正确即可。

		String url = queryUrl;// 交易请求url从配置文件读取对应属性文件acp_sdk.properties中的 acpsdk.singleQueryUrl
		//这里调用signData之后，调用submitUrl之前不能对submitFromData中的键值对做任何修改，如果修改会导致验签不通过
		Map<String, String> rspData = AcpService.post(reqData,url,encoding);
		UnionpayOrderQueryResponse resp = JSON.parseObject(JSON.toJSONString(rspData), UnionpayOrderQueryResponse.class);
		/**对应答码的处理，请根据您的业务逻辑来编写程序,以下应答码处理逻辑仅供参考------------->**/
		//应答码规范参考open.unionpay.com帮助中心 下载  产品接口规范  《平台接入接口规范-第5部分-附录》
		if(!rspData.isEmpty()){
			if(AcpService.validate(rspData, encoding)){
				LogUtil.writeLog("验证签名成功");
				if("00".equals(rspData.get("respCode"))){//如果查询交易成功
					//处理被查询交易的应答码逻辑
					String origRespCode = rspData.get("origRespCode");
					if("00".equals(origRespCode)){
						//交易成功，更新商户订单状态
						//TODO
					}else if("03".equals(origRespCode) ||
							 "04".equals(origRespCode) ||
							 "05".equals(origRespCode)){
						//需再次发起交易状态查询交易
						//TODO
					}else{
						//其他应答码为失败请排查原因
						//TODO
					}
				}else{//查询交易本身失败，或者未查到原交易，检查查询交易报文要素
					//TODO
				}
			}else{
				LogUtil.writeErrorLog("验证签名失败");
				//TODO 检查验证签名失败的原因
			}
		}else{
			//未返回正确的http状态
			LogUtil.writeErrorLog("未获取到返回报文或返回http状态码非200");
		}
		 return resp ;
	}

	//Map<String, String> resultMap
	private Map<String, String> queryWxOrder(Map<String, String> config,String tradeNo) throws Exception{
		String  defaultUrl="https://api.mch.weixin.qq.com/pay/orderquery";
		SortedMap<Object, Object> parameters = new TreeMap<Object, Object>();

		String appId = config.get("appId");// WeixinConfig.AppId; //微信APPID
		String mch_id = config.get("mchId");// WeixinConfig.mch_id; //商户ID
		String merchatKey = config.get("merchatKey");// WeixinConfig.merchatKey;
		String nonce_str = UUIDUtils.uuid32();
		String tradeQueryUrl = config.get("tradeQueryUrl");
		if(org.springframework.util.StringUtils.isEmpty(tradeQueryUrl)){
			tradeQueryUrl = defaultUrl ;
		}
		parameters.put("appid", appId);
		parameters.put("mch_id", mch_id);
		parameters.put("nonce_str", nonce_str);
		parameters.put("out_trade_no", tradeNo);

		String sign = WeChatUtil.createSign(parameters, merchatKey);

		StringBuilder requestStr = new StringBuilder("<xml>");
		requestStr.append("<appid>");
		requestStr.append(appId);
		requestStr.append("</appid>");

		requestStr.append("<mch_id>");
		requestStr.append(mch_id);
		requestStr.append("</mch_id>");

		requestStr.append("<nonce_str>");
		requestStr.append(nonce_str);
		requestStr.append("</nonce_str>");

		requestStr.append("<out_trade_no>");
		requestStr.append(tradeNo);
		requestStr.append("</out_trade_no>");

		requestStr.append("<sign>");
		requestStr.append(sign);
		requestStr.append("</sign>");
		requestStr.append("</xml>");

		logger.info("wxRequest: " + requestStr.toString());
		// TODO http
		HttpRequest request =
				HttpRequest.post(tradeQueryUrl).
                        contentType("application/xml","UTF-8").
                        trustAllCerts().trustAllHosts().send(requestStr);
		String respBody = request.body();
		logger.debug("微信支付查询订单返回:\n" + respBody);
		Map<String, String> resultMap = XMLUtil.parseXml(respBody);
		return resultMap;
	}



	@Override
	@Transactional
	public String wxCallBack(Map<String, String> param) {
		logger.info("WxCallBack param str={}",JSON.toJSONString(param, true));
		String outTradeNo=param.get("out_trade_no") ;
		CollectionDealerTradeEntity oriOrder = collectionDealerTradeDao.findByTradeNo(outTradeNo);

		if(oriOrder==null){
			logger.info("商户订单号["+outTradeNo+"]不存在!");
            return "FAIL";
        }

		if(oriOrder.getStatus() ==TradeStatus.SUCCESS){
            logger.info("订单["+outTradeNo+"]已经支付成功!");
            return "SUCCESS";
		}

		logger.info("oriOrder={}",oriOrder.toString());
		Map<String, String> config = assetDepositChannelConfService.
				findOptionsByChannel(oriOrder.getChannelCode());
		if (config.isEmpty()) {
			throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "未找到支付渠道配置:" +
                    oriOrder.getChannelCode());
		}

		String merchatKey = config.get("merchatKey");
		// 验签
		SortedMap<Object, Object> parameters = new TreeMap<>();
		parameters.putAll(param);
		String rightSign = WeChatUtil.createSign(parameters, merchatKey);
		String paramSign = param.get("sign");
		String resultCode = param.get("result_code");
		String transactionId = param.get("transaction_id");

		long totalFee = Long.valueOf(param.get("total_fee"));

		if(!paramSign.equals(rightSign)){
			logger.info("验签失败");
			return "FAIL";
		}

		if("SUCCESS".equals(resultCode) && oriOrder.getAmount().longValue() == totalFee){
			oriOrder.setPayTime(new Date());
			oriOrder.setStatus(TradeStatus.SUCCESS);
			oriOrder.setStatusDes("已支付");
			oriOrder.setChannelTxnId(transactionId);
			oriOrder.setAttach(JSON.toJSONString(param, true));
			collectionDealerTradeDao.update(oriOrder);

			CollectionDealerAccountEntity dealerAccount =
					collectionDealerAccountDao.findByDealerId(oriOrder.getDealerId());

			long newBalance = dealerAccount.getBalance().longValue() + oriOrder.getAmount().longValue();

			dealerAccount.setBalance(newBalance);
			dealerAccount.setWxpayNum(dealerAccount.getWxpayNum().longValue() + 1);
			dealerAccount.setWxpaySum(dealerAccount.getWxpaySum().longValue() +
                    oriOrder.getAmount().longValue());
			collectionDealerAccountDao.update(dealerAccount);
			CollectionDealerAccountLogEntity log = new CollectionDealerAccountLogEntity() ;
			log.setAction("微信支付");
			log.setAmount(oriOrder.getAmount().longValue());
			log.setChannel("微信");
			log.setTradeNo(outTradeNo);
			log.setContent(oriOrder.getBody());
			log.setCreateTime(new Date());
			log.setDealerBalance(newBalance);
			log.setInOrOut(FlowType.IN+"");
			log.setDealerId(oriOrder.getDealerId());
			log.setLogId(UUIDUtils.uuid32());
			log.setOpenId(oriOrder.getOpenId());
			log.setChannelTxnId(transactionId);
			collectionDealerAccountLogDao.create(log);

			Map<String,String> notifyParams = new HashMap<>() ;
			notifyParams.put("tradeNo", oriOrder.getTradeNo());
            notifyParams.put("outTranNumber", oriOrder.getOutTradeNo());
			notifyParams.put("createTime", DateTimeUtil.format(oriOrder.getCreateTime()));
			notifyParams.put("payTime", DateTimeUtil.format(oriOrder.getPayTime()));
			notifyParams.put("status", oriOrder.getStatus()+"");
			notifyParams.put("statusDes", oriOrder.getStatusDes()+"");
			notifyParams.put("amount", oriOrder.getAmount().toString());
			notifyParams.put("openid", oriOrder.getOpenId());
			notifyParams.put("channel", oriOrder.getChannel());
			notifyService.submit(oriOrder.getNotifyUrl(), notifyParams);
			logger.info("微信回调处理完成!tradeNo={},", oriOrder.getTradeNo());
		}
		else{
			logger.info("resultCode={},total_fee={}",resultCode,totalFee);
			return "FAIL";
		}
		return "SUCCESS";
	}

	@SuppressWarnings("unchecked")
	@Override
	@Transactional
	public void updateTradeStatus(String tradeNo) throws Exception {
		CollectionDealerTradeEntity oriOrder = collectionDealerTradeDao.findByTradeNo(tradeNo);
		if(null==oriOrder){
			logger.error("商户订单号[{}]不存在!",tradeNo);
		}else{
			logger.info("oriOrder={}",oriOrder);
			if(oriOrder.getStatus()==0){
				Map<String, String> config=null;
				//如果是复兴一号要从单独处理
				if(oriOrder.getChannelCode().startsWith("BOC_RENAISSANCE_")){
					String resetChannel = StrUtil.subBefore(oriOrder.getChannelCode(), "_", true);
					config = assetDepositChannelConfService.findOptionsByChannel(resetChannel);
				}else {
					config = assetDepositChannelConfService.findOptionsByChannel(oriOrder.getChannelCode());
				}
				///微信BOC_WXPAY_1 =市直属机关;
				if (oriOrder.getChannelCode().startsWith("BOC_WXPAY_")) {
					Map<String, String> queryWxOrderResult = this.queryWxOrder(config, tradeNo);
					String return_code = queryWxOrderResult.get("return_code");
					String result_code = queryWxOrderResult.get("result_code");
					String trade_state = queryWxOrderResult.get("trade_state");
					String transaction_id = queryWxOrderResult.get("transaction_id");

					if ("SUCCESS".equals(return_code) && "SUCCESS".equals(result_code)
							&& "SUCCESS".equals(trade_state)) {
						oriOrder.setChannelTxnId(transaction_id);
						oriOrder.setAttach(JSON.toJSONString(queryWxOrderResult, true));
						oriOrder.setPayTime(new Date());
						oriOrder.setStatus(TradeStatus.SUCCESS);
						oriOrder.setStatusDes("已支付");
						collectionDealerTradeDao.update(oriOrder);
						CollectionDealerAccountEntity dealerAccount = collectionDealerAccountDao
								.findByDealerId(oriOrder.getDealerId());
						long newBalance = dealerAccount.getBalance().longValue() +
                                oriOrder.getAmount().longValue();
						dealerAccount.setBalance(newBalance);
						dealerAccount.setWxpayNum(dealerAccount.getWxpayNum().longValue() + 1);
						dealerAccount.setWxpaySum(
								dealerAccount.getWxpaySum().longValue() + oriOrder.getAmount().longValue());
						collectionDealerAccountDao.update(dealerAccount);
						CollectionDealerAccountLogEntity log = new CollectionDealerAccountLogEntity();
						log.setAction("微信支付");
						log.setAmount(oriOrder.getAmount().longValue());
						log.setChannel("微信");
						log.setTradeNo(tradeNo);
						log.setContent(oriOrder.getBody());
						log.setCreateTime(new Date());
						log.setDealerBalance(newBalance);
						log.setInOrOut(FlowType.IN + "");
						log.setDealerId(oriOrder.getDealerId());
						log.setLogId(UUIDUtils.uuid32());
						log.setOpenId(oriOrder.getOpenId());
						log.setChannelTxnId(transaction_id);
						collectionDealerAccountLogDao.create(log);

						Map<String, String> notifyParams = new HashMap<>();
						notifyParams.put("tradeNo", oriOrder.getTradeNo());
                        notifyParams.put("outTranNumber", oriOrder.getOutTradeNo());
						notifyParams.put("createTime", DateTimeUtil.format(oriOrder.getCreateTime()));
						notifyParams.put("payTime", DateTimeUtil.format(oriOrder.getPayTime()));
						notifyParams.put("status", oriOrder.getStatus() + "");
						notifyParams.put("statusDes", oriOrder.getStatusDes() + "");
						notifyParams.put("amount", oriOrder.getAmount().toString());
						notifyParams.put("openid", oriOrder.getOpenId());
						notifyParams.put("channel", oriOrder.getChannel());
						notifyService.submit(oriOrder.getNotifyUrl(), notifyParams);
                        System.out.println(JSON.toJSONString(notifyParams));
						logger.info("微信支付订单处理完成!tradeNo={}", oriOrder.getTradeNo());
					}
					return;
				}

                ///纯微信支付
                if (oriOrder.getChannelCode().startsWith("WXPAY_")) {
                    Map<String, String> queryWxOrderResult = this.queryWxOrder(config, tradeNo);
                    String return_code = queryWxOrderResult.get("return_code");
                    String result_code = queryWxOrderResult.get("result_code");
                    String trade_state = queryWxOrderResult.get("trade_state");
                    String transaction_id = queryWxOrderResult.get("transaction_id");

                    if ("SUCCESS".equals(return_code) && "SUCCESS".equals(result_code)
                            && "SUCCESS".equals(trade_state)) {
                        oriOrder.setChannelTxnId(transaction_id);
                        oriOrder.setAttach(JSON.toJSONString(queryWxOrderResult, true));
                        oriOrder.setPayTime(new Date());
                        oriOrder.setStatus(TradeStatus.SUCCESS);
                        oriOrder.setStatusDes("已支付");
                        collectionDealerTradeDao.update(oriOrder);
                        CollectionDealerAccountEntity dealerAccount = collectionDealerAccountDao
                                .findByDealerId(oriOrder.getDealerId());
                        long newBalance = dealerAccount.getBalance().longValue() +
                                oriOrder.getAmount().longValue();
                        dealerAccount.setBalance(newBalance);
                        dealerAccount.setWxpayNum(dealerAccount.getWxpayNum().longValue() + 1);
                        dealerAccount.setWxpaySum(
                                dealerAccount.getWxpaySum().longValue() + oriOrder.getAmount().longValue());
                        collectionDealerAccountDao.update(dealerAccount);

                        CollectionDealerAccountLogEntity log = new CollectionDealerAccountLogEntity();
                        log.setAction("纯微信支付");
                        log.setAmount(oriOrder.getAmount().longValue());
                        log.setChannel("纯微信");
                        log.setTradeNo(tradeNo);
                        log.setContent(oriOrder.getBody());
                        log.setCreateTime(new Date());
                        log.setDealerBalance(newBalance);
                        log.setInOrOut(FlowType.IN + "");
                        log.setDealerId(oriOrder.getDealerId());
                        log.setLogId(UUIDUtils.uuid32());
                        log.setOpenId(oriOrder.getOpenId());
                        log.setChannelTxnId(transaction_id);
                        collectionDealerAccountLogDao.create(log);

                        Map<String, String> notifyParams = new HashMap<>();
                        notifyParams.put("tradeNo", oriOrder.getTradeNo());
                        notifyParams.put("outTranNumber", oriOrder.getOutTradeNo());
                        notifyParams.put("createTime", DateTimeUtil.format(oriOrder.getCreateTime()));
                        notifyParams.put("payTime", DateTimeUtil.format(oriOrder.getPayTime()));
                        notifyParams.put("status", oriOrder.getStatus() + "");
                        notifyParams.put("statusDes", oriOrder.getStatusDes() + "");
                        notifyParams.put("amount", oriOrder.getAmount().toString());
                        notifyParams.put("openid", oriOrder.getOpenId());
                        notifyParams.put("channel", oriOrder.getChannel());
                        notifyService.submit(oriOrder.getNotifyUrl(), notifyParams);
                        System.out.println(JSON.toJSONString(notifyParams));
                        logger.info("纯微信支付订单处理完成!tradeNo={}", oriOrder.getTradeNo());
                    }
                    return;
                }

				//工商银行微信-沙坪坝区=ICBC_WX_SPB
				if (oriOrder.getChannelCode().startsWith("ICBC_WX_")) {
					QrcodeQueryResponseV2 queryWxOrderResult = this.queryICBCWxOrder(config, tradeNo);
					logger.info("queryWxOrderResult ={}",JSON.toJSONString(queryWxOrderResult,
                            true));
					//pay_status 交易结果标志，0：支付中，1：支付成功，2：支付失败
					if(queryWxOrderResult.isSuccess() && queryWxOrderResult.getPayStatus().equals("1")){
						String orderId =queryWxOrderResult.getOrderId();
						oriOrder.setPayTime(new Date());
						oriOrder.setStatus(TradeStatus.SUCCESS);
						oriOrder.setStatusDes("已支付");
						oriOrder.setChannelTxnId(orderId);
						oriOrder.setAttach(JSON.toJSONString(queryWxOrderResult, true));
						this.collectionDealerTradeDao.update(oriOrder);

						CollectionDealerAccountEntity dealerAccount = this.collectionDealerAccountDao
								.findByDealerId(oriOrder.getDealerId());
						long newBalance = dealerAccount.getBalance().longValue() +
                                oriOrder.getAmount().longValue();
						dealerAccount.setBalance(newBalance);
						dealerAccount.setWxpayNum(dealerAccount.getWxpayNum().longValue() + 1);
						dealerAccount.setWxpaySum(dealerAccount.getWxpaySum().longValue()+
                                oriOrder.getAmount().longValue());
						this.collectionDealerAccountDao.update(dealerAccount);


						Map<String, String> notifyParams = new HashMap<>();
						notifyParams.put("tradeNo", oriOrder.getTradeNo());
                        notifyParams.put("outTranNumber", oriOrder.getOutTradeNo());
						notifyParams.put("createTime", DateTimeUtil.format(oriOrder.getCreateTime()));
						notifyParams.put("payTime", DateTimeUtil.format(oriOrder.getPayTime()));
						notifyParams.put("status", oriOrder.getStatus() + "");
						notifyParams.put("statusDes", oriOrder.getStatusDes() + "");
						notifyParams.put("amount", oriOrder.getAmount().toString());
						notifyParams.put("openid", oriOrder.getOpenId());
						notifyParams.put("channel", oriOrder.getChannel());
						this.notifyService.submit(oriOrder.getNotifyUrl(), notifyParams);

						logger.info("处理完成!tradeNo={},", oriOrder.getTradeNo());
					}
                    return;
				}

				// 农业银行网上支付平台- 云阳县党费缴纳专用 : ABC_WX_CQYUNYANG
				if (oriOrder.getChannelCode().startsWith("ABC_WX_")) {
					com.abc.pay.client.JSON queryWxOrderResult = this.ABCBankService.
                            queryOrder(oriOrder.getTradeNo(), Integer.valueOf(config.get("confIndex")));
					logger.info("queryWxOrderResult ={}",JSON.
                            toJSONString(queryWxOrderResult, true));
					boolean isSuccess = queryWxOrderResult!=null &&
                            queryWxOrderResult.GetKeyValue("ReturnCode").equals("0000");

					//pay_status 交易结果标志，0：支付中，1：支付成功，2：支付失败
					if(isSuccess){
						String orderStr = com.abc.pay.client.Base64Code.
                                Decode64(queryWxOrderResult.GetKeyValue("Order"));
						Map<String,String> orderMap  =
								com.alibaba.fastjson.JSON.parseObject(orderStr, HashMap.class);
						System.out.println(orderMap.get("iRspRef"));

						String orderId =orderMap.get("iRspRef");
						String Status =orderMap.get("Status");
						logger.info("Status={}",Status);

						//03:已请款 ; 04:成功 ;
						if("04".equals(Status) || "03".equals(Status)){
							oriOrder.setPayTime(new Date());
							oriOrder.setStatus(TradeStatus.SUCCESS);
							oriOrder.setStatusDes("已支付");
							oriOrder.setChannelTxnId(orderId);
							oriOrder.setAttach(orderStr);
							this.collectionDealerTradeDao.update(oriOrder);

							CollectionDealerAccountEntity dealerAccount = this.collectionDealerAccountDao
									.findByDealerId(oriOrder.getDealerId());
							long newBalance = dealerAccount.getBalance().longValue() +
                                    oriOrder.getAmount().longValue();

							dealerAccount.setBalance(newBalance);
							dealerAccount.setWxpayNum(dealerAccount.getWxpayNum().longValue() + 1);
							dealerAccount.setWxpaySum(dealerAccount.getWxpaySum().longValue() +
                                    oriOrder.getAmount().longValue());
							this.collectionDealerAccountDao.update(dealerAccount);

							CollectionDealerAccountLogEntity log = new CollectionDealerAccountLogEntity();
							log.setAction("中国农业银行网上支付平台微信支付");
							log.setAmount(oriOrder.getAmount().longValue());
							log.setChannel("微信");
							log.setTradeNo(tradeNo);
							log.setContent(oriOrder.getBody());
							log.setCreateTime(new Date());
							log.setDealerBalance(newBalance);
							log.setInOrOut(FlowType.IN + "");
							log.setDealerId(oriOrder.getDealerId());
							log.setLogId(UUIDUtils.uuid32());
							log.setOpenId(oriOrder.getOpenId());
							log.setChannelTxnId(orderId);
							this.collectionDealerAccountLogDao.create(log);

							Map<String, String> notifyParams = new HashMap<>();
							notifyParams.put("tradeNo", oriOrder.getTradeNo());
                            notifyParams.put("outTranNumber", oriOrder.getOutTradeNo());
							notifyParams.put("createTime", DateTimeUtil.format(oriOrder.getCreateTime()));
							notifyParams.put("payTime", DateTimeUtil.format(oriOrder.getPayTime()));
							notifyParams.put("status", oriOrder.getStatus() + "");
							notifyParams.put("statusDes", oriOrder.getStatusDes() + "");
							notifyParams.put("amount", oriOrder.getAmount().toString());
							notifyParams.put("openid", oriOrder.getOpenId());
							notifyParams.put("channel", oriOrder.getChannel());
							this.notifyService.submit(oriOrder.getNotifyUrl(), notifyParams);

							logger.info("处理完成!tradeNo={},", oriOrder.getTradeNo());
						}

					}
                    return;
				}
                //工商H5支付宝
                if (oriOrder.getChannelCode().startsWith("ICBC_H5_")) {
                    CardbusinessAggregatepayB2cOnlineOrderqryResponseV1 queryWxOrderResult =
                            this.queryICBCH5Order(config, tradeNo);
                    logger.info("queryWxOrderResult ={}",JSON.toJSONString(queryWxOrderResult,
                            true));
                    //pay_status 交易结果标志，0：支付中，1：支付成功，2：支付失败
                    if(queryWxOrderResult.isSuccess()&&"0".equals(queryWxOrderResult.getPay_status())){
                        String orderId =queryWxOrderResult.getOrder_id();
                        oriOrder.setPayTime(new Date());
                        oriOrder.setStatus(TradeStatus.SUCCESS);
                        oriOrder.setStatusDes("已支付");
                        oriOrder.setChannelTxnId(orderId);
                        oriOrder.setAttach(JSON.toJSONString(queryWxOrderResult, true));
                        this.collectionDealerTradeDao.update(oriOrder);

                        CollectionDealerAccountEntity dealerAccount = this.collectionDealerAccountDao
                                .findByDealerId(oriOrder.getDealerId());
                        long newBalance = dealerAccount.getBalance().longValue() +
                                oriOrder.getAmount().longValue();
                        dealerAccount.setBalance(newBalance);
                        dealerAccount.setWxpayNum(dealerAccount.getWxpayNum().longValue() + 1);
                        dealerAccount.setWxpaySum(dealerAccount.getWxpaySum().longValue()+
                                oriOrder.getAmount().longValue());
                        this.collectionDealerAccountDao.update(dealerAccount);


                        Map<String, String> notifyParams = new HashMap<>();
                        notifyParams.put("tradeNo", oriOrder.getTradeNo());
                        notifyParams.put("outTranNumber", oriOrder.getOutTradeNo());
                        notifyParams.put("createTime", DateTimeUtil.format(oriOrder.getCreateTime()));
                        notifyParams.put("payTime", DateTimeUtil.format(oriOrder.getPayTime()));
                        notifyParams.put("status", oriOrder.getStatus() + "");
                        notifyParams.put("statusDes", oriOrder.getStatusDes() + "");
                        notifyParams.put("amount", oriOrder.getAmount().toString());
                        notifyParams.put("openid", oriOrder.getOpenId());
                        notifyParams.put("channel", oriOrder.getChannel());
                        this.notifyService.submit(oriOrder.getNotifyUrl(), notifyParams);
                        //如果017生成文件 通過ftp 傳入到內網
                        if(oriOrder.getChannelCode().equals("ICBC_H5_GONGAN")){
                            List<CollectionDealerTradeEntity> collectionDealerTradeEntities = Collections.singletonList(oriOrder);
                            fileServices.createFtpFile(collectionDealerTradeEntities);
                        }else {
                            this.notifyService.submit(oriOrder.getNotifyUrl(), notifyParams);
                        }
                        logger.info("处理完成!tradeNo={},", oriOrder.getTradeNo());
                    }else {
                        String orderId =queryWxOrderResult.getOrder_id();
                        oriOrder.setStatus(TradeStatus.WAIT_PAY);
                        oriOrder.setStatusDes("待支付-定时任务查询");
                        oriOrder.setChannelTxnId(orderId);
                        oriOrder.setTxnTime(orderId);
                        this.collectionDealerTradeDao.update(oriOrder);
                    }
                    return;
                }

				/**
				 * 通联支付
				 */
				if (oriOrder.getChannelCode().startsWith("ALLINPAY_WX_")) {
					Map<String, String> params =new HashMap<>();
					params.put("cusorderid", tradeNo);
					params.put("trxamt", oriOrder.getAmount().toString());
					sybPayService.recevPayResultNotify(params) ;
					logger.info("通联收银宝渠道订单更新完成tradeNo={}",tradeNo);
				}

                //招商银行
                if(oriOrder.getChannelCode().startsWith("MERCH_")){
                    merchantsServices.receivePayResultNotify(oriOrder, config);
                }

                //招商数币支付回调
                if(oriOrder.getChannelCode().startsWith("MERCHDIGITAL_")){
                    merchantsServices.receiveDIGITALPayResultNotify(oriOrder, config);
                }

                //建设银行H5支付
                if(oriOrder.getChannelCode().startsWith("CCB_H5_")){
                    ccbService.recevPayResultNotify(oriOrder, config);
                }

                //钉钉支付
                if(oriOrder.getChannelCode().startsWith("DingDing_")){
                    alipayService.receivePayResultNotify(oriOrder, config);
                }

                //支付宝ISV模式
                if(oriOrder.getChannelCode().startsWith("ALIPAYWEB_")){
                    alipayService.receivePayResultNotifyISV(oriOrder, config);
                }
                //建行数字人民币支付查询结果
                if(oriOrder.getChannelCode().startsWith("CBC_DIGITAL_")){
                    ccbService.receivePayResultNotifyDIGITAL(oriOrder, config);
                }

                //中行数字人民币支付查询结果
                if(oriOrder.getChannelCode().startsWith("BOC_DIGITAL_")){
                    ccbService.receivePayResultNotifyBocDIGITAL(oriOrder, config);
                }

                //工行H5 微信支付最新版本
                if(oriOrder.getChannelCode().startsWith("ICBC_H5PAY_")){
                    icbcBankService.receivePayResultNotifyICBCH5Pay(oriOrder, config);
                }

                //B2C线上消费查询接口
                if(oriOrder.getChannelCode().startsWith("ICBC_UNIONPAY_")){
                    icbcBankService.receivePayResultNotifyICBCUNIONPAY(oriOrder, config);
                }

                //工行聚富通
                if(oriOrder.getChannelCode().startsWith("ICBC_JFT_")){
                    icbcBankService.receivePayResultNotifyICBCJFT(oriOrder, config);
                }

				//微信H5支付
				if(oriOrder.getChannelCode().startsWith("WX_H5_PAY_")){
					weiXinH5Services.orderQueryResult(oriOrder, config);
				}

				//中行复兴一号
				if(oriOrder.getChannelCode().startsWith("BOC_RENAISSANCE_")){
					bocRenaissanceServices.orderQueryResult(oriOrder, config);
				}
            }

		}

	}

	@Override
	public List<Map<String, Object>> queryUnPaymentOrders() {
		String sql="SELECT trade_no FROM gs_collection_dealer_trade WHERE `status`=0 AND create_time BETWEEN  date_sub(now(), interval 60 MINUTE) AND date_sub(now(), interval 2 MINUTE) ";
		return this.collectionDealerTradeDao.findBySql(sql, new Object[]{});
	}

    @Override
    public List<Map<String, Object>> queryUnPayOrders(String  startTime,String endTime,String  channelCode) {
        String sql="SELECT trade_no FROM gs_collection_dealer_trade WHERE create_time>'"+startTime+"' and  "+    "create_time<'"+endTime+"' AND `status`=0 AND channel_code='"+channelCode+"' ";
        return this.collectionDealerTradeDao.findBySql(sql, new Object[]{});
    }

    @Override
    public List<Map<String, Object>> queryAllPayOrders(String startTime, String endTime, String channelCode) {
        String sql="SELECT trade_no FROM gs_collection_dealer_trade WHERE create_time>'"+startTime+"' and  "+    "create_time<'"+endTime+"'  AND channel_code='"+channelCode+"' ";
        return this.collectionDealerTradeDao.findBySql(sql, new Object[]{});
    }

    public static void main(String[] args) {
		String txnTime = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
		System.out.println(txnTime);
	}

	@Override
	@Transactional
	public String uninonpayNotify(Map<String, String> params) {

		String channel = params.get("reqReserved");
		String encoding = "UTF-8";
		Map<String, String> config = assetDepositChannelConfService.findOptionsByChannel(channel);
		String merId = config.get("merId");
		String version = config.get("acpsdk.version");
		String signMethod = config.get("acpsdk.signMethod");
		String frontUrl = config.get("acpsdk.frontUrl");
		String backUrl = config.get("acpsdk.backUrl");
		String frontTransUrl = config.get("acpsdk.frontTransUrl");
		String signCertPath = config.get("acpsdk.signCert.path");
		String signCertPwd = config.get("acpsdk.signCert.pwd");

		// 获取银联通知服务器发送的后台通知参数
		// 重要！验证签名前不要修改reqParam中的键值对的内容，否则会验签不过

			 if (!AcpService.validate(params, encoding)) {
			logger.info("验证签名结果[失败].");
			// 验签失败，需解决验签问题
			return "fali";
		} else {
			logger.info("验证签名结果[成功].");
			// 【注：为了安全验签成功才应该写商户的成功处理逻辑】交易成功，更新商户订单状态

			String orderId = params.get("orderId"); // 获取后台通知的数据，其他字段也可用类似方式获取
			String respCode = params.get("respCode");
			// 判断respCode=00、A6后，对涉及资金类的交易，请再发起查询接口查询，确定交易成功后更新数据库。
			CollectionDealerTradeEntity tradeEntity =
					this.collectionDealerTradeDao.findByTradeNo(orderId);

			if(tradeEntity==null){
				logger.error("银联订单号{}不存在!",orderId);
				return "fail";
			}
			if(tradeEntity.getStatus()==1){
				logger.error("银联订单{}已支付成功!",orderId);
				return "ok";
			}
			UnionpayOrderQueryResponse orderResp =
					this.queryUnionPayOrder(config, orderId, tradeEntity.getTxnTime());

			if(orderResp.isSuccess() && "00".equals(orderResp.getOrigRespCode())){
				tradeEntity.setStatus(1);
				tradeEntity.setStatusDes("已支付");
				tradeEntity.setPayTime(new Date());
				tradeEntity.setAttach(JSON.toJSONString(params, true));
				tradeEntity.setChannelTxnId(orderResp.getQueryId());
				this.collectionDealerTradeDao.update(tradeEntity);

				CollectionDealerAccountEntity dealerAccount =
						collectionDealerAccountDao.findByDealerId(tradeEntity.getDealerId());

				long newBalance = dealerAccount.getBalance().longValue() +
						tradeEntity.getAmount().longValue();

				dealerAccount.setBalance(newBalance);
				dealerAccount.setUnionpayNum(dealerAccount.getUnionpayNum().longValue() + 1);
				dealerAccount.setUnionpaySum(dealerAccount.getUnionpaySum().longValue() +
						tradeEntity.getAmount().longValue());
				collectionDealerAccountDao.update(dealerAccount);

				CollectionDealerAccountLogEntity log = new CollectionDealerAccountLogEntity() ;
				log.setAction("银联支付");
				log.setAmount(tradeEntity.getAmount().longValue());
				log.setChannel("银联");
				log.setTradeNo(orderId);
				log.setContent(tradeEntity.getBody());
				log.setCreateTime(new Date());
				log.setDealerBalance(newBalance);
				log.setInOrOut(FlowType.IN+"");
				log.setDealerId(tradeEntity.getDealerId());
				log.setLogId(UUIDUtils.uuid32());
				log.setOpenId(tradeEntity.getOpenId());
				log.setChannelTxnId(orderResp.getQueryId());
				collectionDealerAccountLogDao.create(log);

				Map<String,String> notifyParams = new HashMap<>() ;
				notifyParams.put("tradeNo", tradeEntity.getTradeNo());
                notifyParams.put("outTranNumber", tradeEntity.getOutTradeNo());
				notifyParams.put("createTime", DateTimeUtil.format(tradeEntity.getCreateTime()));
				notifyParams.put("payTime", DateTimeUtil.format(tradeEntity.getPayTime()));
				notifyParams.put("status", tradeEntity.getStatus()+"");
				notifyParams.put("statusDes", tradeEntity.getStatusDes()+"");
				notifyParams.put("amount", tradeEntity.getAmount().toString());
				notifyParams.put("openid", tradeEntity.getOpenId());
				notifyParams.put("channel", tradeEntity.getChannel());
				notifyService.submit(tradeEntity.getNotifyUrl(), notifyParams);
				logger.info("银联订单回调处理完成!tradeNo={}!", tradeEntity.getTradeNo());

				return "ok";
			}
			else{
				logger.error("银联订单{}支付失败! {}[{}]",orderId,orderResp.getOrigRespMsg(),orderResp.getOrigRespCode());
				return "fail";
			}

		}
	}

	@Override
	@Transactional
	public String icbcNotifyHandle(Map<String, String> param,String requestURI) throws IcbcApiException {
        String responseBizContent = "{\"return_code\":0,\"return_msg\":\"SUCCESS\",\"msg_id\":\"" + UUIDUtils.uuid32() + "\"}";
        String bizContent = param.get("biz_content");
		HashMap bizContentMap = JSON.parseObject(bizContent, HashMap.class);
		String outTradeNo= bizContentMap.get("out_trade_no").toString();
		String orderId= bizContentMap.get("order_id").toString();
		String sign= param.get("sign");
		String signType= param.get("sign_type");
		String charset= param.get("charset");
		int returnCode = Integer.parseInt(bizContentMap.get("return_code").toString());
		String icbcPubKey;
		String privateKey = null;

		CollectionDealerTradeEntity oriOrder = this.collectionDealerTradeDao.findByTradeNo(outTradeNo);
		if(null==oriOrder){
			logger.error("商户订单号[{}]不存在!",outTradeNo);
		}else{
			logger.info("待处理订单信息{}", oriOrder);
			Map<String, String> config = this.assetDepositChannelConfService.findOptionsByChannel(oriOrder.getChannelCode());
			icbcPubKey = config.get("icbc_pub_key");
			privateKey = config.get("rsa_private_key");

			if(oriOrder.getStatus()!=TradeStatus.SUCCESS){
				param.remove("sign");
				String signContentStr=com.icbc.api.utils.WebUtils.buildOrderedSignStr(requestURI, param);
				logger.info("signContentStr={}",signContentStr);
				boolean signVerify = IcbcSignature.verify(signContentStr,signType, icbcPubKey,charset,sign);
				if(!signVerify){
					logger.info("签名检查失败!tradeNo={}",outTradeNo);
					responseBizContent = "{\"return_code\":-12345,\"return_msg\":\"icbc sign not pass.\"}";
				}
				else{
					logger.info("签名检查成功!tradeNo={}",outTradeNo);
					if (returnCode == 0) {
						oriOrder.setPayTime(new Date());
						oriOrder.setStatus(TradeStatus.SUCCESS);
						oriOrder.setStatusDes("已支付");
						oriOrder.setChannelTxnId(orderId);
						oriOrder.setAttach(JSON.toJSONString(param, true));
						this.collectionDealerTradeDao.update(oriOrder);

						CollectionDealerAccountEntity dealerAccount = this.collectionDealerAccountDao
								.findByDealerId(oriOrder.getDealerId());

						long newBalance = dealerAccount.getBalance() + oriOrder.getAmount();

						dealerAccount.setBalance(newBalance);
						dealerAccount.setWxpayNum(dealerAccount.getWxpayNum() + 1);
						dealerAccount.setWxpaySum(dealerAccount.getWxpaySum() + oriOrder.getAmount());
						this.collectionDealerAccountDao.update(dealerAccount);

						CollectionDealerAccountLogEntity log = new CollectionDealerAccountLogEntity();
						log.setAction("中国工商银行-公众号聚合支付");
						log.setAmount(oriOrder.getAmount());
						log.setChannel("工行支付");
						log.setTradeNo(outTradeNo);
						log.setContent(oriOrder.getBody());
						log.setCreateTime(new Date());
						log.setDealerBalance(newBalance);
						log.setInOrOut(FlowType.IN + "");
						log.setDealerId(oriOrder.getDealerId());
						log.setLogId(UUIDUtils.uuid32());
						log.setOpenId(oriOrder.getOpenId());
						log.setChannelTxnId(orderId);
						this.collectionDealerAccountLogDao.create(log);

						Map<String, String> notifyParams = new HashMap<>();
						notifyParams.put("tradeNo", oriOrder.getTradeNo());
                        notifyParams.put("outTranNumber", oriOrder.getOutTradeNo());
						notifyParams.put("createTime", DateTimeUtil.format(oriOrder.getCreateTime()));
						notifyParams.put("payTime", DateTimeUtil.format(oriOrder.getPayTime()));
						notifyParams.put("status", oriOrder.getStatus() + "");
						notifyParams.put("statusDes", oriOrder.getStatusDes() + "");
						notifyParams.put("amount", oriOrder.getAmount().toString());
						notifyParams.put("openid", oriOrder.getOpenId());
						notifyParams.put("channel", oriOrder.getChannel());
                        //如果017生成文件 通過ftp 傳入到內網
                        if(oriOrder.getChannelCode().equals("ICBC_H5_GONGAN")){
                            List<CollectionDealerTradeEntity> collectionDealerTradeEntities = Collections.singletonList(oriOrder);
                            fileServices.createFtpFile(collectionDealerTradeEntities);
                        }else {
                            this.notifyService.submit(oriOrder.getNotifyUrl(), notifyParams);
                        }
						logger.info("回调处理完成!tradeNo={},", oriOrder.getTradeNo());
					}
				}
			}
		}
		String signStr="\"response_biz_content\":"+responseBizContent+","+"\"sign_type\":"+"\"RSA\"";
		sign=IcbcSignature.sign(signStr, "RSA2", privateKey, charset);
		return "{"+signStr+",\"sign\":\""+sign+"\"}";
	}

    /**
     * 微信主动查询接口
     * @param config
     * @param tradeNo
     * @return
     * @throws IcbcApiException
     */
	private QrcodeQueryResponseV2 queryICBCWxOrder(Map<String, String> config,String tradeNo) throws IcbcApiException{
		String merId = config.get("mer_id");
		String signType = config.get("sign_type");
		String appId = config.get("app_id");
		String tpAppId = config.get("tp_app_id");
		String encryptKey = config.get("encrypt_key");
		String rsaPrivateKey = config.get("rsa_private_key");
		String icbcPubKey = config.get("icbc_pub_key");
		String tradeQueryUrl = config.get("tradeQueryUrl");

//		DefaultIcbcClient  client = new
//				DefaultIcbcClient (appId,signType,rsaPrivateKey,icbcPubKey);
        DefaultIcbcClient client = new DefaultIcbcClient(appId, IcbcConstants.SIGN_TYPE_RSA2, rsaPrivateKey,
                icbcPubKey);
		QrcodeQueryRequestV2 ICBCRequest = new QrcodeQueryRequestV2();
		ICBCRequest.setServiceUrl(tradeQueryUrl);

		QrcodeQueryRequestV2Biz bizContent = new QrcodeQueryRequestV2Biz();
		bizContent.setMerId(merId);
		bizContent.setOutTradeNo(tradeNo);

		ICBCRequest.setBizContent(bizContent);

		return client.execute(ICBCRequest, UUIDUtils.uuid32());
	}

    /**
     * 工商支付宝查询接口
     * @param config
     * @param tradeNo
     * @return
     * @throws IcbcApiException
     */
    private CardbusinessAggregatepayB2cOnlineOrderqryResponseV1 queryICBCH5Order(Map<String, String> config,String tradeNo) {
        String merId = config.get("mer_id");
        String signType = config.get("sign_type");
        String appId = config.get("app_id");
        String tpAppId = config.get("tp_app_id");
        String encryptKey = config.get("encrypt_key");
        String rsaPrivateKey = config.get("rsa_private_key");
        String icbcPubKey = config.get("icbc_pub_key");
        String tradeQueryUrl = config.get("tradeQueryUrl");

        //签名类型为RSA时，需传入appid，私钥和网关公钥，签名类型使用定值IcbcConstants.SIGN_TYPE_RSA，其他参数使用缺省值
        DefaultIcbcClient client = new DefaultIcbcClient(appId,IcbcConstants.SIGN_TYPE_RSA2,rsaPrivateKey, icbcPubKey);
        CardbusinessAggregatepayB2cOnlineOrderqryRequestV1 request = new CardbusinessAggregatepayB2cOnlineOrderqryRequestV1();
        //根据测试环境和生产环境替换相应ip和端口
        request.setServiceUrl(tradeQueryUrl);
        CardbusinessAggregatepayB2cOnlineOrderqryRequestV1.CardbusinessAggregatepayB2cOnlineOrderqryRequestV1Biz
                bizContent = new CardbusinessAggregatepayB2cOnlineOrderqryRequestV1      .CardbusinessAggregatepayB2cOnlineOrderqryRequestV1Biz();
        request.setBizContent(bizContent);
        //请对照接口文档用bizContent.setxxx()方法对业务上送数据进行赋值
        bizContent.setMer_id(merId);
        bizContent.setOut_trade_no(tradeNo);
       // bizContent.setOrder_id(tradeNo);
        bizContent.setDeal_flag("0");
        bizContent.setIcbc_appid(appId);
        //msgId消息通讯唯一编号，要求每次调用独立生成，APP级唯一
        CardbusinessAggregatepayB2cOnlineOrderqryResponseV1 response;
        try {
            return client.execute(request, System.currentTimeMillis()+"");
        } catch (IcbcApiException e) {
            e.printStackTrace();
        }
        return null;
    }

	@Override
	@Transactional
	public void updateOrderServiceFee() {
		this.collectionDealerAccountDao.updateBySql("UPDATE gs_collection_dealer_trade t SET t.service_fee = ( SELECT ABS(a.service_fee * 100) FROM gs_pay_wx_bill_item a WHERE a.channel = t.channel_code AND a.out_trade_no = t.trade_no ) WHERE t.channel_code LIKE 'WXPAY_%'");
		this.collectionDealerAccountDao.updateBySql("UPDATE gs_collection_dealer_trade t SET t.service_fee = ( SELECT ABS(a.service_fee * 100) FROM gs_pay_alipay_bill_item a WHERE a.channel = t.channel_code AND a.out_trade_no = t.trade_no ) WHERE t.channel_code LIKE 'ALIPAY_%'");
	}

	@Override
	@Transactional
	public void refundMark(TradeRefundRequest param) {
		CollectionDealerTradeEntity order = this.collectionDealerTradeDao.
				findByTradeNoAndDealerId(param.getTradeNo(),Integer.valueOf(param.getDealerId()));
		Asserts.bizCheck(order != null, "订单不存在!");
		Asserts.bizCheck(order.getStatus()==TradeStatus.SUCCESS, "只有支付成功状态才可退款");
		
		order.setRefundedAmount(order.getAmount());
		order.setRefundOperator(param.getOperator());
		order.setRefundTime(new Date());
		order.setStatus(TradeStatus.CLOSED);
		order.setStatusDes("已退款,订单关闭");
		this.collectionDealerTradeDao.update(order);
		
		CollectionDealerAccountEntity dealerAccount = 
				collectionDealerAccountDao.findByDealerId(order.getDealerId());
		
		long newBalance = dealerAccount.getBalance().longValue() - 
				order.getAmount().longValue();
				
		dealerAccount.setBalance(newBalance);
		dealerAccount.setWxpayNum(dealerAccount.getWxpayNum().longValue() +1);
		dealerAccount.setWxpaySum(dealerAccount.getWxpaySum().longValue() + order.getAmount().longValue() );
		collectionDealerAccountDao.update(dealerAccount);
		
		CollectionDealerAccountLogEntity log = new CollectionDealerAccountLogEntity() ;
		log.setAction(order.getChannel()+"退款");
		log.setAmount(-order.getAmount().longValue());
		log.setChannel(order.getChannel());
		log.setTradeNo(order.getTradeNo());
		log.setContent("退款");
		log.setCreateTime(new Date());
		log.setOpenId("");
		log.setDealerBalance(newBalance);
		log.setInOrOut(String.valueOf(FlowType.OUT));
		log.setDealerId(order.getDealerId());
		log.setLogId(UUIDUtils.uuid32());
		collectionDealerAccountLogDao.create(log);
		
	}

	@Override
	public PageResult<Map<String, Object>> queryTradeList(TradeRecordQueryRequest request) {
		StringBuilder select = new StringBuilder("select a.service_fee,a.channel,a.create_time,a.pay_time,a.trade_no,a.out_trade_no,a.channel_txn_id,a.`status`,a.status_des,a.`subject`,a.body,a.USER_ID user_id,a.amount,a.refund_time from gs_collection_dealer_trade  a WHERE 1 = 1 ");
		StringBuilder count = new StringBuilder("select COUNT(*) from gs_collection_dealer_trade  a WHERE 1 = 1 ");
		StringBuilder stat = new StringBuilder("SELECT count(*) count_,sum(a.amount) sumAmount FROM gs_collection_dealer_trade a  WHERE 1 = 1 ");
		StringBuilder where = new StringBuilder() ;
		
		List<String> params=new ArrayList<>();

		params.add(request.getDealerId());
		select.append("\n AND a.dealer_id=? ");
		stat.append("\n AND a.dealer_id=? ");
		count.append("\n AND a.dealer_id=? ");
		
		
		if (!StringUtils.isEmpty(request.getBeginTime()) && !StringUtils.isEmpty(request.getEndTime())) {
			long BeginTime = Integer.valueOf(request.getBeginTime());
			long EndTime = Integer.valueOf(request.getEndTime());
			params.add(DateTimeUtil.format(new Date(BeginTime * 1000L)));
			params.add(DateTimeUtil.format(new Date(EndTime * 1000L)));
			
			if (request.isRefundOrder()) {
				where.append("\n AND a.refund_time BETWEEN ? AND ?");
			}else{
				where.append("\n AND a.create_time BETWEEN ? AND ?");
			}
			
		}
		if (!StringUtils.isEmpty(request.getOrderNo())) {
			params.add((request.getOrderNo()));
			params.add((request.getOrderNo()));
			params.add((request.getOrderNo()));
			where.append("\n AND (a.trade_no=? OR a.out_trade_no =? OR a.channel_txn_id=?) ");
		}
		
		if (!StringUtils.isEmpty(request.getStatus())) {
			//params.add((request.getStatus()));
			where.append("\n AND a.status in ("+request.getStatus()+")");
		}
		logger.info("refundOrder={}",request.isRefundOrder());
		if (request.isRefundOrder()) {
			where.append("\n AND a.refunded_amount > 0  ");
		}else{
			///where.append("\n AND a.refunded_amount = 0  ");
		}

		select.append(where.toString()).toString();
		stat.append(where.toString()).toString();
		count.append(where.toString()).toString();
		select.append("\n ORDER BY a.create_time DESC");
		
		PageResult<Map<String, Object>> page = this.collectionDealerAccountDao.pageQuery(Integer.valueOf(request.getPageNo()), 
				Integer.valueOf(request.getPageSize()), params.toArray(), select.toString(), count.toString());
		
		List<Map<String, Object>> statResult = this.collectionDealerAccountDao.findBySql(stat.toString(),params.toArray());
		Map<String, Object> statData =new HashMap<>();
		statData.put("count", statResult.get(0).get("count_"));
		statData.put("sumAmount", statResult.get(0).get("sumAmount")==null ? 0L:statResult.get(0).get("sumAmount"));
		page.setStatData(statData);
		return page;
	}


	@Override
	public ExportResponse tradeListExport(TradeRecordExportRequest request) throws Exception {
        return null;
	}

    @Override
    public void payPageConfig(String channelCode, HttpServletRequest request, HttpServletResponse response) throws IOException {
        Jedis jedis = null;
        response.setCharacterEncoding("utf-8");
        response.setContentType("application/json; charset=utf-8");
        PrintWriter writer = response.getWriter();
        try {
            jedis = redisPool.getResource();
            //是不是存在redis Key
            if(jedis.exists(PAYCONFIGLISTKEY)){
                String redisResult = jedis.get(PAYCONFIGLISTKEY);
                List<PayPageConfigVo> list = JSON.parseArray(redisResult, PayPageConfigVo.class);
                writer.write(JSON.toJSONString(list));
                return;
            }
            List<CollectionPayPageConfigEntity> byChannelCode = collectionPayPageConfigDao.findByChannelCode(channelCode);
            List<PayPageConfigVo> list=new LinkedList<>();
            byChannelCode.stream().filter(item->{
                return item.getParentId() == 0;
            }).forEach(item->{
                PayPageConfigVo payPageConfigVo = new PayPageConfigVo();
                payPageConfigVo.setName(item.getName());
                payPageConfigVo.setIcon(item.getIcon());
                payPageConfigVo.setCode(item.getCode());
                payPageConfigVo.setOrder(item.getOrder());
                payPageConfigVo.setIsAvailable(item.getIsAvailable());
                List<CollectionPayPageConfigEntity> collect = byChannelCode.stream().filter(it -> {
                    return it.getParentId() == item.getId();
                }).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(collect)){
                    List<PayPageConfigVo> listSub=new LinkedList<>();
                    collect.forEach(it->{
                        PayPageConfigVo innerPayPageConfigVo = new PayPageConfigVo();
                        innerPayPageConfigVo.setName(it.getName());
                        innerPayPageConfigVo.setIcon(it.getIcon());
                        innerPayPageConfigVo.setCode(it.getCode());
                        innerPayPageConfigVo.setIsAvailable(it.getIsAvailable());
                        innerPayPageConfigVo.setOrder(it.getOrder());
                        listSub.add(innerPayPageConfigVo);
                    });
                    if(CollectionUtil.isNotEmpty(listSub)){
                        payPageConfigVo.setSubList(listSub.stream().sorted(Comparator.comparing(PayPageConfigVo::getOrder))
                                .collect(Collectors.toList()));
                    }
                }
                list.add(payPageConfigVo);
            });
            List<PayPageConfigVo> collect = list.stream().sorted(Comparator.comparing(PayPageConfigVo::getOrder))
                    .collect(Collectors.toList());
            //设置redis值
            jedis.set(PAYCONFIGLISTKEY, JSON.toJSONString(collect));
            writer.write(JSON.toJSONString(collect));
        } catch (Exception exception){
            logger.error("payPageConfig列表出错", exception);
        } finally {
            if (jedis != null) {
                redisPool.returnResource(jedis);
            }
        }
    }

	@Override
	public List<String> queryPayOrder(PayOrderQueryRequest payOrderQueryRequest) {
		String startTime=payOrderQueryRequest.getPayDate()+" 00:00:00";
		String endTime=payOrderQueryRequest.getPayDate()+" 23:59:59";
		String subject = payOrderQueryRequest.getUserName();
		Integer amount = payOrderQueryRequest.getAmount();
		String sql="SELECT CONCAT(USER_ID,'-',trade_no) as trade_no FROM gs_collection_dealer_trade WHERE create_time>'"+startTime+"' and "+"create_time<'"+endTime+"' " +
				"AND `status`=1 AND subject like '%"+subject+"%' and amount="+amount;
		List<Map<String, Object>> list = this.collectionDealerTradeDao.findBySql(sql, new Object[]{});
		if(CollectionUtil.isEmpty(list)){
			return Collections.singletonList("未找到支付记录");
		}
		List<String> listOrders = new ArrayList<>();
		list.stream().forEach(item->{
			String tranNum = item.get("trade_no").toString();
		});
		return listOrders;
	}

    @Override
    @Transactional
    public String ICBCJFTNotifyRecev(Map<String, String> params, String requestURI) {
        String notifyData = params.get("transData");
        //为一个json字符串
        Map map = JSONUtil.toBean(notifyData, Map.class);
        String jsonStr = Base64.decodeStr(map.get("notifyData").toString());
        Map mapNotifyResult = JSONUtil.toBean(jsonStr, Map.class);
        System.out.println(mapNotifyResult);
        String respCode = mapNotifyResult.get("respCode").toString();
        if ("00000".equals(respCode)) {
            String orderId = mapNotifyResult.get("orderId").toString();
            CollectionDealerTradeEntity oriOrder = this.collectionDealerTradeDao.findByTradeNo(orderId);
            if(null==oriOrder){
                logger.info("ICBCJFTNotifyRecev无法查询订单号，orderId={}",orderId);
                return "error";
            }
            if(oriOrder.getStatus()==1){
                logger.info("ICBCJFTNotifyRecev订单已经完成了支付，orderId={}",orderId);
                return "error";
            }
            oriOrder.setPayTime(new Date());
            oriOrder.setStatus(TradeStatus.SUCCESS);
            oriOrder.setStatusDes("已支付");
            this.collectionDealerTradeDao.update(oriOrder);
            Map<String, Object> mapReturn = new HashMap<>();
            mapReturn.put("return_code", 0);
            mapReturn.put("return_msg", "success");
            mapReturn.put("megId", DateUtil.format(new Date(),"yyyyMMddHHmmssSSS"));
            Map<String, String> notifyParams = new HashMap<>();
            notifyParams.put("tradeNo", oriOrder.getTradeNo());
            notifyParams.put("outTranNumber", oriOrder.getOutTradeNo());
            notifyParams.put("createTime", DateTimeUtil.format(oriOrder.getCreateTime()));
            notifyParams.put("payTime", DateTimeUtil.format(oriOrder.getPayTime()));
            notifyParams.put("status", oriOrder.getStatus() + "");
            notifyParams.put("statusDes", oriOrder.getStatusDes() + "");
            notifyParams.put("amount", oriOrder.getAmount().toString());
            notifyParams.put("openid", oriOrder.getOpenId());
            notifyParams.put("channel", oriOrder.getChannel());
            this.notifyService.submit(oriOrder.getNotifyUrl(), notifyParams);
            logger.info("回调处理完成!tradeNo={},", oriOrder.getTradeNo());
            return JSONUtil.toJsonStr(mapReturn) ;
        }

        return "error";

    }

	@Override
	public void runPayNoPay(String tranNumber) {
		CollectionDealerTradeEntity oriOrder = collectionDealerTradeDao.findByTradeNo(tranNumber);
		if(null==oriOrder){
			logger.info("runPayNoPay无法查询订单号，tranNumber={}",tranNumber);
			return;
		}
		try {
			Map<String, String> notifyParams = new HashMap<>();
			notifyParams.put("tradeNo", oriOrder.getTradeNo());
			notifyParams.put("outTranNumber", oriOrder.getOutTradeNo());
			notifyParams.put("createTime", DateTimeUtil.format(oriOrder.getCreateTime()));
			notifyParams.put("payTime", DateTimeUtil.format(oriOrder.getPayTime()));
			notifyParams.put("status", oriOrder.getStatus() + "");
			notifyParams.put("statusDes", oriOrder.getStatusDes());
			notifyParams.put("amount", oriOrder.getAmount().toString());
			notifyParams.put("openid", oriOrder.getOpenId());
			notifyParams.put("channel", oriOrder.getChannel());
			this.notifyService.submit(oriOrder.getNotifyUrl(), notifyParams);
			logger.info("runPayNoPay回调处理完成!tradeNo={},", oriOrder.getTradeNo());
		}catch (Exception e){
			logger.error("runPayNoPay回调处理失败,tradeNo={},", oriOrder.getTradeNo(), e);
		}

	}

}
