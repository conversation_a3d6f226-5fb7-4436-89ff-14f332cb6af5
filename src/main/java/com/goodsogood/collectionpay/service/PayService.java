package com.goodsogood.collectionpay.service;

import com.goodsogood.collectionpay.dao.support.PageResult;
import com.goodsogood.collectionpay.model.request.*;
import com.goodsogood.collectionpay.model.response.ExportResponse;
import com.goodsogood.collectionpay.model.response.TradeCreateResponse;
import com.icbc.api.IcbcApiException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface PayService {
	
	TradeCreateResponse tradeCreate(TradeCreateRequest param,HttpServletRequest req, HttpServletResponse resp) throws Exception;
	
	String wxCallBack( Map<String, String> param);
	
	void updateTradeStatus(String tradeNo) throws Exception;
	
	List<Map<String,Object>> queryUnPaymentOrders();


    List<Map<String,Object>> queryUnPayOrders(String  startTime,String endTime,String  channelCode);

    List<Map<String,Object>> queryAllPayOrders(String  startTime,String endTime,String  channelCode);
	
	String uninonpayNotify(Map<String, String> param) ;
	
	/**
	 * 处理  中国工商银行-公众号聚合支付 回调
	 * @param param
	 * @return
	 * @throws IcbcApiException 
	 */
	String icbcNotifyHandle(Map<String, String> param, String requestURI) throws IcbcApiException ;
	
	
	/**
	 * 订单标记成已经退款
	 */
	void refundMark(TradeRefundRequest param);

    /**
     * 更新手续费金额字段
     */
    void updateOrderServiceFee();

	
	PageResult<Map<String, Object>> queryTradeList(TradeRecordQueryRequest param);

	ExportResponse tradeListExport(TradeRecordExportRequest param) throws Exception;

    void payPageConfig(String channelCode, HttpServletRequest request, HttpServletResponse response) throws IOException;

	/**
	 * 查询支付订单
	 * @param payOrderQueryRequest
	 * @return
	 */
	List<String> queryPayOrder(PayOrderQueryRequest payOrderQueryRequest);

    String ICBCJFTNotifyRecev(Map<String, String> params, String requestURI);

	void runPayNoPay(String tranNumber);
}
