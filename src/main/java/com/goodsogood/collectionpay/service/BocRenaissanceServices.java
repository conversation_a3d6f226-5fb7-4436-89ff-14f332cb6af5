package com.goodsogood.collectionpay.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.bocnet.common.security.PKCS7Tool;
import com.goodsogood.collectionpay.consts.TradeStatus;
import com.goodsogood.collectionpay.consts.TradeTypeCode;
import com.goodsogood.collectionpay.dao.CollectionDealerTradeDao;
import com.goodsogood.collectionpay.entity.CollectionDealerTradeEntity;
import com.goodsogood.collectionpay.model.request.TradeCreateRequest;
import com.goodsogood.collectionpay.model.response.TradeCreateResponse;
import com.goodsogood.collectionpay.model.vo.boc.BocRenaissancePayResultVo;
import com.goodsogood.collectionpay.model.vo.boc.BocRenaissanceVo;
import com.goodsogood.collectionpay.util.HttpClientUtil;
import com.goodsogood.collectionpay.util.P7Utils;
import org.bouncycastle.util.encoders.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

@Service("BocRenaissanceServices")
public class BocRenaissanceServices {


    private static final Logger log = LoggerFactory.getLogger(BocRenaissanceServices.class);
    @Autowired
    private TradeNoGenerater tradeNoGenerater;
    @Autowired
    private CollectionDealerTradeDao collectionDealerTradeDao;

    @Autowired
    private AssetDepositChannelConfService assetDepositChannelConfService;

    @Autowired
    private NotifyService notifyService;


    @Transactional
    public void buildOrder(TradeCreateResponse resp, TradeCreateRequest request,
                           CollectionDealerTradeEntity order,
                           Map<String, String> config) {
        //生成复兴一号支付订单
        String orderNo = tradeNoGenerater.getNewTradeNo(TradeTypeCode.BOC_RENAISSANCE);
        int price = Integer.parseInt(request.getAmount());
        order.setAmount((long) price);
        order.setBody(request.getBody());
        order.setSubject(request.getBody());
        order.setDealerId(Integer.parseInt(request.getDealerId()));
        order.setOpenId(request.getOpenId());
        order.setChannel("复兴一号支付");
        order.setChannelCode(request.getChannelNew());
        order.setCreateTime(new Date());
        order.setStatus(TradeStatus.WAIT_PAY);
        order.setStatusDes("待支付");
        order.setTradeNo(orderNo);
        Long orgId = request.getOrgId();
        order.setUserId(request.getUserId());
        order.setOpenId(request.getUserId());
        order.setOrgId(orgId);
        order.setBody(request.getBody());
        order.setNotifyUrl(request.getNotifyUrl());
        order.setVersion(0L);
        this.collectionDealerTradeDao.create(order);
        try {
            //这里是测试证书加签,根据生产上的环境切换成产的证书
            Map<String, String> channelInfo = getChannelInfo(request.getChannelNew(), null);
            String pfxPath = config.get("sign_file_name_url");
            PKCS7Tool signer = PKCS7Tool.getSigner(pfxPath, config.get("sign_file_password"), config.get("sign_file_password"));
            StringBuilder stringBuilder = new StringBuilder();
            //添加prodNo信息
            stringBuilder.append(config.get("prodNo")).append("|");
            //设置金额
            String orderAmount = new BigDecimal(request.getAmount()).divide(new BigDecimal(100))
                    .setScale(2, RoundingMode.HALF_UP).toString();
            //添加orderAmount信息
            stringBuilder.append(orderAmount).append("|");
            //添加bankAccountId信息
            stringBuilder.append(config.get("bankAccountId")).append("|");
            //添加tradeNo信息
            stringBuilder.append(orderNo);
            String requestPlainText = stringBuilder.toString();
            log.info("BocRenaissanceServices.requestPlainText的值信息={}", requestPlainText);
            //调用加签方法
            String signValue = signer.sign(requestPlainText.getBytes(StandardCharsets.UTF_8)).replace("\r\n", "");
            //拼装返回信息
            BocRenaissanceVo bocRenaissanceVo = new BocRenaissanceVo();
            bocRenaissanceVo.setProdNo(config.get("prodNo"));
            bocRenaissanceVo.setOrderAmount(orderAmount);
            bocRenaissanceVo.setBankAccountId(config.get("bankAccountId"));
            bocRenaissanceVo.setSignType("SHA256");
            bocRenaissanceVo.setUserFlags(request.getUserId());
            bocRenaissanceVo.setSignValue(signValue);
            bocRenaissanceVo.setAsyncNotifyUrl(config.get("asyncNotifyUrl"));
            //1代表复兴一号支付
            bocRenaissanceVo.setGsFlag(1);
            bocRenaissanceVo.setChannel(channelInfo.get("channel"));
            if (ObjectUtil.isNotEmpty(config.get("wxAppId"))) {
                bocRenaissanceVo.setWxAppId(config.get("wxAppId"));
            }
            bocRenaissanceVo.setUserChooseChannel(channelInfo.get("userChooseChannel"));
            //设置细项信息
            List<BocRenaissanceVo.OrderDetail> listItems = new ArrayList<>();
            BocRenaissanceVo.OrderDetail orderDetail = new BocRenaissanceVo.OrderDetail();
            orderDetail.setItemAmount(orderAmount);
            orderDetail.setItemId(orderNo);
            orderDetail.setItemSeq(1);
            listItems.add(orderDetail);
            bocRenaissanceVo.setOrderDetails(listItems);
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("tradeNo", orderNo);
            parameters.put("info", JSONUtil.toJsonStr(bocRenaissanceVo));
            resp.setData(parameters);
            log.info("BocRenaissanceServices.createPayOrder之后返回参数信息,parameters={}", JSONUtil.toJsonStr(parameters));
        } catch (Exception ex) {
            log.error("中行复兴号支付生成订单异常", ex);
        }
    }


    /**
     * 封装支付渠道
     */
    private  Map<String, String> getChannelInfo(String input,Integer thType) {
        Map<String, String> resultMap = new HashMap<>();
        String bocRenaissance1 = input.substring(0, input.lastIndexOf("_"));
        String a = input.substring(input.lastIndexOf("_") + 1);
        // 存储到Map
        resultMap.put("channelCode", bocRenaissance1);
        //支付标识 3：渝快办 4：渝快政 为解决组织部支付环境不一样的标识
        if(ObjectUtil.isNotEmpty(thType)) {
            if(3 == thType){
                if("C".equals(a)){
                    resultMap.put("channel", "C1");
                }else {
                    resultMap.put("channel", a);
                }
            }else {
                resultMap.put("channel", a);
            }
        }else{
            resultMap.put("channel", a);
        }
        resultMap.put("userChooseChannel", input);
        return resultMap;
    }

    /**
     * 主动查询接口
     */
    @Transactional
    public void orderQueryResult(CollectionDealerTradeEntity order,Map<String, String> config) {
        try {
            //这里是测试证书加签,根据生产上的环境切换成产的证书
            String pfxPath = config.get("sign_file_name_url");
            PKCS7Tool signer = PKCS7Tool.getSigner(pfxPath, config.get("sign_file_password"), config.get("sign_file_password"));
            String tradeNo = order.getTradeNo();
            String orderNo="";
            //设置拼接信息
            String requestPlainText = config.get("prodNo") + "|" + orderNo + "|" + tradeNo;
            log.info("BocRenaissanceServices.requestPlainText的值信息={}", requestPlainText);
            //调用加签方法
            String signValue = signer.sign(requestPlainText.getBytes(StandardCharsets.UTF_8)).replace("\r\n", "");
            //接接查询参数
            Map<String, Object> params = new HashMap<>();
            params.put("prodNo", config.get("prodNo"));
            params.put("orderNo", orderNo);
            params.put("itemId", tradeNo);
            params.put("signType", "SHA256");
            params.put("signValue", signValue);
            log.info("BocRenaissanceServices.params={}", JSONUtil.toJsonStr(params));
            String result = HttpClientUtil.postWithStr(config.get("queryUrl"), JSONUtil.toJsonStr(params));
            log.info("BocRenaissanceServices.orderQueryResult,查询订单返回结果={}", result);
            String code = JSON.parseObject(result).get("code").toString();
            //成功结果信息
            if(code.equals("10001")) {
                String data = JSON.parseObject(result).get("data").toString();
                List<BocRenaissancePayResultVo> list = JSONUtil.toList(data, BocRenaissancePayResultVo.class);
                //有成功的单子我才进行验签
                List<BocRenaissancePayResultVo> collect = list.stream().filter(item -> item.getOrderStatus() == 1).collect(Collectors.toList());
                if (CollUtil.isEmpty(collect)) {
                    return;
                }
                //加签验证
                StringBuilder stringResetBuilder = new StringBuilder();
                list.forEach(item -> {
                    stringResetBuilder.append(item.getOrderNo()).append(",").append(item.getOrderStatus()).append(",");
                    if (ObjectUtil.isNotNull(item.getPayAmount())) {
                        stringResetBuilder.append(item.getPayAmount()).append("|");
                    }
                });
                //得到银行返回签名字符串
                String signValueResult = JSON.parseObject(result).get("signValue").toString();
                //去掉最后一个|
                String resetPlainText = stringResetBuilder.toString().replaceAll("\\|(?=[^\\|]*$)", "");
                log.info("BocRenaissanceServices.resetPlainText={}", resetPlainText);
                boolean verify = P7Utils.verify(resetPlainText.getBytes(), Base64.decode(signValueResult));
                if (verify) {
                    log.info("BocRenaissanceServices.orderQueryResult,查询订单成功！");
                    order.setPayTime(new Date());
                    order.setStatus(TradeStatus.SUCCESS);
                    order.setStatusDes("已支付");
                    // 支付渠道单号信息
                    Optional<BocRenaissancePayResultVo> optionalResultVo = list.stream()
                            .filter(item -> item.getOrderStatus() == 1)
                            .findFirst();
                    // 如果找到匹配的元素，设置order的channelTxnId
                    optionalResultVo.ifPresent(bocRenaissancePayResultVo ->{
                        order.setChannelTxnId(bocRenaissancePayResultVo.getOrderNo());
                        //设置实际支付时间
                        order.setPayTime(DateUtil.parse(bocRenaissancePayResultVo.getPayTime(), "yyyyMMddHHmmss"));
                    });
                    this.collectionDealerTradeDao.update(order);
                    //异步回调党费告诉通知结果
                    notifyService.notifyPPMD(order);
                }else {
                    log.debug("中行复兴号支付主动查询验签失败");
                }
            }
        }catch (Exception ex) {
            log.error("中行复兴号支付主动查询异常", ex);
        }
    }

    @Transactional
    public String handleNotifyOrder(Map<String, String> params) {
        try {
            CollectionDealerTradeEntity order = collectionDealerTradeDao.findByTradeNo(params.get("itemIds"));
            if (order.getStatus() == TradeStatus.SUCCESS) {
                return "OK";
            }
            //设置金额
            String orderPayAmount = new BigDecimal(order.getAmount()).divide(new BigDecimal(100))
                    .setScale(2, RoundingMode.HALF_UP).toString();
            //判断支付金额是否致一致
            if (!params.get("payAmount").equals(orderPayAmount)) {
                log.info("中行复兴号支付支付金额不一致,orderNo={},payAmount={},amount={}",
                        order.getTradeNo(), params.get("payAmount"), order.getAmount());
                return "FAIL";
            }
            //添加orderNo信息
            String requestPlainText = params.get("orderNo") + "|" +
                    //设置金额payAmount
                    params.get("payAmount") + "|" +
                    //添加payTime信息
                    params.get("payTime");
            log.info("BocRenaissanceServices.handleNotifyOrder.requestPlainText的值信息={}", requestPlainText);
            String signValue = params.get("signValue");
            boolean verify = P7Utils.verify(requestPlainText.getBytes(), Base64.decode(signValue));
            if (verify) {
                log.info("BocRenaissanceServices.handleNotifyOrder.handleNotifyOrder,签名验证成功！");
                //不取当前时间作为用户的支付时间，取用户实际的支付时间
                DateTime userPayTime = DateUtil.parse(params.get("payTime"), "yyyyMMddHHmmss");
                order.setPayTime(userPayTime);
                order.setStatus(TradeStatus.SUCCESS);
                order.setStatusDes("已支付");
                //支付渠道单号 用于对账
                order.setChannelTxnId(params.get("orderNo"));
                this.collectionDealerTradeDao.update(order);
                //异步回调党费告诉通知结果
                notifyService.notifyPPMD(order);
                return "OK";
            } else {
                log.info("cbcDigitalNotify.handleNotifyOrder,签名验证失败！");
                return "FAIL";
            }
        } catch (Exception ex) {
            log.error("handleNotifyOrder中行复兴号支付异步通知异常", ex);
            return "FAIL";
        }
    }
}
