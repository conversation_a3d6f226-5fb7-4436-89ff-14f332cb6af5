package com.goodsogood.collectionpay.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodsogood.collectionpay.consts.*;
import com.goodsogood.collectionpay.dao.CollectionDealerAccountDao;
import com.goodsogood.collectionpay.dao.CollectionDealerAccountLogDao;
import com.goodsogood.collectionpay.dao.CollectionDealerTradeDao;
import com.goodsogood.collectionpay.demo.MD5;
import com.goodsogood.collectionpay.entity.CollectionDealerAccountEntity;
import com.goodsogood.collectionpay.entity.CollectionDealerAccountLogEntity;
import com.goodsogood.collectionpay.entity.CollectionDealerTradeEntity;
import com.goodsogood.collectionpay.exception.AssetServiceException;
import com.goodsogood.collectionpay.model.request.*;
import com.goodsogood.collectionpay.model.response.TradeCreateResponse;
import com.goodsogood.collectionpay.util.DateTimeUtil;
import com.goodsogood.collectionpay.util.HttpUtils;
import com.goodsogood.collectionpay.util.SignatureUtil;
import com.goodsogood.collectionpay.util.UUIDUtils;
import com.goodsogood.collectionpay.util.sm2.SM2Util;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.*;

/**
 * @program: collection-pay
 * @description: 招商银行服务
 * @author: Mr.LiGuoYong
 * @create: 2020-09-02 08:55
 **/
@Service
public class MerchantsServices {
    @Autowired
    private TradeNoGenerater tradeNoGenerater;

    @Autowired
    private CollectionDealerTradeDao collectionDealerTradeDao;

    @Autowired
    private CollectionDealerAccountDao collectionDealerAccountDao;

    @Autowired
    private CollectionDealerAccountLogDao collectionDealerAccountLogDao;

    @Autowired
    private NotifyService notifyService;

    @Autowired
    private AssetDepositChannelConfService assetDepositChannelConfService;

    private static final Logger logger = LoggerFactory.getLogger(LogConfiger.PAY);

    /**
     * 招商银行生成订单
     * @param resp
     * @param request
     * @param order
     * @param config
     */
    public TradeCreateResponse createResponseMerchants(TradeCreateResponse resp,
                                  TradeCreateRequest request,
                                  CollectionDealerTradeEntity order,
                                  Map<String, String> config){
        String tranNumber = tradeNoGenerater.getMerchantsTradeNo(TradeTypeCode.COLLECTION_MERCH);
        order.setAmount(Long.valueOf(request.getAmount()));
        order.setBody(request.getSubject());
        order.setSubject(request.getSubject());
        order.setDealerId(Integer.parseInt(request.getDealerId()));
        order.setOpenId(request.getOpenId());
        order.setChannel("招商银行");
        order.setChannelCode(request.getChannel());
        order.setCreateTime(new Date());
        order.setStatus(TradeStatus.WAIT_PAY);
        order.setStatusDes("待支付");
        order.setTradeNo(tranNumber);
        order.setUserId(request.getUserId());
        order.setBody(request.getBody());
        order.setNotifyUrl(request.getNotifyUrl());
        order.setVersion(0L);
        this.collectionDealerTradeDao.create(order);

        LocalDate localDate = LocalDate.now();
        CMBCreateBatchOrderRequest cmbCreateBatchOrderRequest = new CMBCreateBatchOrderRequest();
        cmbCreateBatchOrderRequest.setTxcode("5101");
        cmbCreateBatchOrderRequest.setOpr_usr(config.get("opr_user"));
        cmbCreateBatchOrderRequest.setMerch_id(config.get("merch_Id"));
        String timeStamp=System.currentTimeMillis()+"";
        String payNumber=UUIDUtils.uuid().replaceAll("-","").substring(0,20);
        //得到商品名称
        cmbCreateBatchOrderRequest.setBatch_name(request.getBody());
        cmbCreateBatchOrderRequest.setExt_merch_batch(payNumber);
        //时间格式YYYYMMDD
        cmbCreateBatchOrderRequest.setBegin_date(localDate.toString().replaceAll("-",""));
        cmbCreateBatchOrderRequest.setEnd_date(localDate.plusDays(1).toString().
                replaceAll("-",""));
        //转换金额 招商最小单位为元
        String amount =""+ toFloat(Integer.parseInt(request.getAmount()), 100);
        cmbCreateBatchOrderRequest.setSum_amt(amount);
        cmbCreateBatchOrderRequest.setPkg_num("1");
        //下面4个参数固定
        cmbCreateBatchOrderRequest.setSum_cnt("1");
        cmbCreateBatchOrderRequest.setPkg_cnt("1");
        cmbCreateBatchOrderRequest.setPkg_amt(amount);
        cmbCreateBatchOrderRequest.setFlag_on("N");
        cmbCreateBatchOrderRequest.setDetail(new String[]{"缴费编号~^应缴金额~^姓名~^手机号~^明细流水号",
                ""+tranNumber+"~^"+amount+"~^"+request.getUserName()+
                        "~^"+request.getMobile()+"~^"+payNumber+""});
        cmbCreateBatchOrderRequest.setT(timeStamp);
        //得到他们配置参数
        CMBRequest<CMBBaseRequest> requestCMBRequest = getCMBRequest(config,cmbCreateBatchOrderRequest);
        String verifyMapString = requestCMBRequest.getResponse(String.class);
        Map<String, Object> mapStr = JSON.parseObject(JSON.toJSONString
                (JSON.parseObject(verifyMapString), filter)).getInnerMap();
        if(!mapStr.get("respcode").toString().equals("CMBMB99")){
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "招商银行创建订单失败。。。");
        }
        //拿到返回的merch_batch批次号 存入gs_collection_dealer_trad 表中的out_trade_no这个字段
        order.setOutTradeNo(JSON.parseArray(mapStr.get("merch_batch").toString()).get(0).toString());
        collectionDealerTradeDao.update(order);
        //这里添加交易流水号传给党费
        Map<String, Object> mapResult = new HashMap<>(1);
        mapResult.put("tradeNo", tranNumber);
        resp.setData(mapResult);
        return resp;
    }

    /**
     * TODO 除法运算，保留小数
     * @param denominator 被除数 分母
     * @param numerator 除数 分子
     * @return 商
     */
    public static Double toFloat(int denominator,int numerator) {
        // TODO 自动生成的方法存根
        DecimalFormat df=new DecimalFormat("0.00");//设置保留位数
        return Double.valueOf(df.format((float)denominator/numerator));
    }

    /**
     * 每两分钟 处理回调结果
     */

    public void receivePayResultNotify(  CollectionDealerTradeEntity order,
                                                        Map<String, String> config){
        CMBQueryOrderRequest cmbQueryOrderRequest = new CMBQueryOrderRequest();
        cmbQueryOrderRequest.setTxcode("5105");
        cmbQueryOrderRequest.setOpr_usr(config.get("opr_user"));
        cmbQueryOrderRequest.setMerch_id(config.get("merch_Id"));
        //合到生成的订单的merch_batch批次号
        cmbQueryOrderRequest.setMerch_batch(order.getOutTradeNo());
        cmbQueryOrderRequest.setStatus("A");
        cmbQueryOrderRequest.setPag_nbr("1");
        cmbQueryOrderRequest.setPag_siz("10");
        CMBRequest<CMBBaseRequest> request = getCMBRequest(config,cmbQueryOrderRequest);
        String verifyMapString = request.getResponse(String.class);
        Map<String, Object> mapStr = JSON.parseObject(JSON.toJSONString
                (JSON.parseObject(verifyMapString), filter)).getInnerMap();
        //根据应答码 说明已经支付成功 这里返回验签是失败 没有进行验证
        String trnSerial = mapStr.get("trn_serial").toString();
        String resultRow = mapStr.get("rows").toString();
        String custMerchId =JSON.parseArray(resultRow).getJSONObject(0).get("cust_merch_id").toString();
        String status = JSON.parseArray(resultRow).getJSONObject(0).get("status").toString();

        if(mapStr.get("respcode").equals("CMBMB99")){
            if(!order.getTradeNo().equals(custMerchId)||!status.equals("Y")){
                logger.info("订单存在，还未完成付款，tradeNo={}", order.getTradeNo());
                return;
            }
            order.setPayTime(new Date());
            order.setStatus(TradeStatus.SUCCESS);
            order.setStatusDes("已支付");
            order.setChannelTxnId(trnSerial);
            order.setAttach(JSON.toJSONString(mapStr));
            this.collectionDealerTradeDao.update(order);
            CollectionDealerAccountEntity dealerAccount = this.collectionDealerAccountDao
                    .findByDealerId(order.getDealerId());
            long newBalance = dealerAccount.getBalance() + order.getAmount();
            dealerAccount.setBalance(newBalance);
            dealerAccount.setWxpayNum(dealerAccount.getWxpayNum() + 1);
            dealerAccount.setWxpaySum(dealerAccount.getWxpaySum() + order.getAmount());
            this.collectionDealerAccountDao.update(dealerAccount);

            CollectionDealerAccountLogEntity log = new CollectionDealerAccountLogEntity();
            log.setAction("招商银行支付");
            log.setAmount(order.getAmount());
            log.setChannel("招商银行");
            log.setTradeNo(order.getTradeNo());
            log.setContent(order.getBody());
            log.setCreateTime(new Date());
            log.setDealerBalance(newBalance);
            log.setInOrOut(FlowType.IN + "");
            log.setDealerId(order.getDealerId());
            log.setLogId(UUIDUtils.uuid32());
            log.setOpenId(order.getOpenId());
            log.setChannelTxnId(trnSerial);
            this.collectionDealerAccountLogDao.create(log);

            Map<String, String> notifyParams = new HashMap<>();
            notifyParams.put("tradeNo", order.getTradeNo());
            notifyParams.put("outTranNumber", order.getOutTradeNo());
            notifyParams.put("createTime", DateTimeUtil.format(order.getCreateTime()));
            notifyParams.put("payTime", DateTimeUtil.format(order.getPayTime()));
            notifyParams.put("status", order.getStatus() + "");
            notifyParams.put("statusDes", order.getStatusDes() + "");
            notifyParams.put("amount", order.getAmount().toString());
            notifyParams.put("openid", order.getOpenId());
            notifyParams.put("channel", order.getChannel());
            this.notifyService.submit(order.getNotifyUrl(), notifyParams);
            logger.info("回调处理完成!tradeNo={},", order.getTradeNo());
        }else {
            logger.info("招商银行tradeNo={}未完成", order.getTradeNo());
        }
    }


    /**
     * 封装招商银行请求
     * @param config
     * @return
     */
    private CMBRequest<CMBBaseRequest> getCMBRequest(Map<String, String> config,
                                                     CMBBaseRequest cmbBaseRequest){
        String merchId = config.get("merch_Id");
        String aesKey = config.get("aes_key");
        String desKey = config.get("des_key");
        String privateKey = config.get("private_key");
        String publicKey = config.get("public_key");
        String url = config.get("url");
        CMBRequest<CMBBaseRequest> cmbRequest = new CMBRequest<>(cmbBaseRequest, merchId,aesKey,
                                                                    desKey, privateKey, publicKey,url);
        return cmbRequest;

    }

    private static PropertyFilter filter = (obj, s, v) -> {
        if (v instanceof String) {
            return !StringUtils.isEmpty(v);
        }
        return null != v;
    };


    /**
     * 招商银行数字人民币支付
     * @param resp
     * @param request
     * @param order
     * @param config
     * @return
     */
    public TradeCreateResponse createResponseMERCHDIGITAL(TradeCreateResponse resp, TradeCreateRequest request,
                                                          CollectionDealerTradeEntity order, Map<String, String> config) {
        String tranNumber = tradeNoGenerater.getMerchantsTradeNo(TradeTypeCode.COLLECTION_MERCH);
        order.setAmount(Long.valueOf(request.getAmount()));
        order.setBody(request.getSubject());
        order.setSubject(request.getSubject());
        order.setDealerId(Integer.parseInt(request.getDealerId()));
        order.setOpenId(request.getOpenId());
        order.setChannel("招商银行");
        order.setChannelCode(request.getChannel());
        order.setCreateTime(new Date());
        order.setStatus(TradeStatus.WAIT_PAY);
        order.setStatusDes("待支付");
        order.setTradeNo(tranNumber);
        order.setUserId(request.getUserId());
        order.setBody(request.getBody());
        order.setNotifyUrl(request.getNotifyUrl());
        order.setVersion(0L);
        this.collectionDealerTradeDao.create(order);
        // 组装requestBody并加签
        String signResult= signPayMethod(config,order);
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String,String> signResultMap = mapper.readValue(signResult, Map.class);
            long currentTimeMills = System.currentTimeMillis() / 1000;
            // 组apiSign加密Map
            Map<String,String> apiSign = new TreeMap<>();
            apiSign.put("appid", config.get("appId"));
            apiSign.put("secret", config.get("secret"));
            apiSign.put("sign", signResultMap.get("sign"));
            apiSign.put("timestamp", "" + currentTimeMills);
            // MD5加密
            String MD5Content = SignatureUtil.getSignContent(apiSign);
            String apiSignString = MD5.md5Str(MD5Content).toLowerCase();
            // 组request头部Map
            Map<String, String> apiHeader = new HashMap<>();
            apiHeader.put("appid", config.get("appId"));
            apiHeader.put("timestamp", "" + currentTimeMills);
            apiHeader.put("apisign", apiSignString);
            logger.info("apiHeader={}",apiHeader);
            logger.info("signResult={}",signResult);
            // 发送HTTP post请求
            Map<String,String> response = HttpUtils.postForEntity(config.get("tradeUrl"), signResult, apiHeader);
            System.out.println(mapper.writeValueAsString(response));
            // 返回结果验签
            Boolean checkResult1 = checkSign(mapper.writeValueAsString(response), config);
            if(!checkResult1){
                throw new AssetServiceException(ErrorCode.INVALID_PARAMETER, "生成订单返回时验签失败！");
            }
            String returnCode = response.get("returnCode");
            String respCode = response.get("respCode");
            if("SUCCESS".equals(returnCode)&&"SUCCESS".equals(respCode)){
                String bizContent = response.get("biz_content");
                Map<String,Object> bizContentMap = mapper.readValue(bizContent, Map.class);
                String invokeUrl = bizContentMap.get("invokeUrl").toString();
                Map<String,String> contextMap = (Map<String, String>) bizContentMap.get("context");
                Map<String, Object> parameters = new HashMap<>();
                String urlParas = buildParams(contextMap);
                parameters.put("invokeUrl", invokeUrl+"?"+urlParas);
                resp.setData(parameters);
            }else {
                throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, response.get("respMsg"));
            }
        } catch (Exception e) {
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, e.getMessage());
        }
        return resp;
    }


    private String buildParams(Map<String,String> contextMap){
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : contextMap.entrySet()) {
            sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        return sb.substring(0,sb.toString().length() - 1);
    }




    /**
     * 支付的时候进行签名
     * @param config
     * @param order
     * @return
     */
    private static String signPayMethod(Map<String, String> config,CollectionDealerTradeEntity order){
        Map<String, String> requestPublicParams = new TreeMap<>();
        try {
            //公共请求参数
            requestPublicParams.put("version", "0.0.1");    //版本号，固定为0.0.1(必传字段)
            requestPublicParams.put("encoding", "UTF-8");   //编码方式，固定为UTF-8(必传)
            requestPublicParams.put("signMethod", "02");    //签名方法，固定为02，表示签名方式为国密(必传)
            //业务要素
            Map<String, String> requestTransactionParams = new HashMap<>();
            requestTransactionParams.put("merId",  config.get("merId"));   //商户号(必传)
            requestTransactionParams.put("orderId", order.getTradeNo()); //商户订单号(必传)
            requestTransactionParams.put("userId", config.get("virtualUserId")); //虚拟收银员编号
            requestTransactionParams.put("notifyUrl", config.get("notifyUrl"));  //交易通知地址(必传)
            requestTransactionParams.put("currencyCode", "156");    //交易币种，默认156，目前只支持人民币（156）
            requestTransactionParams.put("transactionType", "TT04"); //交易类型 TT01:扫码支付 TT04:H5拉起支付
            requestTransactionParams.put("txnAmt",""+order.getAmount());  //交易金额,单位为分(必传)
//            requestTransactionParams.put("termId", "00774411");  //终端号
            requestTransactionParams.put("terminalNo", config.get("terminalNo"));   //受理终端编号 禁止中文
            requestTransactionParams.put("terminalIp", config.get("terminalIp"));   //受理终端IP 交易场景
            requestTransactionParams.put("goodsName", order.getBody());   //商品名称 不可使用特殊字条
            requestTransactionParams.put("tradePlace", config.get("tradePlace"));   //交易地点

            //设置订单过期时间
            Calendar nowTime = Calendar.getInstance();
            nowTime.add(Calendar.MINUTE,Integer.parseInt(config.get("orderTimeExpire")));
            String orderTimeExpire = DateTimeUtil.getFormatDateStr(nowTime.getTime(), "yyyy-MM-dd HH:MM:ss")
                    .replace(" ", "T");
            requestTransactionParams.put("orderTimeExpire", "2022-07-06T14:43:31"); //设置到期时间
            //requestTransactionParams.put("orderTimeExpire", orderTimeExpire); //设置到期时间
            requestPublicParams.put("biz_content", JSON.toJSONString(requestTransactionParams));
            logger.info("加签前的报文内容：" + JSON.toJSONString(requestTransactionParams));
            //对待加签内容进行排序拼接
            String signContent= SignatureUtil.getSignContent(requestPublicParams);
            //加签
            requestPublicParams.put("sign", SM2Util.sm2Sign(signContent, config.get("privateKey")));
            return JSON.toJSONString(requestPublicParams);
        }catch (Exception e){
            logger.error("加签发生异常！", e);
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "signPayMethod错误！");
        }
    }




    private static Boolean checkSign(String string,Map<String, String> config){
        System.out.println("要验签的报文内容：" + string);
        try {
            //验签
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, String> responseBodyMap = objectMapper.readValue(string, Map.class);
            String sign = responseBodyMap.remove("sign");
            String contentStr = SignatureUtil.getSignContent(responseBodyMap);
            boolean result = SM2Util.sm2Check(contentStr,sign, config.get("publicKey"));
            if (result) {
                System.out.println("报文验签成功!");
            } else {
                System.out.println("报文验签失败!");
            }
            return result;
        }catch (Exception e){
            logger.error("checkSign-招行数币验签发生异常！",e);
            return false;
        }
    }

    /**
     * 招行数字人民币支付查询
     * @param order
     * @param config
     */
    public void receiveDIGITALPayResultNotify(CollectionDealerTradeEntity order,
                                              Map<String, String> config) {
        // 组装requestBody并加签
        String signResult= signQueryMethod(config,order);
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String,String> signResultMap = mapper.readValue(signResult, Map.class);
            long currentTimeMills = System.currentTimeMillis() / 1000;
            // 组apiSign加密Map
            Map<String,String> apiSign = new TreeMap<>();
            apiSign.put("appid", config.get("appId"));
            apiSign.put("secret", config.get("secret"));
            apiSign.put("sign", signResultMap.get("sign"));
            apiSign.put("timestamp", "" + currentTimeMills);
            // MD5加密
            String MD5Content = SignatureUtil.getSignContent(apiSign);
            String apiSignString = MD5.md5Str(MD5Content).toLowerCase();
            // 组request头部Map
            Map<String, String> apiHeader = new HashMap<>();
            apiHeader.put("appid", config.get("appId"));
            apiHeader.put("timestamp", "" + currentTimeMills);
            apiHeader.put("apisign", apiSignString);
            //发送HTTP post请求
            Map<String,String> response = HttpUtils.postForEntity(config.get("queryUrl"), signResult, apiHeader);
            System.out.println(mapper.writeValueAsString(response));
            // 返回结果验签
            Boolean checkResult1 = checkSign(mapper.writeValueAsString(response), config);
            if(!checkResult1){
                throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "招商数币查询订单失败");
            }
            //回调信息
            Map<String, String> notifyParams = new HashMap<>();
            notifyParams.put("tradeNo", order.getTradeNo());
            notifyParams.put("outTranNumber", order.getOutTradeNo());
            notifyParams.put("createTime", DateTimeUtil.format(order.getCreateTime()));
            notifyParams.put("payTime", DateTimeUtil.format(order.getPayTime()));
            notifyParams.put("status", order.getStatus() + "");
            notifyParams.put("statusDes", order.getStatusDes() + "");
            notifyParams.put("amount", order.getAmount().toString());
            notifyParams.put("openid", order.getOpenId());
            notifyParams.put("channel", order.getChannel());
            this.notifyService.submit(order.getNotifyUrl(), notifyParams);
            logger.info("回调处理完成!tradeNo={},", order.getTradeNo());
        } catch (Exception e) {
            logger.error("招商数币查询订单异常", e);
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "招商数币查询订单异常");
        }
    }


    /**
     * 查询接口进行签名
     * @param config
     * @param order
     * @return
     */
    private static String signQueryMethod(Map<String, String> config,CollectionDealerTradeEntity order){
        Map<String, String> requestPublicParams = new TreeMap<>();
        try {
            //公共请求参数
            requestPublicParams.put("version", "0.0.1");    //版本号，固定为0.0.1(必传字段)
            requestPublicParams.put("encoding", "UTF-8");   //编码方式，固定为UTF-8(必传)
            requestPublicParams.put("signMethod", "02");    //签名方法，固定为02，表示签名方式为国密(必传)
            //业务要素
            Map<String, String> requestTransactionParams = new HashMap<>();
            requestTransactionParams.put("merId",  config.get("merId"));   //商户号(必传)
            requestTransactionParams.put("orderId", order.getTradeNo()); //商户订单号(必传)
            requestTransactionParams.put("userId", config.get("virtualUserId")); //虚拟收银员编号
            requestPublicParams.put("biz_content", JSON.toJSONString(requestTransactionParams));
            logger.info("加签前的报文内容：" + JSON.toJSONString(requestTransactionParams));
            //对待加签内容进行排序拼接
            String signContent= SignatureUtil.getSignContent(requestPublicParams);
            //加签
            requestPublicParams.put("sign", SM2Util.sm2Sign(signContent, config.get("secret")));
            return JSON.toJSONString(requestPublicParams);
        }catch (Exception e){
            logger.error("加签发生异常！", e);
            return null;
        }
    }
//
//    /**
//     *
//     * @param mapBizContent 传递的内容
//     * @param params
//     * @param notifyURLParam
//     * @param response
//     * @return
//     */
//    public String merchDigitalNotify(Map<String, String> mapBizContent, Map<String, String> params,
//                                     String notifyURLParam, HttpServletResponse response) {
//        String orderId = mapBizContent.get("orderId");
//        CollectionDealerTradeEntity oriOrder = this.collectionDealerTradeDao.findByTradeNo(orderId);
//
//        logger.info("待处理订单信息{}",oriOrder.toString());
//        if (null == oriOrder) {
//            logger.error("商户订单号[{}]不存在!", orderId);
//        } else {
//            Map<String, String> config =
//                    this.assetDepositChannelConfService.findOptionsByChannel(oriOrder.getChannelCode());
//            Map<String, String> notifyParams = new HashMap<>();
//            notifyParams.put("tradeNo", oriOrder.getTradeNo());
//            notifyParams.put("createTime", DateTimeUtil.format(oriOrder.getCreateTime()));
//            notifyParams.put("payTime", DateTimeUtil.format(oriOrder.getPayTime()));
//            notifyParams.put("status", oriOrder.getStatus() + "");
//            notifyParams.put("statusDes", oriOrder.getStatusDes() + "");
//            notifyParams.put("amount", oriOrder.getAmount().toString());
//            notifyParams.put("openid", oriOrder.getOpenId());
//            notifyParams.put("channel", oriOrder.getChannel());
//            this.notifyService.submit(oriOrder.getNotifyUrl(), notifyParams);
//            logger.info("回调处理完成!tradeNo={},", oriOrder.getTradeNo());
//        }
//        return "SUC";
//    }

    /**
     *
     * @param mapBizContent 传递的内容
     * @param params
     * @param notifyURLParam
     * @param response
     * @return
     */
    public void merchDigitalNotify(Map<String, String> mapBizContent, Map<String, String> params,
                                     String notifyURLParam, HttpServletResponse response) throws IOException {
        String orderId = mapBizContent.get("orderId");
        CollectionDealerTradeEntity oriOrder = this.collectionDealerTradeDao.findByTradeNo(orderId);
        logger.info("待处理订单信息{}",oriOrder.toString());
        Map<String, String> config =
                this.assetDepositChannelConfService.findOptionsByChannel(oriOrder.getChannelCode());
        signNotifyMethod(mapBizContent, params, config, oriOrder, response);
    }


    /**
     * 回调接口进行签名
     * @return
     */
    private  void signNotifyMethod(Map<String, String> mapBizContent,
                                   Map<String, String> params,
                                   Map<String, String> config,
                                   CollectionDealerTradeEntity order,
                                   HttpServletResponse response) throws IOException {
        Map<String, String> requestPublicParams = new TreeMap<>();
        Map<String, Object> returnParams = new HashMap<>();
        try {
            //公共请求参数
            requestPublicParams.put("version", "0.0.1");    //版本号，固定为0.0.1(必传字段)
            requestPublicParams.put("encoding", "UTF-8");   //编码方式，固定为UTF-8(必传)
            requestPublicParams.put("signMethod", "02");    //签名方法，固定为02，表示签名方式为国密(必传)
            //业务要素
//            Map<String, String> requestTransactionParams = bulidTransactionParams(mapBizContent);
//            requestTransactionParams.put("merId",  mapBizContent.get("merId"));   //商户号(必传)
//            requestTransactionParams.put("orderId", mapBizContent.get("orderId")); //商户订单号(必传)
//            requestTransactionParams.put("cmbOrderId", mapBizContent.get("cmbOrderId")); //招行生成的订单号(必传)
//            requestTransactionParams.put("userId", mapBizContent.get("virtualUserId")); //虚拟收银员编号
//            requestTransactionParams.put("txnAmt", mapBizContent.get("txnAmt")); //交易金额
//            requestTransactionParams.put("dscAmt", mapBizContent.get("dscAmt")); //优惠金额
//            requestTransactionParams.put("currencyCode", mapBizContent.get("currencyCode")); //支付方式
//            requestTransactionParams.put("payType", mapBizContent.get("payType")); //用户标识
//            requestTransactionParams.put("txnTime", mapBizContent.get("txnTime")); //订单发送时间
            requestPublicParams.put("biz_content", JSON.toJSONString(mapBizContent));
            logger.info("加签前的报文内容：" + JSON.toJSONString(mapBizContent));
            //对待加签内容进行排序拼接
            String signContent= SignatureUtil.getSignContent(requestPublicParams);
            //加签验证是不是正确的
            String signStr = SM2Util.sm2Sign(signContent, config.get("secret"));
            //回调信息
            Map<String, String> notifyParams = new HashMap<>();
            returnParams.put("version", "0.0.1");
            returnParams.put("encoding", "UTF-8");
            returnParams.put("sign", signStr);
            returnParams.put("signMethod", "02");
            if(signStr.equals(params.get("sign"))){
                notifyParams.put("tradeNo", order.getTradeNo());
                notifyParams.put("outTranNumber", order.getOutTradeNo());
                notifyParams.put("createTime", DateTimeUtil.format(order.getCreateTime()));
                notifyParams.put("payTime", DateTimeUtil.format(order.getPayTime()));
                notifyParams.put("status", order.getStatus() + "");
                notifyParams.put("statusDes", order.getStatusDes() + "");
                notifyParams.put("amount", order.getAmount().toString());
                notifyParams.put("openid", order.getOpenId());
                notifyParams.put("channel", order.getChannel());
                this.notifyService.submit(order.getNotifyUrl(), notifyParams);
                logger.info("回调处理完成!tradeNo={},", order.getTradeNo());
                //然后回调招行银行
                returnParams.put("returnCode", "SUCCESS");
            }else {
                returnParams.put("returnCode", "FAIL");
                returnParams.put("respMsg", "验证解密失败");
            }
        }catch (Exception e){
            logger.error("加签发生异常！", e);
            returnParams.put("returnCode", "FAIL");
            returnParams.put("respMsg", "验证解密失败");
        }
        response.setCharacterEncoding("utf-8");
        response.setContentType("application/json; charset=utf-8");
        PrintWriter writer = response.getWriter();
        writer.write(JSON.toJSONString(returnParams));
    }

    /**
     * 构建业务参数
     * @return
     */
    private Map<String, String> bulidTransactionParams(Map<String, String> mapBizContent){
        //业务要素
        Map<String, String> requestTransactionParams = new HashMap<>();
        for (Map.Entry<String, String> entry : mapBizContent.entrySet()) {
            requestTransactionParams.put(entry.getKey(), entry.getValue());
        }
        return requestTransactionParams;
    }
}
