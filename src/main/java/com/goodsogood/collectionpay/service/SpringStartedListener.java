package com.goodsogood.collectionpay.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Service;

import com.goodsogood.collectionpay.consts.LogConfiger;

@Service
public class SpringStartedListener implements ApplicationListener<ContextRefreshedEvent>{

	private static final Logger logger = LoggerFactory.getLogger(LogConfiger.APP);
	
	
    @Override
    public void onApplicationEvent(ContextRefreshedEvent event)
	{
		if (event.getApplicationContext().getParent() == null) {
			logger.info("Spring 启动完成!");

		}

	}
    
 

}