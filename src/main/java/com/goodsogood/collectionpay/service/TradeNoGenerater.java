package com.goodsogood.collectionpay.service;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.apache.commons.lang3.RandomStringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.goodsogood.collectionpay.conf.RedisKeyConf;
import com.goodsogood.collectionpay.consts.LogConfiger;
import com.goodsogood.collectionpay.consts.TradeTypeCode;
import com.goodsogood.collectionpay.redis.RedisPool;

import redis.clients.jedis.Jedis;

@Component("tradeNoGenerater")
public class TradeNoGenerater {
	
	private static final String DEALER_FLOW_ID_PREFIX = "10";
	private static final Logger logger = LoggerFactory.getLogger(LogConfiger.APP);
	private static final int KEY_TTL = 60 * 60 * 24 * 1;//key存活为1天

	@Autowired
	private RedisPool redisPool;

	/**
	 * 生成新的交易单号
	 * 
	 * @param tradeTypeCode
	 * @return
	 */
	public String getNewTradeNo(TradeTypeCode tradeTypeCode, Long Autoidgen) {
		Jedis jedis = null;
		try {
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
			String date = dateFormat.format(new Date());
			if (Autoidgen!=null && Autoidgen.longValue() != 0L) {
				return date + tradeTypeCode.getValue() + Autoidgen;
			}
			jedis = redisPool.getResource();
			Long val = jedis.incr(RedisKeyConf.TRADE_NO_INCR_KEY);
			if(1==val.intValue()){
				jedis.expire(RedisKeyConf.TRADE_NO_INCR_KEY, KEY_TTL);
			}
		    return date + tradeTypeCode.getValue() +RandomStringUtils.randomNumeric(8)+String.format("%06d", val.intValue());
		} catch (Exception ex) {
			logger.error("生成交易号失败！", ex);
			throw ex;
		} finally {
			if (jedis != null){
				redisPool.returnResource(jedis);
			}
		}
	}
	
	/**
	 * 生成新的交易单号
	 * 
	 * @param tradeTypeCode
	 * @return
	 */
	public String getNewTradeNo(TradeTypeCode tradeTypeCode) {
		Jedis jedis = null;
		try {
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
			String date = dateFormat.format(new Date());
			jedis = redisPool.getResource();
			Long val = jedis.incr(RedisKeyConf.TRADE_NO_INCR_KEY);
			
			if(1==val.intValue()){
				jedis.expire(RedisKeyConf.TRADE_NO_INCR_KEY, KEY_TTL);
			}
			return date + tradeTypeCode.getValue() +RandomStringUtils.randomNumeric(8)+ String.format("%06d", val.intValue());
		} catch (Exception ex) {
			logger.error("生成交易号失败！", ex);
			throw ex;
		} finally {
			if (jedis != null){
				redisPool.returnResource(jedis);
			}
		}
	}

    /**
     * 生成数字人民币单号
     *
     * @param tradeTypeCode
     * @return
     */
    public String getNewTradeNo(TradeTypeCode tradeTypeCode,Integer count) {
        Jedis jedis = null;
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
            String date = dateFormat.format(new Date());
            jedis = redisPool.getResource();
            Long val = jedis.incr(RedisKeyConf.TRADE_NO_INCR_KEY);

            if(1==val.intValue()){
                jedis.expire(RedisKeyConf.TRADE_NO_INCR_KEY, KEY_TTL);
            }
            return date + tradeTypeCode.getValue() +RandomStringUtils.randomNumeric(count)+ String.format("%06d", val.intValue());
        } catch (Exception ex) {
            logger.error("生成交易号失败！", ex);
            throw ex;
        } finally {
            if (jedis != null){
                redisPool.returnResource(jedis);
            }
        }
    }

    /**
     * 生成招行银行订单号
     *
     * @param tradeTypeCode
     * @return
     */
    public String getMerchantsTradeNo(TradeTypeCode tradeTypeCode) {
        Jedis jedis = null;
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
            String date = dateFormat.format(new Date());
            jedis = redisPool.getResource();
            Long val = jedis.incr(RedisKeyConf.TRADE_NO_INCR_KEY);

            if(1==val.intValue()){
                jedis.expire(RedisKeyConf.TRADE_NO_INCR_KEY, KEY_TTL);
            }
            return date + tradeTypeCode.getValue()+ RandomStringUtils.randomNumeric(2)+
                    String.format("%06d", val.intValue());
        } catch (Exception ex) {
            logger.error("生成交易号失败！", ex);
            throw ex;
        } finally {
            if (jedis != null){
                redisPool.returnResource(jedis);
            }
        }
    }


	
	/**
	 * 生成 商城资金流水id
	 * @return
	 */
	public String getDealerFlowId() {
		Jedis jedis = null;
		try {
			SimpleDateFormat dateFormat = new SimpleDateFormat("YYMMddHHmmss");
			String date = dateFormat.format(new Date());
			jedis = redisPool.getResource();
			Long val = jedis.incr(RedisKeyConf.TRADE_NO_INCR_KEY);
			if(1==val.intValue()){
				jedis.expire(RedisKeyConf.TRADE_NO_INCR_KEY, KEY_TTL);
			}
			return DEALER_FLOW_ID_PREFIX + date + String.format("%06d", val.intValue());
		} catch (Exception ex) {
			logger.error("生成交易号失败！", ex);
			throw ex;
		} finally {
			if (jedis != null) {
				redisPool.returnResource(jedis);
			}
		}
	}
	

	/**
	 * 根据原交易单号生成退款单号
	 * @param tradeNo
	 * @return
	 */
	public String getRefundTradeNo(String tradeNo) {
		return getNewTradeNo(TradeTypeCode.TRADE_REFUND,0L);
	}

	public static void main(String[] args) {
        System.out.println(RandomStringUtils.randomNumeric(2));
	}
}
