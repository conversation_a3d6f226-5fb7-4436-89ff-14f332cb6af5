package com.goodsogood.collectionpay.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.goodsogood.collectionpay.consts.TradeStatus;
import com.goodsogood.collectionpay.consts.TradeTypeCode;
import com.goodsogood.collectionpay.dao.CollectionDealerTradeDao;
import com.goodsogood.collectionpay.entity.CollectionDealerTradeEntity;
import com.goodsogood.collectionpay.model.ResponseObject;
import com.goodsogood.collectionpay.model.request.TradeCreateRequest;
import com.goodsogood.collectionpay.model.response.TradeCreateResponse;
import com.wechat.pay.contrib.apache.httpclient.WechatPayHttpClientBuilder;
import com.wechat.pay.contrib.apache.httpclient.auth.AutoUpdateCertificatesVerifier;
import com.wechat.pay.contrib.apache.httpclient.auth.PrivateKeySigner;
import com.wechat.pay.contrib.apache.httpclient.auth.WechatPay2Credentials;
import com.wechat.pay.contrib.apache.httpclient.auth.WechatPay2Validator;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.notification.NotificationConfig;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.core.notification.RequestParam;
import com.wechat.pay.java.core.util.PemUtil;
import com.wechat.pay.java.service.partnerpayments.h5.model.Transaction;
import okhttp3.HttpUrl;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.security.Signature;
import java.util.*;


@Service
public class WeiXinH5Services {

    private static final Logger log = LoggerFactory.getLogger(WeiXinH5Services.class);
    @Autowired
    private TradeNoGenerater tradeNoGenerater;
    @Autowired
    private CollectionDealerTradeDao collectionDealerTradeDao;
    @Autowired
    private NotifyService notifyService;


    @Autowired
    private AssetDepositChannelConfService assetDepositChannelConfService ;

    /*
     * 生成订单
     */
    @Transactional
    public TradeCreateResponse createPayOrder(TradeCreateResponse resp,
                                              TradeCreateRequest request,
                                              CollectionDealerTradeEntity order,
                                              Map<String, String> config) {
        String url="https://api.mch.weixin.qq.com/v3/pay/transactions/h5";
        String tradeNo = tradeNoGenerater.getNewTradeNo(TradeTypeCode.COLLECTION_WX_H5);
        try {
            //得到渠道的配置信息
            Map<String, Object> map = new HashMap<>();
            map.put("mchid", config.get("mchid"));
            map.put("appid", config.get("appid"));
            map.put("out_trade_no", tradeNo);
            map.put("description", request.getSubject());
            map.put("notify_url", config.get("notify_url"));
            //添加金额
            Map<String, Object> mapAmount = new HashMap<>();
            mapAmount.put("total", Integer.parseInt(request.getAmount()));
            mapAmount.put("currency", "CNY");
            map.put("amount", mapAmount);
            //添加场景
            Map<String, Object> sceneInfo = new HashMap<>();
            sceneInfo.put("payer_client_ip","***************");
            Map<String, Object> h5Info = new HashMap<>();
            h5Info.put("type", "Wap");
            sceneInfo.put("h5_info", h5Info);
            map.put("scene_info", sceneInfo);
            String body = JSONUtil.toJsonStr(map);
            System.out.println(body);
            String schema = "WECHATPAY2-SHA256-RSA2048";
            HttpUrl httpurl = HttpUrl.parse(url);
            String Authorization = getToken("POST", httpurl, body,config);
            String result = HttpUtil.createPost(url)
                    .header("Accept", "application/json")
                    .header("Content-Type", "application/json")
                    .header("Authorization", schema+" "+ Authorization)
                    .body(JSONUtil.toJsonStr(map)).execute().body();
            Map bean = JSONUtil.toBean(result, Map.class);
            Map<String, Object> reResult = new HashMap<>();
            //生成订单号
            int price = Integer.valueOf(request.getAmount());
            order.setAmount((long) price);
            order.setBody(request.getBody());
            order.setSubject(request.getBody());
            order.setDealerId(Integer.valueOf(request.getDealerId()));
            order.setOpenId(request.getOpenId());
            order.setChannel("微信H5支付");
            order.setChannelCode(request.getChannel());
            order.setCreateTime(new Date());
            order.setStatus(TradeStatus.WAIT_PAY);
            order.setStatusDes("待支付");
            order.setTradeNo(tradeNo);
            Long orgId = request.getOrgId();
            order.setUserId(request.getUserId());
            order.setOpenId(request.getUserId());
            order.setOrgId(orgId);
            order.setBody(request.getBody());
            order.setNotifyUrl(request.getNotifyUrl());
            order.setVersion(0L);
            this.collectionDealerTradeDao.create(order);
            if(ObjectUtil.isNotNull(bean.get("h5_url"))){
                reResult.put("tradeNo", tradeNo);
                reResult.put("jumpUrl", bean.get("h5_url"));
                reResult.put("channelCode", "WX-H5-PAY");
                resp.setData(reResult);
            }
            return resp;
        }catch (Exception ex){
            log.error("WeiXinServices.createPayOrder发生异常",ex);
        }
        return null;
    }



     private   String getToken(String method, HttpUrl url, String body,Map<String, String> config ) throws Exception {
        String nonceStr = UUID.randomUUID().toString();
        long timestamp = System.currentTimeMillis() / 1000;
        String message = buildMessage(method, url, timestamp, nonceStr, body);
        String signature = sign(message.getBytes(StandardCharsets.UTF_8),config);
        String yourMerchantId=config.get("mchid");
        String yourCertificateSerialNo = config.get("merchantSerialNumber");
        return "mchid=\"" + yourMerchantId + "\","
                 + "nonce_str=\"" + nonceStr + "\","
                 + "timestamp=\"" + timestamp + "\","
                 + "serial_no=\"" + yourCertificateSerialNo + "\","
                 + "signature=\"" + signature + "\"";
    }

    private   String sign(byte[] message,Map<String, String> config) throws Exception {
        Signature sign = Signature.getInstance("SHA256withRSA");
        PrivateKey privateKey = PemUtil.loadPrivateKeyFromPath(config.get("privateKeyPath"));
        sign.initSign(privateKey);
        sign.update(message);
        return Base64.getEncoder().encodeToString(sign.sign());
    }

    String buildMessage(String method, HttpUrl url, long timestamp, String nonceStr, String body) {
        String canonicalUrl = url.encodedPath();
        if (url.encodedQuery() != null) {
            canonicalUrl += "?" + url.encodedQuery();
        }
        return method + "\n"
                + canonicalUrl + "\n"
                + timestamp + "\n"
                + nonceStr + "\n"
                + body + "\n";
    }

    /**
     * 订单查询结果
     */
    @Transactional
    public void wxH5Notify(HttpServletRequest request,
                           HttpServletResponse response,
                           Map<String,Object> map,
                            String channel) {
        String timestamp = request.getHeader("Wechatpay-Timestamp");
        String nonce = request.getHeader("Wechatpay-Nonce");
        String signature = request.getHeader("Wechatpay-Signature");
        String serialNumber = request.getHeader("Wechatpay-Serial");
        try {
            Map<String, String> configChannel = assetDepositChannelConfService.findOptionsByChannel(channel);
            //构造RequestParam
            RequestParam requestParam = new RequestParam.Builder()
                    .serialNumber(serialNumber)
                    .nonce(nonce)
                    .signature(signature)
                    .timestamp(timestamp)
                    .body(JSONUtil.toJsonStr(map))
                    .build();
            // 如果已经初始化了RSAAutoCertificateConfig，可直接使用没有的话，则构造一个
            NotificationConfig config = new RSAAutoCertificateConfig.Builder()
                    .merchantId(configChannel.get("mchid"))
                    .privateKeyFromPath(configChannel.get("privateKeyPath"))
                    .merchantSerialNumber(configChannel.get("merchantSerialNumber"))
                    .apiV3Key(configChannel.get("apiV3Key"))
                    .build();
            //初始化NotificationParser
            NotificationParser parser = new NotificationParser(config);
            //以支付通知回调为例，验签、解密并转换成Transaction
            Transaction transaction = parser.parse(requestParam, Transaction.class);
            if (transaction.getTradeState().toString().equals("SUCCESS")) {
                String outTradeNo = transaction.getOutTradeNo();
                if (outTradeNo != null) {
                    CollectionDealerTradeEntity order = this.collectionDealerTradeDao.findByTradeNo(outTradeNo);
                    log.info("wxNotifyCommonHandler的order={}",JSONUtil.toJsonStr(order));
                    if (ObjectUtil.isNotNull(order)) {
                        if(order.getStatus()==0) {
                            order.setPayTime(new Date());
                            order.setStatus(TradeStatus.SUCCESS);
                            order.setStatusDes("已支付");
                            collectionDealerTradeDao.update(order);
                            //异步回调党费告诉通知结果
                            notifyService.notifyPPMD(order);
                            log.info("wxNotifyCommonHandler的outTradeNo={}支付成功", outTradeNo);
                            responseResult("SUCCESS", "支付成功", response);
                        }else{
                            log.info("wxNotifyCommonHandler的tranNo={}已经支付成功",outTradeNo);
                            responseResult("SUCCESS", "已经支付成功", response);
                        }
                    }else{
                        log.info("wxNotifyCommonHandler的tranNo={}未找到订单",outTradeNo);
                        responseResult("FAIL", "未找到订单号", response);
                    }
                }else{
                    log.info("wxNotifyCommonHandler的tranNo为空");
                    responseResult("FAIL", "订单号为空", response);
                }
            }else{
                log.info("wxNotifyCommonHandle的状态信息，transaction={}",JSONUtil.toJsonStr(transaction));
            }
        } catch (Exception e) {
            log.error("wxNotifyCommonHandler发生异常", e);
        }
    }

    /**
     * 订单查询结果
     */
    @Transactional
    public void orderQueryResult(CollectionDealerTradeEntity order, Map<String, String> config) {
        try {
            String url="https://api.mch.weixin.qq.com/v3/pay/transactions/out-trade-no/" +order.getTradeNo();
            //请求URL
            URIBuilder uriBuilder = new URIBuilder(url);
            uriBuilder.setParameter("mchid", config.get("mchid"));
            //完成签名并执行请求
            HttpGet httpGet = new HttpGet(uriBuilder.build());
            httpGet.addHeader("Accept", "application/json");
            CloseableHttpClient httpClient = getHttpClient(config);
            CloseableHttpResponse response = httpClient.execute(httpGet);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == 200) {
                order.setPayTime(new Date());
                order.setStatus(TradeStatus.SUCCESS);
                order.setStatusDes("已支付");
                collectionDealerTradeDao.update(order);
                //异步回调党费告诉通知结果
                notifyService.notifyPPMD(order);
            }
        }catch (Exception ex){
            log.error("WeiXinServices.orderQueryResult发生异常",ex);
        }
    }


    private CloseableHttpClient getHttpClient(Map<String, String> config) throws Exception {
        PrivateKey privateKey = PemUtil.loadPrivateKeyFromPath(config.get("privateKeyPath"));
        // 加载平台证书（mchId：商户号,mchSerialNo：商户证书序列号,apiV3Key：V3密钥）
        AutoUpdateCertificatesVerifier verifier = new AutoUpdateCertificatesVerifier(
                new WechatPay2Credentials(config.get("mchid"),
                new PrivateKeySigner(config.get("merchantSerialNumber"),privateKey)),
                config.get("apiV3Key").getBytes(StandardCharsets.UTF_8));
        // 初始化httpClient
        return WechatPayHttpClientBuilder.create()
                .withMerchant(config.get("mchid"), config.get("merchantSerialNumber"), privateKey)
                .withValidator(new WechatPay2Validator(verifier)).build();
    }


    /**
     *  返回结果
     */
    private void responseResult(String code, String message, HttpServletResponse response) throws IOException {
        // 创建返回对象
        ResponseObject responseObject = new ResponseObject(code, message);
        // // 设置响应内容类型
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        // 将对象转换为 JSON 字符串
        String jsonResponse =JSONUtil.toJsonStr(responseObject);
        // 将 JSON 字符串写入响应
        response.getWriter().write(jsonResponse);
        response.getWriter().flush();
    }




}
