package com.goodsogood.collectionpay.service;

import ccb.pay.api.util.CCBPayUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.goodsogood.collectionpay.consts.FlowType;
import com.goodsogood.collectionpay.consts.LogConfiger;
import com.goodsogood.collectionpay.consts.TradeStatus;
import com.goodsogood.collectionpay.dao.CollectionDealerAccountDao;
import com.goodsogood.collectionpay.dao.CollectionDealerAccountLogDao;
import com.goodsogood.collectionpay.dao.CollectionDealerTradeDao;
import com.goodsogood.collectionpay.entity.CollectionDealerAccountEntity;
import com.goodsogood.collectionpay.entity.CollectionDealerAccountLogEntity;
import com.goodsogood.collectionpay.entity.CollectionDealerTradeEntity;
import com.goodsogood.collectionpay.util.DateTimeUtil;
import com.goodsogood.collectionpay.util.HttpConnectionUtil;
import com.goodsogood.collectionpay.util.UUIDUtils;
import com.goodsogood.collectionpay.util.tonglian.SybUtil;
import com.icbc.api.internal.util.internal.util.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * 
 * @Description
 * 通联收银宝 接口
 * <AUTHOR>
 * @time 2019年4月8日下午6:28:50
 */
@Service("sybPayService")
public class SybPayService {
	
	private static final Logger logger = LoggerFactory.getLogger(LogConfiger.APP);
	
	@Autowired
	private AssetDepositChannelConfService assetDepositChannelConfService ;
	
	@Autowired
	private CollectionDealerTradeDao collectionDealerTradeDao ;
	
	@Autowired
	private CollectionDealerAccountDao collectionDealerAccountDao;
	
	@Autowired
	private CollectionDealerAccountLogDao collectionDealerAccountLogDao ;
	
	@Autowired
	private NotifyService notifyService;
	
	public Map<String,String> pay(Map<String,String> confMap ,String trxamt,String reqsn,String paytype,String body,String remark,String acct,String sub_appid) throws Exception{
		HttpConnectionUtil http = new HttpConnectionUtil(confMap.get("apiurl")+"/pay");
		http.init();
		TreeMap<String,String> params = new TreeMap<String,String>();
		params.put("cusid", confMap.get("cusid"));
		params.put("appid", confMap.get("appid"));
		params.put("version", "11");
		params.put("trxamt", String.valueOf(trxamt));
		params.put("reqsn", reqsn);
		params.put("paytype", paytype);
		params.put("randomstr", SybUtil.getValidatecode(16));
		params.put("body", body);
		params.put("remark", remark);
		params.put("acct", acct);
		//订单有效时间，以分为单位
		params.put("validtime", "20");
		params.put("notify_url", confMap.get("notifyUrl"));
		if(sub_appid!=null){
			params.put("sub_appid", sub_appid);
		}
        params.put("signtype", confMap.get("signType"));
        String appkey = confMap.get("appkey");
        params.put("sign", SybUtil.unionSign(params,appkey,confMap.get("signType")));
		byte[] bys = http.postParams(params, true);
		String result = new String(bys,"UTF-8");
		logger.info("Pay result={}",result);
		Map<String,String> map = handleResult(result,confMap.get("public_key"),confMap.get("signType"));
		return map;
		
	}



    @SuppressWarnings({ "rawtypes", "unchecked" })
    public static Map<String,String> handleResult(String result,String appkey,String signType) throws Exception{
        System.out.println("ret:"+result);
        Map map = SybUtil.json2Obj(result, Map.class);
        if(map == null){
            throw new Exception("返回数据错误");
        }
        if("0000".equals(map.get("trxstatus"))){
            TreeMap tmap = new TreeMap();
            tmap.putAll(map);
            if(SybUtil.validSign(tmap, appkey, signType)){
                System.out.println("签名成功");
                return map;
            }else{
                throw new Exception("验证签名失败");
            }
        }else{
            throw new Exception(map.get("retmsg").toString());
        }
    }


    public Map<String,String> query(Map<String,String> confMap ,String reqsn) throws Exception{
        HttpConnectionUtil http = new HttpConnectionUtil(confMap.get("apiurl")+"/query");
        http.init();
        TreeMap<String,String> params = new TreeMap<String,String>();
        params.put("cusid", confMap.get("cusid"));
        params.put("appid", confMap.get("appid"));
        params.put("version", "11");
        params.put("reqsn", reqsn);
        params.put("randomstr", SybUtil.getValidatecode(8));
        params.put("signtype", confMap.get("signType"));
        params.put("sign", SybUtil.unionSign(params,confMap.get("appkey"),confMap.get("signType")));
        byte[] bys = http.postParams(params, true);
        String result = new String(bys,"UTF-8");
        Map<String,String> map = handleResult(result,confMap.get("public_key"),confMap.get("signType"));
        return map;
    }

    /**
     * 查询建行数字人民币的支付结果
     * @param confMap
     * @param oriOrder
     * @return
     * @throws Exception
     */
    public Map<String,String> queryCBCDIGITAL(Map<String,String> confMap ,CollectionDealerTradeEntity oriOrder){
        try {
            String merInfo = "MERCHANTID="+confMap.get("MERCHANTID")+"&POSID="+
                    confMap.get("POSID")+"&BRANCHID="+confMap.get("BRANCHID");
            String PUB=confMap.get("PUB");
            StringBuffer tmp = new StringBuffer();
            tmp.append("&TXCODE=");
            tmp.append("PDPCX0");
            tmp.append("&Ordr_ID=");
            tmp.append(oriOrder.getTradeNo());
            //加密原串【PAY100接口定义的请求参数】
            String param = merInfo + tmp;
            //执行加密操作
            CCBPayUtil ccbPayUtil = new CCBPayUtil();
            String ccbParam = ccbPayUtil.makeCCBParam(param, PUB);
            //拼接请求串
            String url = confMap.get("queryUrl") + merInfo + "&ccbParam=" + ccbParam;
            Map<String, String> map = new HashMap<>();
            String result = HttpUtil.get(url);
            JSONObject jsonObject = com.alibaba.fastjson.JSON.parseObject(result);
            String success = jsonObject.get("SUCCESS").toString();
            String statusCode = jsonObject.getJSONArray("Detail_Grp").getJSONObject(0).get("STATUSCODE").toString();
            map.put("statusCode", statusCode);
            map.put("reCode", success);
            return map;
        }catch (Exception e){
            logger.error("查询建行数字人民币的支付结果异常",e);
            return null;
        }
    }

    /***
     * 处理支付回调
     * @param params
     * @return
     * @throws Exception
     */
    @Transactional
    public String recevPayResultNotify(Map<String, String> params) throws Exception{
        String cusorderid = params.get("cusorderid");
        String trxamt = params.get("trxamt");
        CollectionDealerTradeEntity oriOrder = this.collectionDealerTradeDao.findByTradeNo(cusorderid);
        logger.info("原始订单={}",oriOrder);

        if(oriOrder!=null &&oriOrder.getStatus()==TradeStatus.WAIT_PAY &&
                oriOrder.getChannelCode().startsWith("ALLINPAY_WX_") &&
                oriOrder.getAmount().longValue() ==Long.valueOf(trxamt) ){

            Map<String, String> confMap = this.assetDepositChannelConfService.
                    findOptionsByChannel(oriOrder.getChannelCode());
            Map<String,String> queryResult = this.query(confMap, cusorderid) ;
            String retcode = queryResult.get("retcode");

            if (retcode.equals("SUCCESS")) {
                String trxstatus = queryResult.get("trxstatus");
                String trxid = queryResult.get("trxid");
                logger.info("trxstatus={},cusorderid={}",trxstatus,cusorderid);

                if("0000".equals(trxstatus)){
                    oriOrder.setPayTime(new Date());
                    oriOrder.setStatus(TradeStatus.SUCCESS);
                    oriOrder.setStatusDes("已支付");
                    oriOrder.setChannelTxnId(trxid);
                    oriOrder.setAttach(JSON.toJSONString(queryResult));
                    this.collectionDealerTradeDao.update(oriOrder);
                    CollectionDealerAccountEntity dealerAccount = this.collectionDealerAccountDao
                            .findByDealerId(oriOrder.getDealerId());
                    long newBalance = dealerAccount.getBalance().longValue() +
                            oriOrder.getAmount().longValue();
                    dealerAccount.setBalance(newBalance);
                    dealerAccount.setWxpayNum(dealerAccount.getWxpayNum().longValue() + 1);
                    dealerAccount.setWxpaySum(dealerAccount.getWxpaySum().longValue() +
                            oriOrder.getAmount().longValue());
                    this.collectionDealerAccountDao.update(dealerAccount);
                    CollectionDealerAccountLogEntity log = new CollectionDealerAccountLogEntity();
                    log.setAction("通联收银宝微信支付");
                    log.setAmount(oriOrder.getAmount().longValue());
                    log.setChannel("微信");
                    log.setTradeNo(oriOrder.getTradeNo());
                    log.setContent(oriOrder.getBody());
                    log.setCreateTime(new Date());
                    log.setDealerBalance(newBalance);
                    log.setInOrOut(FlowType.IN + "");
                    log.setDealerId(oriOrder.getDealerId());
                    log.setLogId(UUIDUtils.uuid32());
                    log.setOpenId(oriOrder.getOpenId());
                    log.setChannelTxnId(trxid);
                    this.collectionDealerAccountLogDao.create(log);
                    Map<String, String> notifyParams = new HashMap<>();
                    notifyParams.put("tradeNo", oriOrder.getTradeNo());
                    notifyParams.put("outTranNumber", oriOrder.getOutTradeNo());
                    notifyParams.put("createTime", DateTimeUtil.format(oriOrder.getCreateTime()));
                    notifyParams.put("payTime", DateTimeUtil.format(oriOrder.getPayTime()));
                    notifyParams.put("status", oriOrder.getStatus() + "");
                    notifyParams.put("statusDes", oriOrder.getStatusDes() + "");
                    notifyParams.put("amount", oriOrder.getAmount().toString());
                    notifyParams.put("openid", oriOrder.getOpenId());
                    notifyParams.put("channel", oriOrder.getChannel());
                    this.notifyService.submit(oriOrder.getNotifyUrl(), notifyParams);
                    logger.info("回调处理完成!tradeNo={},", oriOrder.getTradeNo());
                    return "success";
                }
            }
        }
        else{
            return "FAIL";
        }
        return "success";
    }
}
