package com.goodsogood.collectionpay.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.XmlUtil;
import com.alibaba.fastjson.JSON;
import com.bocnet.common.security.PKCS7Tool;
import com.github.kevinsawicki.http.HttpRequest;
import com.goodsogood.collectionpay.consts.*;
import com.goodsogood.collectionpay.dao.CollectionDealerTradeDao;
import com.goodsogood.collectionpay.entity.CollectionDealerTradeEntity;
import com.goodsogood.collectionpay.exception.AssetServiceException;
import com.goodsogood.collectionpay.model.bank.ICBCAggregatePaymentRequestV1;
import com.goodsogood.collectionpay.model.request.TradeCreateRequest;
import com.goodsogood.collectionpay.model.response.TradeCreateResponse;
import com.goodsogood.collectionpay.redis.RedisPool;
import com.goodsogood.collectionpay.unionpay.sdk.AcpService;
import com.goodsogood.collectionpay.unionpay.sdk.LogUtil;
import com.goodsogood.collectionpay.unionpay.sdk.SDKConfig;
import com.goodsogood.collectionpay.util.*;
import com.icbc.api.DefaultIcbcClient;
import com.icbc.api.IcbcConstants;
import com.icbc.api.UiIcbcClient;
import com.icbc.api.request.AggregatePaymentRequestV1;
import com.icbc.api.request.CardbusinessAggregatepayB2cOnlineConsumepurchaseRequestV1;
import com.icbc.api.request.CardbussinessZfbH5UiH5ConsumptionRequestV1;
import com.icbc.api.response.CardbusinessAggregatepayB2cOnlineConsumepurchaseResponseV1;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import redis.clients.jedis.Jedis;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @program: collection-pay
 * @description: 微信services
 * @author: Mr.LiGuoYong
 * @create: 2020-08-31 14:27
 **/
@Service
public class WenXinServices {

    private static final Logger logger = LoggerFactory.getLogger(LogConfiger.APP);
    @Autowired
    private CollectionDealerTradeDao collectionDealerTradeDao ;
    @Autowired
    private TradeNoGenerater tradeNoGenerater ;
    @Autowired
    private RedisPool redisPool ;
    @Autowired
    private SybPayService sybPayService;
    @Autowired
    private ABCBankService abcBankService;
    @Autowired
    private DESCoderUtil desCoderUtil;

//    @Autowired
//    private FileUtils fileUtils;

    /**
     * 市直机关 中国银行
     * @param resp
     * @param request
     * @param order
     * @param config
     */
    public void createResponseBOC(TradeCreateResponse resp,
                                  TradeCreateRequest request,
                                  CollectionDealerTradeEntity order,
                                  Map<String, String> config){
        String wxTradeType = request.getWxTradeType();
        if (StringUtils.isEmpty(wxTradeType)) {
            wxTradeType = "NATIVE";// 默认扫码方式
        }
        // trade_type=JSAPI时（即公众号支付），此参数必传
        if ("JSAPI".equals(wxTradeType) && StringUtils.isEmpty(request.getOpenId())) {
            throw new AssetServiceException(ErrorCode.MISSING_PARAMETER, "缺少参数公众号[openid]");
        }
        /*
         * 生成业务订单
         */
        long price = Long.parseLong(request.getAmount());
        order.setAmount(price);
        order.setBody(request.getSubject());
        order.setSubject(request.getSubject());
        order.setDealerId(Integer.valueOf(request.getDealerId()));
        order.setOpenId(request.getOpenId());
        order.setChannel("微信");
        order.setChannelCode(request.getChannel());
        order.setCreateTime(new Date());
        order.setStatus(TradeStatus.WAIT_PAY);
        order.setStatusDes("待支付");
        order.setTradeNo(tradeNoGenerater.getNewTradeNo(TradeTypeCode.COLLECTION_WX));
        order.setUserId(request.getUserId());
        order.setBody(request.getBody());
        order.setNotifyUrl(request.getNotifyUrl());
        order.setVersion(0L);

        this.collectionDealerTradeDao.create(order);
        String nonce_str = UUIDUtils.uuid32();
        /*
         * 发起微信预支付
         */
        String payResult;
        try {
            String detail ="";
            String ip = config.get("defaultIp");// WeixinConfig.defaultIp;
            payResult = sendWxPayRequest(config, request.getSubject(), detail, order.getTradeNo(), price, ip,
                    wxTradeType, request.getOpenId(), nonce_str,request);
        } catch (Exception e) {
            logger.error("发起预订单失败", e);
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "微信下单失败");
        }

        Map<String, String> resultMap;
        try {
            resultMap = XMLUtil.parseXml(payResult);
        } catch (Exception e) {
            logger.error("预订单返回数据错误", e);
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "预订单返回数据错误");
        }
        if (null==resultMap) {
            logger.error("预订单返回数据错误: resultMap is null");
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "微信下单返回数据错误");
        }

        String return_code = resultMap.get("return_code");
        if (!"SUCCESS".equals(return_code)) {
            String return_msg = resultMap.get("return_msg");
            logger.error("预订单错误:" + return_msg);
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "微信下单错误: " + return_msg);
        }
        String result_code = resultMap.get("result_code");
        if (!"SUCCESS".equals(result_code)) {
            String err_code_des = resultMap.get("err_code_des");
            logger.error("预订单错误: " + err_code_des);
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "微信下单错误: " + err_code_des);
        }

        try {
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("prepayId", resultMap.get("prepay_id"));
            parameters.put("tradeType", resultMap.get("trade_type"));
            parameters.put("tradeNo", order.getTradeNo());
            if ("JSAPI".equals(request.getWxTradeType())) {
                parameters.put("appId", resultMap.get("jsapi_appid"));
                parameters.put("timeStamp", resultMap.get("jsapi_timestamp"));
                parameters.put("nonceStr", resultMap.get("jsapi_noncestr"));
                parameters.put("package", resultMap.get("jsapi_package"));
                parameters.put("signType", resultMap.get("jsapi_signtype"));
                parameters.put("paySign", resultMap.get("jsapi_paysign"));
            }
            if ("NATIVE".equals(request.getWxTradeType())) {
                if (resultMap.get("code_url") != null) {
                    // 将二维码地址生成 一个base64的字符串返回
                    parameters.put("payCodeUrl", resultMap.get("code_url"));// 二维码地址
                    parameters.put("payBase64", QRCodeUtil.generateQRBase64(resultMap.get("code_url"),
                            200, 200));// 二维码地址
                }
            }
            resp.setData(parameters);
            order.setPrepayId(resultMap.get("prepay_id"));
        } catch (Exception e) {
            logger.error("微信支付下单失败",e);
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "微信支付下单失败");
        }
    }

    /**
     * 市直机关 中国银行 H5 支付宝支付
     * @param resp
     * @param request
     * @param order
     * @param config
     */
    public void createResponseBOCWEB(TradeCreateResponse resp,
                                  TradeCreateRequest request,
                                  CollectionDealerTradeEntity order,
                                  Map<String, String> config){
        String wxTradeType = request.getWxTradeType();
        if (StringUtils.isEmpty(wxTradeType)) {
            wxTradeType = "NATIVE";// 默认扫码方式
        }
        /*
         * 生成业务订单
         */
        long price = Long.parseLong(request.getAmount());
        order.setAmount(price);
        order.setBody(request.getSubject());
        order.setSubject(request.getSubject());
        order.setDealerId(Integer.valueOf(request.getDealerId()));
        order.setOpenId(request.getOpenId());
        order.setChannel("烟草-中行-支付宝");
        order.setChannelCode(request.getChannel());
        order.setCreateTime(new Date());
        order.setStatus(TradeStatus.WAIT_PAY);
        order.setStatusDes("待支付");
        order.setTradeNo(tradeNoGenerater.getNewTradeNo(TradeTypeCode.COLLECTION_WX));
        order.setUserId(request.getUserId());
        order.setBody(request.getBody());
        order.setNotifyUrl(request.getNotifyUrl());
        order.setVersion(0L);

        this.collectionDealerTradeDao.create(order);
        String nonce_str = UUIDUtils.uuid32();
        /*
         * 发起微信预支付
         */
        String payResult;
        try {
            String detail ="";
            String ip = config.get("defaultIp");// WeixinConfig.defaultIp;
            payResult = sendAliPayRequest(config, request.getSubject(), detail, order.getTradeNo(), price, ip,
                    wxTradeType, request.getOpenId(), nonce_str,request);
        } catch (Exception e) {
            logger.error("发起预订单失败", e);
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "微信下单失败");
        }
        Map<String, String> resultMap;
        try {
            resultMap = XMLUtil.parseXml(payResult);
        } catch (Exception e) {
            logger.error("预订单返回数据错误", e);
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "预订单返回数据错误");
        }
        if (null==resultMap) {
            logger.error("预订单返回数据错误: resultMap is null");
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "微信下单返回数据错误");
        }

        String return_code = resultMap.get("return_code");
        if (!"SUCCESS".equals(return_code)) {
            String return_msg = resultMap.get("return_msg");
            logger.error("预订单错误:" + return_msg);
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "微信下单错误: " + return_msg);
        }
        String result_code = resultMap.get("result_code");
        if (!"SUCCESS".equals(result_code)) {
            String err_code_des = resultMap.get("err_code_des");
            logger.error("预订单错误: " + err_code_des);
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "微信下单错误: " + err_code_des);
        }

        try {
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("prepayId", resultMap.get("prepay_id"));
            parameters.put("tradeType", resultMap.get("trade_type"));
            parameters.put("tradeNo", order.getTradeNo());
            if ("JSAPI".equals(request.getWxTradeType())) {
                parameters.put("appId", resultMap.get("jsapi_appid"));
                parameters.put("timeStamp", resultMap.get("jsapi_timestamp"));
                parameters.put("nonceStr", resultMap.get("jsapi_noncestr"));
                parameters.put("package", resultMap.get("jsapi_package"));
                parameters.put("signType", resultMap.get("jsapi_signtype"));
                parameters.put("paySign", resultMap.get("jsapi_paysign"));
            }
            if ("NATIVE".equals(request.getWxTradeType())) {
                if (resultMap.get("code_url") != null) {
                    // 将二维码地址生成 一个base64的字符串返回
                    parameters.put("payCodeUrl", resultMap.get("code_url"));// 二维码地址
                    parameters.put("payBase64", QRCodeUtil.generateQRBase64(resultMap.get("code_url"),
                            200, 200));// 二维码地址
                }
            }
            resp.setData(parameters);
            order.setPrepayId(resultMap.get("prepay_id"));
        } catch (Exception e) {
            logger.error("微信支付下单失败",e);
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "微信支付下单失败");
        }
    }


    /**
     * 工商银行微信-沙坪坝区=ICBC_WX_SPB
     * @param resp
     * @param request
     * @param order
     * @param config
     */
    public void createResponseCBC(TradeCreateResponse resp,
                                  TradeCreateRequest request,
                                  CollectionDealerTradeEntity order,
                                  Map<String, String> config){
        Long price = Long.valueOf(request.getAmount());
        order.setAmount(price);
        order.setBody(request.getSubject());
        order.setSubject(request.getSubject());
        order.setDealerId(Integer.valueOf(request.getDealerId()));
        order.setOpenId(request.getOpenId());
        order.setChannel("微信");
        order.setChannelCode(request.getChannel());
        order.setCreateTime(new Date());
        order.setStatus(TradeStatus.WAIT_PAY);
        order.setStatusDes("待支付");
        order.setTradeNo(tradeNoGenerater.getNewTradeNo(TradeTypeCode.COLLECTION_WX));
        order.setUserId(request.getUserId());
        order.setBody(request.getBody());
        order.setNotifyUrl(request.getNotifyUrl());
        order.setVersion(0L);

        this.collectionDealerTradeDao.create(order);

        String merId = config.get("mer_id");
        String appId = config.get("app_id");
        String encryptKey = config.get("encrypt_key");
        String rsaPrivateKey = config.get("rsa_private_key");
        String tradeUrl = config.get("tradeUrl");
        String notifyUrl = config.get("notifyUrl");
        UiIcbcClient client = new UiIcbcClient(appId, IcbcConstants.SIGN_TYPE_RSA2,
                rsaPrivateKey, "UTF-8", "AES", encryptKey);
        AggregatePaymentRequestV1 ICBCRequest = new AggregatePaymentRequestV1();
        ICBCRequest.setServiceUrl(tradeUrl);
        ICBCAggregatePaymentRequestV1 bizContent = new ICBCAggregatePaymentRequestV1();
        bizContent.setMerId(merId);
        bizContent.setOutTradeNo(order.getTradeNo());
        bizContent.setInstallTimes("1");
        bizContent.setTranType("OnlinePay");
        bizContent.setOrderDate(DateTimeUtil.format4ICBC(new Date()));
        bizContent.setEndTime(DateTimeUtil.format4ICBC(new Date(System.currentTimeMillis() + (1000 * 60 * 20))));
        bizContent.setGoodsBody(request.getSubject());
        bizContent.setGoodsDetail("");
        bizContent.setOrderAmount(price.toString());
        bizContent.setPayLimit("no_credit");
        bizContent.setSpbillCreateIp("***************");
        bizContent.setNotifyUrl(notifyUrl);
        bizContent.setResultType("0");
        bizContent.setReturnUrl(request.getReturnUrl());
        bizContent.setNotifyType("HS");
        bizContent.setInterfaceVersion("*******");
        bizContent.setBackup1("");
        bizContent.setBackup2("");
        bizContent.setBackup3("");
        bizContent.setBackup4("");
        ICBCRequest.setBizContent(bizContent);
        String form = "";
        Jedis jedis = null;
        try {
            jedis = redisPool.getResource();
            form = client.buildPostForm(ICBCRequest);
            logger.info("ICBC respBody=\n{}", form);
            jedis.setex(RedisKey.COMPANY_RECHARGE_ALIPAY_FORM + order.getTradeNo(),
                    1200, form);
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("tradeNo", order.getTradeNo());
            resp.setData(parameters);
        } catch (Exception exception){
            System.out.println(exception);
            logger.error("执行createResponseCBC---发生异常-{}",exception.getMessage());
        } finally {
            if (jedis != null) {
                redisPool.returnResource(jedis);
            }
        }

    }

    public static void main(String[] args) {
        String orderAmount = new BigDecimal(27840).divide(new BigDecimal(100))
                .setScale(2, RoundingMode.HALF_UP).toString();
        System.out.println(orderAmount);
    }

    /**
     * 通联支付
     * @param resp
     * @param request
     * @param order
     * @param config
     */
    public void createResponseALLINPAY(TradeCreateResponse resp,
                                  TradeCreateRequest request,
                                  CollectionDealerTradeEntity order,
                                  Map<String, String> config){
        Long price = Long.valueOf(request.getAmount());
        order.setAmount(price);
        order.setBody(request.getSubject());
        order.setSubject(request.getSubject());
        order.setDealerId(Integer.valueOf(request.getDealerId()));
        order.setOpenId(request.getOpenId());
        order.setChannel("微信");
        order.setChannelCode(request.getChannel());
        order.setCreateTime(new Date());
        order.setStatus(TradeStatus.WAIT_PAY);
        order.setStatusDes("待支付");
        order.setTradeNo(tradeNoGenerater.getNewTradeNo(TradeTypeCode.COLLECTION_ALLINPAY));
        order.setUserId(request.getUserId());
        order.setBody(request.getBody());
        order.setNotifyUrl(request.getNotifyUrl());
        order.setVersion(0L);

        this.collectionDealerTradeDao.create(order);
        request.setNotifyUrl(config.get("notifyUrl"));
        /* W01	微信扫码支付 W02	微信JS支付 W03	微信APP支付 */
        String paytype = "W02";
        if("NATIVE".equals(request.getWxTradeType())){
            paytype = "W01";
        }
        if("APP".equals(request.getWxTradeType())){
            paytype = "W03";
        }
        logger.info("{} notifyUrl={}",request.getChannel() ,request.getNotifyUrl());
        Map<String,String> resultMap = null;
        try {
            resultMap = sybPayService.pay(config, request.getAmount(), order.getTradeNo(),
                    paytype, request.getSubject(), request.getSubject(),
                    request.getOpenId(), request.getWxAppId());
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        logger.info("SybPayService pay resultMap={}", JSON.toJSONString(resultMap, true));
        String retcode = resultMap.get("retcode");
        String errmsg = resultMap.get("errmsg");

        if (retcode.equals("SUCCESS") && org.springframework.util.StringUtils.isEmpty(errmsg)) {
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("tradeNo", order.getTradeNo());
            String payinfo = resultMap.get("payinfo");

            if("JSAPI".equals(request.getWxTradeType())){
                Map<String,String> payinfoMap = JSON.parseObject(payinfo, Map.class);
                parameters.put("appId", payinfoMap.get("appId"));
                parameters.put("prepayId", payinfoMap.get("package").replace("prepay_id=", ""));
                parameters.put("timeStamp", payinfoMap.get("timeStamp"));
                parameters.put("nonceStr", payinfoMap.get("nonceStr"));
                parameters.put("package", payinfoMap.get("package"));
                parameters.put("signType",payinfoMap.get("signType"));
                parameters.put("paySign", payinfoMap.get("paySign"));
            }
            if ("NATIVE".equals(request.getWxTradeType())) {
                if (!org.springframework.util.StringUtils.isEmpty(payinfo)) {
                    // 将二维码地址生成 一个base64的字符串返回
                    parameters.put("payCodeUrl", payinfo);// 二维码地址
                    parameters.put("payBase64", QRCodeUtil.generateQRBase64(payinfo,
                            200, 200));// 二维码地址
                }
            }
            resp.setData(parameters);
        }
        else{
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "下单失败!"+errmsg);
        }
    }

    public void createResponseUNIONPAY(TradeCreateResponse resp,
                                       TradeCreateRequest request,
                                       CollectionDealerTradeEntity order,
                                       Map<String, String> config){

        SDKConfig.getConfig().loadPropertiesFromMap(config);

        String encoding = "UTF-8";
        String merId = config.get("merId");
        String version = config.get("acpsdk.version");
        String signMethod = config.get("acpsdk.signMethod");
        String frontUrl = config.get("acpsdk.frontUrl");
        String backUrl = config.get("acpsdk.backUrl");
        String frontTransUrl = config.get("acpsdk.frontTransUrl");
        String signCertPath = config.get("acpsdk.signCert.path");
        String signCertPwd = config.get("acpsdk.signCert.pwd");

        String orderId = tradeNoGenerater.getNewTradeNo(TradeTypeCode.COLLECTION_UNIONPAY);
        String txnTime = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        System.out.println("txnTime="+txnTime);

        Map<String, String> requestData = new HashMap<String, String>();

        /*** 银联全渠道系统，产品参数，除了encoding自行选择外其他不需修改 ***/
        requestData.put("version", version); // 版本号，全渠道默认值
        requestData.put("encoding", encoding); // 字符集编码，可以使用UTF-8,GBK两种方式
        requestData.put("signMethod", signMethod); // 签名方法
        requestData.put("txnType", "01"); // 交易类型 ，01：消费
        requestData.put("txnSubType", "01"); // 交易子类型， 01：自助消费
        requestData.put("bizType", "000201"); // 业务类型，B2C网关支付，手机wap支付
        requestData.put("channelType", "08"); // 渠道类型，这个字段区分B2C网关支付和手机wap支付；07：PC,平板
        // 08：手机

        /*** 商户接入参数 ***/
        requestData.put("merId", merId); // 商户号码，请改成自己申请的正式商户号或者open上注册得来的777测试商户号
        requestData.put("accessType", "0"); // 接入类型，0：直连商户
        requestData.put("orderId", orderId); // 商户订单号，8-40位数字字母，不能含“-”或“_”，可以自行定制规则
        requestData.put("txnTime", txnTime); // 订单发送时间，取系统时间，格式为YYYYMMDDhhmmss，必须取当前时间，否则会报txnTime无效
        requestData.put("currencyCode", "156"); // 交易币种（境内商户一般是156 人民币）
        requestData.put("txnAmt", request.getAmount()); // 交易金额，单位分，不要带小数点
        requestData.put("reqReserved", request.getChannel());
        // //请求方保留域，如需使用请启用即可；透传字段（可以实现商户自定义参数的追踪）本交易的后台通知,对本交易的交易状态查询交易、对账文件中均会原样返回，商户可以按需上传，长度为1-1024个字节。出现&={}[]符号时可能导致查询接口应答报文解析失败，建议尽量只传字母数字并使用|分割，或者可以最外层做一次base64编码(base64编码之后出现的等号不会导致解析失败可以不用管)。

        requestData.put("riskRateInfo", "{commodityName=" + request.getSubject() + "}");

        // 前台通知地址 （需设置为外网能访问 http https均可），支付成功后的页面
        // 点击“返回商户”按钮的时候将异步通知报文post到该地址
        // 如果想要实现过几秒中自动跳转回商户页面权限，需联系银联业务申请开通自动返回商户权限
        // 异步通知参数详见open.unionpay.com帮助中心 下载 产品接口规范 网关支付产品接口规范 消费交易 商户通知

        requestData.put("frontUrl", request.getReturnUrl() ==null ? backUrl :  request.getReturnUrl());

        // 后台通知地址（需设置为【外网】能访问 http
        // https均可），支付成功后银联会自动将异步通知报文post到商户上送的该地址，失败的交易银联不会发送后台通知
        // 后台通知参数详见open.unionpay.com帮助中心 下载 产品接口规范 网关支付产品接口规范 消费交易 商户通知
        // 注意:1.需设置为外网能访问，否则收不到通知 2.http https均可
        // 3.收单后台通知后需要10秒内返回http200或302状态码
        // 4.如果银联通知服务器发送通知后10秒内未收到返回状态码或者应答码非http200，那么银联会间隔一段时间再次发送。总共发送5次，每次的间隔时间为0,1,2,4分钟。
        // 5.后台通知地址如果上送了带有？的参数，例如：http://abc/web?a=b&c=d
        // 在后台通知处理程序验证签名之前需要编写逻辑将这些字段去掉再验签，否则将会验签失败
        requestData.put("backUrl", backUrl);

        // 订单超时时间。
        // 超过此时间后，除网银交易外，其他交易银联系统会拒绝受理，提示超时。
        // 跳转银行网银交易如果超时后交易成功，会自动退款，大约5个工作日金额返还到持卡人账户。
        // 此时间建议取支付时的北京时间加15分钟。
        // 超过超时时间调查询接口应答origRespCode不是A6或者00的就可以判断为失败。
        requestData.put("payTimeout",
                new SimpleDateFormat("yyyyMMddHHmmss").format(new Date().getTime() + 15 * 60 * 1000));

        //////////////////////////////////////////////////
        //
        // 报文中特殊用法请查看 PCwap网关跳转支付特殊用法.txt
        //
        /** 请求参数设置完毕，以下对请求参数进行签名并生成html表单，将表单写入浏览器跳转打开银联页面 **/
        //Map<String, String> submitFromData = AcpService.sign(requestData, encoding); // 报文中certId,signature的值是在signData方法中获取并自动赋值的，只要证书配置正确即可。
        Map<String, String> submitFromData = AcpService.signByCertInfo(requestData,signCertPath,signCertPwd, "utf-8");

        String requestFrontUrl = frontTransUrl; // 获取请求银联的前台地址：对应属性文件acp_sdk.properties文件中的acpsdk.frontTransUrl
        String html = AcpService.createAutoFormHtml(requestFrontUrl, submitFromData, encoding); // 生成自动跳转的Html表单

        LogUtil.writeLog("打印请求HTML，此为请求报文，为联调排查问题的依据：" + html);
        // 将生成的html写到浏览器中完成自动跳转打开银联支付页面；这里调用signData之后，将html写到浏览器跳转到银联页面之前均不能对html中的表单项的名称和值进行修改，如果修改会导致验签不通过

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("tradeNo", orderId);
        Jedis jedis = null;
        try {
            jedis = redisPool.getResource() ;
            jedis.setex(RedisKey.COMPANY_RECHARGE_ALIPAY_FORM + orderId,1200, html);
        } finally {
            if(jedis!=null){
                redisPool.returnResource(jedis);
            }
        }
        order.setAmount(Long.valueOf(request.getAmount()));
        order.setBody(request.getSubject());
        order.setSubject(request.getSubject());
        order.setDealerId(Integer.valueOf(request.getDealerId()));
        order.setOpenId(request.getOpenId());
        order.setChannel("银联");
        order.setChannelCode(request.getChannel());
        order.setCreateTime(new Date());
        order.setStatus(TradeStatus.WAIT_PAY);
        order.setStatusDes("待支付");
        order.setTradeNo(orderId);
        order.setBody(request.getBody());
        order.setNotifyUrl(request.getNotifyUrl());
        order.setVersion(0L);
        order.setTxnTime(txnTime);
        collectionDealerTradeDao.create(order);
        resp.setData(parameters);
    }


    public TradeCreateResponse createResponseWXPAY(TradeCreateResponse resp,
                                   TradeCreateRequest request,
                                   CollectionDealerTradeEntity order,
                                   Map<String, String> config){

        String wxTradeType = request.getWxTradeType();
        if (StringUtils.isEmpty(wxTradeType)) {
            wxTradeType = "NATIVE";// 默认扫码方式
        }
        // trade_type=JSAPI时（即公众号支付），此参数必传
        if ("JSAPI".equals(wxTradeType) && StringUtils.isEmpty(request.getOpenId())) {
            throw new AssetServiceException(ErrorCode.MISSING_PARAMETER, "缺少参数公众号[openid]");
        }
        /*
         * 生成业务订单
         */
        int price = Integer.valueOf(request.getAmount());
        String merchatKey = config.get("merchatKey");// WeixinConfig.merchatKey;
        order.setAmount((long) price);
        order.setBody(request.getSubject());
        order.setSubject(request.getSubject());
        order.setDealerId(Integer.valueOf(request.getDealerId()));
        order.setOpenId(request.getOpenId());
        order.setChannel("微信");
        order.setChannelCode(request.getChannel());
        order.setCreateTime(new Date());
        order.setStatus(TradeStatus.WAIT_PAY);
        order.setStatusDes("待支付");
        order.setTradeNo(tradeNoGenerater.getNewTradeNo(TradeTypeCode.COLLECTION_WX));
        order.setUserId(request.getUserId());
        order.setBody(request.getBody());
        order.setNotifyUrl(request.getNotifyUrl());
        order.setVersion(0L);

        this.collectionDealerTradeDao.create(order);
        String nonce_str = UUIDUtils.uuid32();
        /*
         * 发起微信预支付
         */
        String payResult;
        String erCodeRedirectUrl = config.get("pageUrl");
        try {
            String detail ="";
            String ip = config.get("defaultIp");// WeixinConfig.defaultIp;
            //纯微信支付的时候 不用wx_appId
            request.setWxAppId(null);
            payResult = sendWxPayRequest(config, request.getSubject(), detail, order.getTradeNo(), price, ip,
                    wxTradeType, request.getOpenId(), nonce_str, request);
        } catch (Exception e) {
            order.setStatus(TradeStatus.FAIL);
            order.setStatusDes("支付失败");
            logger.error("发起预订单失败", e);
            logger.error("error!",e);
            resp.setCode(Integer.valueOf(ErrorCode.BUSINESS_ERROR));
            resp.setMessage("支付异常(082)请稍后再试");
            return resp ;
        }

        Map<String, String> resultMap;
        try {
            resultMap = XMLUtil.parseXml(payResult);
        } catch (Exception e) {
            order.setStatus(TradeStatus.FAIL);
            order.setStatusDes("支付失败");
            logger.error("预订单返回数据错误", e);
            logger.error("error!",e);
            resp.setCode(Integer.valueOf(ErrorCode.BUSINESS_ERROR));
            resp.setMessage("支付异常(082)请稍后再试");
            return resp ;
        }
        if (resultMap == null) {
            logger.error("预订单返回数据错误: resultMap is null");
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "微信下单返回数据错误");
        }

        String return_code = resultMap.get("return_code");
        if (!"SUCCESS".equals(return_code)) {
            String return_msg = resultMap.get("return_msg");
            logger.error("预订单错误:" + return_msg);
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "微信下单错误: " + return_msg);
        }
        String result_code = resultMap.get("result_code");
        if (!"SUCCESS".equals(result_code)) {
            String err_code_des = resultMap.get("err_code_des");
            logger.error("预订单错误: " + err_code_des);
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "微信下单错误: " + err_code_des);
        }

        try {
            Map<String, Object> data = new HashMap<>();
            if ("JSAPI".equals(wxTradeType)) {
                Long timeStamp=new Date().getTime();
                SortedMap<Object, Object> parameters = new TreeMap<>();
                parameters.put("appId", resultMap.get("appid"));
                parameters.put("timeStamp", timeStamp);
                parameters.put("nonceStr", resultMap.get("nonce_str"));
                parameters.put("package","prepay_id="+resultMap.get("prepay_id"));
                parameters.put("signType", "MD5");
                //parameters.put("paySign", resultMap.get("sign"));
                //重写签名参数
                String sign = WeChatUtil.createSign(parameters, merchatKey);
                parameters.put("paySign", sign);

                //重写返回值
                data.put("appId", resultMap.get("appid"));
                data.put("timeStamp", timeStamp);
                data.put("nonceStr", resultMap.get("nonce_str"));
                data.put("package","prepay_id="+resultMap.get("prepay_id"));
                data.put("signType", "MD5");
                data.put("paySign", sign);
                data.put("tradeNo", order.getTradeNo());
            }

            if ("NATIVE".equals(wxTradeType)) {
                if (resultMap.get("code_url") != null) {
                    BigDecimal amountBigDecimal = AmountUtil.BigDecimalDivide(
                            new BigDecimal(order.getAmount()), 100D,
                            2);
                    DecimalFormat currencydf = new DecimalFormat("#,##0.00");// 货币格式化设置
                    String amountStr = currencydf.format(amountBigDecimal);
                    // 将二维码地址生成 一个base64的字符串返回
                    data.put("payCodeUrl", resultMap.get("code_url"));// 二维码地址
                    data.put("payBase64", QRCodeUtil.generateQRBase64(resultMap.get("code_url"),
                            300, 300));// 二维码地址
                    data.put("pageUrl", erCodeRedirectUrl+order.getTradeNo());
                    Jedis jedis = null;
                    try {
                        jedis = redisPool.getResource() ;
                        jedis.setex(RedisKey.COMPANY_RECHARGE_ALIPAY_FORM + order.getTradeNo(),
                                1200,
                                QRCodeUtil.generateQRBase64HtmlPage(
                                        data.get("payBase64").toString(),amountStr));
                    } finally {
                        if(jedis!=null){
                            redisPool.returnResource(jedis);
                        }
                    }

                }
            }
            resp.setData(data);
            order.setPrepayId(resultMap.get("prepay_id"));
            return resp;
        } catch (Exception e) {
            logger.error("微信支付下单失败",e);
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "微信支付下单失败");
        }
    }



    /**
     * 工商银行微信-沙坪坝区=ICBC_WX_SPB
     * @param resp
     * @param request
     * @param order
     * @param config
     */
    public void createResponseABC(TradeCreateResponse resp,
                                  TradeCreateRequest request,
                                  CollectionDealerTradeEntity order,
                                  Map<String, String> config){
        Long price = Long.valueOf(request.getAmount());
        order.setAmount(price);
        order.setBody(request.getSubject());
        order.setSubject(request.getSubject());
        order.setDealerId(Integer.valueOf(request.getDealerId()));
        order.setOpenId(request.getOpenId());
        order.setChannel("微信");
        order.setChannelCode(request.getChannel());
        order.setCreateTime(new Date());
        order.setStatus(TradeStatus.WAIT_PAY);
        order.setStatusDes("待支付");
        order.setTradeNo(tradeNoGenerater.getNewTradeNo(TradeTypeCode.COLLECTION_WX));
        order.setUserId(request.getUserId());
        order.setBody(request.getBody());
        order.setNotifyUrl(request.getNotifyUrl());
        order.setVersion(0L);

        this.collectionDealerTradeDao.create(order);
        int confIndex = Integer.valueOf(config.get("confIndex"));
        request.setNotifyUrl(config.get("notifyUrl"));
        logger.info("{} notifyUrl={}",request.getChannel() ,request.getNotifyUrl());
        com.abc.pay.client.JSON result = abcBankService.pay(request,confIndex,order.getTradeNo());
        String ReturnCode = result.GetKeyValue("ReturnCode");
        String ErrorMessage = result.GetKeyValue("ErrorMessage");
        if (ReturnCode.equals("0000")) {
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("tradeNo", order.getTradeNo());
            String QRURL = result.GetKeyValue("QRURL");
            String APP = result.GetKeyValue("APP");
            String JSAPI = result.GetKeyValue("JSAPI");
            logger.info("QRURL={}",QRURL);
            logger.info("APP={}",APP);
            logger.info("JSAPI={}",JSAPI);
            if("JSAPI".equals(request.getWxTradeType())){
                Map<String,String> resultMap = com.alibaba.fastjson.JSON.parseObject(JSAPI, HashMap.class);
                parameters.put("appId", resultMap.get("sub_appId"));
                parameters.put("timeStamp", resultMap.get("timestamp"));
                parameters.put("nonceStr", resultMap.get("nonceStr"));
                parameters.put("package", resultMap.get("package"));
                parameters.put("signType", resultMap.get("signType"));
                parameters.put("paySign", resultMap.get("paySign"));
            }
            if ("NATIVE".equals(request.getWxTradeType())) {
                if (!org.springframework.util.StringUtils.isEmpty(QRURL)) {
                    // 将二维码地址生成 一个base64的字符串返回
                    parameters.put("payCodeUrl", QRURL);// 二维码地址
                    parameters.put("payBase64", QRCodeUtil.generateQRBase64(QRURL,
                            200, 200));// 二维码地址
                }
            }
            resp.setData(parameters);
        }
        else{
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "下单失败!"+ErrorMessage);
        }
    }


    /**
     * @方法名称:sendWxPayRequest
     * @内容摘要: ＜发送统一下单请求＞
     */
    //@Override
    private String sendWxPayRequest(Map<String, String> config, String body,
                                    String detail,String outTradeNo,
                                    long totalFee, String spBillCreateIP, String trade_type,
                                    String openid,String nonce_str,
                                    TradeCreateRequest tradeCreateRequest) throws Exception {
        String notify_url = config.get("notifyUrl");// WeixinConfig.notify_url;//回调
        String appId = config.get("appId");// WeixinConfig.AppId; //微信APPID
        String mch_id = config.get("mchId");// WeixinConfig.mch_id; //商户ID
        String merchatKey = config.get("merchatKey");// WeixinConfig.merchatKey;
        //商户秘钥
        String storeId=config.get("store_id");
        String storeName=config.get("store_name");
        //设置订单过期时间
        Calendar nowTime = Calendar.getInstance();
        nowTime.add(Calendar.MINUTE,Integer.parseInt(config.get("expireTime")));
        String startDate=DateTimeUtil.getFormatDateStr(new Date(),"yyyyMMddHHmmss");
        String expireDate=DateTimeUtil.getFormatDateStr(nowTime.getTime(),"yyyyMMddHHmmss");

        SortedMap<Object, Object> parameters = new TreeMap<Object, Object>();
        parameters.put("appid", appId);
        parameters.put("mch_id", mch_id);
        parameters.put("attach", detail);
        parameters.put("body", body);
        parameters.put("nonce_str", nonce_str);
        parameters.put("notify_url", notify_url);
        parameters.put("out_trade_no", outTradeNo);
        parameters.put("spbill_create_ip", spBillCreateIP);
        parameters.put("total_fee", totalFee);
        parameters.put("trade_type", trade_type);
        parameters.put("time_start", startDate);
        parameters.put("time_expire", expireDate);
        //升级后需要wx_appid
        if(StringUtils.isNoneEmpty(tradeCreateRequest.getWxAppId())) {
            parameters.put("wx_appid", tradeCreateRequest.getWxAppId());
        }
        if(!org.springframework.util.StringUtils.isEmpty(storeId) &&
                !org.springframework.util.StringUtils.isEmpty(storeName)){
            parameters.put("store_id", storeId);
            parameters.put("store_name", storeName);
        }
        if("JSAPI".equals(trade_type)){
            parameters.put("openid", openid);
        }

        String sign = WeChatUtil.createSign(parameters, merchatKey);
        StringBuffer requestStr = new StringBuffer("<xml>");
        requestStr.append("<appid><![CDATA[");
        requestStr.append(appId);
        requestStr.append("]]></appid>");
        requestStr.append("<attach><![CDATA[");
        requestStr.append(detail);
        requestStr.append("]]></attach>");
        requestStr.append("<body><![CDATA[");
        requestStr.append(body);
        requestStr.append("]]></body>");
        requestStr.append("<mch_id><![CDATA[");
        requestStr.append(mch_id);
        requestStr.append("]]></mch_id>");
        requestStr.append("<nonce_str><![CDATA[");
        requestStr.append(nonce_str);
        requestStr.append("]]></nonce_str>");
        requestStr.append("<notify_url><![CDATA[");
        requestStr.append(notify_url);
        requestStr.append("]]></notify_url>");

        if("JSAPI".equals(trade_type)){
            requestStr.append("<openid><![CDATA[");
            requestStr.append(openid);
            requestStr.append("]]></openid>");
        }

        if(StringUtils.isNoneEmpty(tradeCreateRequest.getWxAppId())) {
            //升级后需要wx_appid
            requestStr.append("<wx_appid><![CDATA[");
            requestStr.append(tradeCreateRequest.getWxAppId());
            requestStr.append("]]></wx_appid>");
        }
        if(!org.springframework.util.StringUtils.isEmpty(storeId) &&
                !org.springframework.util.StringUtils.isEmpty(storeName)){

            requestStr.append("<store_id><![CDATA[");
            requestStr.append(storeId);
            requestStr.append("]]></store_id>");

            requestStr.append("<store_name><![CDATA[");
            requestStr.append(storeName);
            requestStr.append("]]></store_name>");
        }

        //开始和到期时间
        requestStr.append("<time_start><![CDATA[");
        requestStr.append(startDate);
        requestStr.append("]]></time_start>");
        requestStr.append("<time_expire><![CDATA[");
        requestStr.append(expireDate);
        requestStr.append("]]></time_expire>");

        requestStr.append("<out_trade_no><![CDATA[");
        requestStr.append(outTradeNo);
        requestStr.append("]]></out_trade_no>");
        requestStr.append("<spbill_create_ip><![CDATA[");
        requestStr.append(spBillCreateIP);
        requestStr.append("]]></spbill_create_ip>");
        requestStr.append("<total_fee><![CDATA[");
        requestStr.append(totalFee);
        requestStr.append("]]></total_fee>");
        requestStr.append("<trade_type><![CDATA[");
        requestStr.append(trade_type);
        requestStr.append("]]></trade_type>");
        requestStr.append("<sign><![CDATA[");
        requestStr.append(sign);
        requestStr.append("]]></sign>");
        requestStr.append("</xml>");

        logger.info("wxRequest: " + requestStr.toString());
        String unifiedorder = "https://api.mch.weixin.qq.com/pay/unifiedorder";
        // TODO http
        HttpRequest request = HttpRequest.post(config.get("tradeUrl") ==null ?
                unifiedorder : config.get("tradeUrl")).
                contentType("application/xml","UTF-8").
                trustAllCerts().trustAllHosts().send(requestStr);
        if (request.ok()) {
            String respBody = request.body();
            logger.debug("微信支付下单返回:\n" + respBody);
            return respBody;
        } else {
            throw new Exception("支付失败");
        }
    }

    /**
     * @方法名称:sendWxPayRequest
     * @内容摘要: ＜发送统一下单请求＞
     * 烟草-中行-支付宝
     */
    private String sendAliPayRequest(Map<String, String> config, String body,
                                    String detail,String outTradeNo,
                                    long totalFee, String spBillCreateIP, String trade_type,
                                    String openid,String nonce_str,
                                    TradeCreateRequest tradeCreateRequest) throws Exception {
        String notify_url = config.get("notifyUrl");// WeixinConfig.notify_url;//回调
        String mch_id = config.get("mchId");// WeixinConfig.mch_id; //商户ID
        String merchatKey = config.get("merchatKey");// WeixinConfig.merchatKey;
        //设置订单过期时间
        Calendar nowTime = Calendar.getInstance();
        nowTime.add(Calendar.MINUTE,Integer.parseInt(config.get("expireTime")));
        String startDate=DateTimeUtil.getFormatDateStr(new Date(),"yyyyMMddHHmmss");
        String expireDate=DateTimeUtil.getFormatDateStr(nowTime.getTime(),"yyyyMMddHHmmss");

        SortedMap<Object, Object> parameters = new TreeMap<Object, Object>();
        parameters.put("service", "aliJsPay");
        parameters.put("mchNo", mch_id);
        parameters.put("attach", detail);
        parameters.put("body", body);
        parameters.put("detail", body);
        parameters.put("notifyUrl", notify_url);
        parameters.put("outTradeNo", outTradeNo);
        parameters.put("totalAmount", totalFee);
        parameters.put("tradeType", "alipay_js");
        parameters.put("startTime", startDate);
        parameters.put("expireTime", expireDate);
        parameters.put("version", "1.0");
        parameters.put("requestNo", UUID.randomUUID().toString());
        parameters.put("partnerId", merchatKey);
        String jsonSign = JSON.toJSONString(parameters);
        jsonSign = jsonSign + merchatKey;
        System.out.println(jsonSign);
        String sign = WeChatUtil.MD5Encode(jsonSign, "UTF-8");
        String unifiedorder = "https://openapi.sit.dcorepay.com";
        InputStream input = new ByteArrayInputStream(JSON.toJSONString(parameters).getBytes(StandardCharsets.UTF_8));
        // TODO http
        HttpRequest request = HttpRequest.post(config.get("tradeUrl") == null ?
                        unifiedorder : config.get("tradeUrl")).
                contentType("application/json", "UTF-8").
                header("Accept", "application/json").
                header("Content-Type", "application/json;charset=utf-8").
                header("x-api-accessKey", mch_id).
                header("x-api-signType", "MD5").header("x-api-sign", sign).
                trustAllCerts().trustAllHosts().send(input);
        if (request.ok()) {
            String respBody = request.body();
            logger.debug("中行支付宝下单返回:\n" + respBody);
            return respBody;
        } else {
            throw new Exception("中行支付宝支付失败");
        }
    }


    /**
     * 工行支付宝支付
     * @param resp
     * @param request
     * @param order
     * @param config
     */
    public void createResponseCBCH5Alipay(TradeCreateResponse resp,
                                    TradeCreateRequest request, CollectionDealerTradeEntity order,
                                    Map<String, String> config) {
        String transferKey;
        String decode;
        if ("ICBC_H5_GONGAN".equals(request.getChannel())) {
            try {
                String ciphertext = request.getCiphertext();
                transferKey = request.getTransferKey();
                decode = DESCoderUtil.decode(transferKey, ciphertext);
                request = JSON.parseObject(decode, TradeCreateRequest.class);
                order.setTradeNo(request.getTradeNo());
                CollectionDealerTradeEntity oriOrder = this.collectionDealerTradeDao.findByTradeNo(request.getTradeNo());
                if (null != oriOrder) {
                    Map<String, Object> parameters = new HashMap();
                    if (oriOrder.getStatus() == 1) {
                        parameters.put("err-msg", "订单已经支付成功,无须再次支付！");
                    } else {
                        parameters.put("tradeNo", order.getTradeNo());
                    }

                    resp.setData(parameters);
                    return;
                }
            } catch (Exception var26) {
                logger.info("公安参数解密失败", var26);
                return;
            }
        } else {
            order.setTradeNo(this.tradeNoGenerater.getNewTradeNo(TradeTypeCode.COLLECTION_ICBC));
        }

        Long price = Long.valueOf(request.getAmount());
        order.setAmount(price);
        order.setBody(request.getBody());
        order.setSubject(request.getBody());
        order.setDealerId(Integer.valueOf(request.getDealerId()));
        order.setOpenId(request.getUserId());
        order.setChannel("微信");
        order.setChannelCode(request.getChannel());
        order.setCreateTime(new Date());
        order.setStatus(0);
        order.setStatusDes("待支付");
        order.setUserId(request.getUserId());
        order.setBody(request.getBody());
        order.setNotifyUrl(request.getNotifyUrl());
        order.setVersion(0L);
        this.collectionDealerTradeDao.create(order);
        transferKey = config.get("mer_id");
        decode = config.get("app_id");
        String encryptKey = config.get("encrypt_key");
        String rsaPrivateKey = config.get("rsa_private_key");
        String tradeUrl = config.get("tradeUrl");
        String notifyUrl = config.get("notifyUrl");
        String returnUrl = config.get("returnUrl");
        String protocolNo = config.get("protocol_number");
        String expireTime = config.get("expire_time");
        UiIcbcClient client = new UiIcbcClient(decode, "RSA2", rsaPrivateKey, "UTF-8", "AES", encryptKey);
        CardbussinessZfbH5UiH5ConsumptionRequestV1 ICBCRequest = new CardbussinessZfbH5UiH5ConsumptionRequestV1();
        ICBCRequest.setServiceUrl(tradeUrl);
        CardbussinessZfbH5UiH5ConsumptionRequestV1.CardbussinessZfbH5UiH5ConsumptionRequestV1Biz bizContent =
                new CardbussinessZfbH5UiH5ConsumptionRequestV1.CardbussinessZfbH5UiH5ConsumptionRequestV1Biz();
        bizContent.setMer_id(transferKey);
        bizContent.setIcbc_appid(decode);
        bizContent.setMer_prtcl_no(protocolNo);
        bizContent.setExpire_time(expireTime);
        bizContent.setBody(request.getBody());
        bizContent.setAmount(price.toString());
        bizContent.setOrder_id(order.getTradeNo());
        bizContent.setOrder_date_time(DateTimeUtil.formatICBCH5(new Date()));
        bizContent.setNotify_url(notifyUrl);
        bizContent.setNotify_type("Hs");
        bizContent.setResult_type("0");
        bizContent.setCur_type("001");
        bizContent.setReturn_url(returnUrl);
        ICBCRequest.setBizContent(bizContent);
        String form = "";
        Jedis jedis = null;

        try {
            jedis = this.redisPool.getResource();
            form = client.buildPostForm(ICBCRequest);
            logger.info("ICBC respBody=\n{}", form);
            jedis.setex("collectionpay:form:" + order.getTradeNo(), 1200, form);
            Map<String, Object> parameters = new HashMap();
            parameters.put("tradeNo", order.getTradeNo());
            resp.setData(parameters);
        } catch (Exception var25) {
            System.out.println(var25);
            logger.error("执行createResponseCBC---发生异常-{}", var25.getMessage());
        } finally {
            if (jedis != null) {
                this.redisPool.returnResource(jedis);
            }
        }

    }

    /**
     * 工行H5支付
     * @param resp
     * @param request
     * @param order
     * @param config
     */
    public void createResponseCBCH5(TradeCreateResponse resp,
                                    TradeCreateRequest request,
                                    CollectionDealerTradeEntity order,
                                    Map<String, String> config) {

        Long price = Long.valueOf(request.getAmount());
        order.setTradeNo(tradeNoGenerater.getNewTradeNo(TradeTypeCode.COLLECTION_H5PAY_ICBC));
        order.setAmount(price);
        order.setBody(request.getBody());
        order.setSubject(request.getBody());
        order.setDealerId(Integer.valueOf(request.getDealerId()));
        order.setOpenId(request.getUserId());
        order.setChannel("工行h5支付");
        order.setChannelCode(request.getChannel());
        order.setCreateTime(new Date());
        order.setStatus(TradeStatus.WAIT_PAY);
        order.setStatusDes("待支付");
        order.setUserId(request.getUserId());
        order.setBody(request.getBody());
        order.setNotifyUrl(request.getNotifyUrl());
        order.setVersion(0L);

        this.collectionDealerTradeDao.create(order);

        String merId = config.get("mer_id");
        String appId = config.get("app_id");
        String rsaPrivateKey = config.get("rsa_private_key");
        //网关公钥
        String gateWayPublicKey = config.get("APIGW_PUBLIC_KEY");
        String tradeUrl = config.get("tradeUrl");
        String notifyUrl = config.get("notifyUrl");
        String protocolNo=config.get("protocol_number");
        DefaultIcbcClient client = new DefaultIcbcClient(appId, IcbcConstants.SIGN_TYPE_RSA2, rsaPrivateKey, gateWayPublicKey);
        CardbusinessAggregatepayB2cOnlineConsumepurchaseRequestV1 ICBCRequest = new CardbusinessAggregatepayB2cOnlineConsumepurchaseRequestV1();
        ICBCRequest.setServiceUrl(tradeUrl);
        CardbusinessAggregatepayB2cOnlineConsumepurchaseRequestV1.CardbusinessAggregatepayB2cOnlineConsumepurchaseRequestV1Biz bizContent = new CardbusinessAggregatepayB2cOnlineConsumepurchaseRequestV1.CardbusinessAggregatepayB2cOnlineConsumepurchaseRequestV1Biz();
        bizContent.setMer_id(merId);
        bizContent.setOut_trade_no(order.getTradeNo());
        bizContent.setPay_mode("9");
        bizContent.setAccess_type("7");
        bizContent.setMer_prtcl_no(protocolNo);
        bizContent.setMer_prtcl_no(protocolNo);
        bizContent.setOrig_date_time(DateTimeUtil.formatICBCH5(new Date()));
        bizContent.setReturn_url(config.get("returnUrl"));
        bizContent.setDecive_info("013467007045764");
        bizContent.setBody(order.getBody());
        bizContent.setFee_type("001");
        bizContent.setSpbill_create_ip( config.get("spbill_create_ip"));
        bizContent.setTotal_fee(""+order.getAmount());
        bizContent.setMer_url(notifyUrl);
        bizContent.setIcbc_appid(appId);
        //微信公众号的appid
        bizContent.setShop_appid( config.get("wx_app_id"));
        //在微信支付要传openId
        bizContent.setOpen_id(request.getOpenId());
        bizContent.setNotify_type("HS");
        //银行只向商户发送交易成功的通知信息
        bizContent.setResult_type("1");
        //设置内容
        ICBCRequest.setBizContent(bizContent);
        try {
            CardbusinessAggregatepayB2cOnlineConsumepurchaseResponseV1 response
                    = client.execute(ICBCRequest, System.currentTimeMillis() + "");
            if (response.getReturnCode() == 0) {
                Map<String, Object> parameters = new HashMap<>();
                parameters.put("tradeNo", order.getTradeNo());
               //解析微信参数
                String wx_data_package = response.getWx_data_package();
                Map map = JSON.parseObject(wx_data_package, Map.class);
                parameters.put("timeStamp", map.get("timestamp"));
                parameters.put("package", map.get("package"));
                parameters.put("paySign", map.get("sign"));
                parameters.put("appId", map.get("appid"));
                parameters.put("signType", map.get("signType"));
                parameters.put("nonceStr", map.get("noncestr"));
                resp.setData(parameters);
            } else {
                throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "下单失败!");
            }
        } catch (Exception exception){
            logger.error("执行createResponseCBCH5---发生异常",exception.getMessage());
        }

    }

    /**
     * 中行數字人民幣支付
     * @param resp
     * @param request
     * @param order
     * @param config
     */
    public void createResponseBOCDIGITAL(TradeCreateResponse resp, TradeCreateRequest request,
                                         CollectionDealerTradeEntity order, Map<String, String> config) {
        String merchantNo = config.get("merchantNo");
        //格式要求为：商户号（merchantNo）后6位+商户按自己规则生成的订单号 长度为30
        String tradeNo =merchantNo.substring(merchantNo.length()-6) +
                tradeNoGenerater.getNewTradeNo(TradeTypeCode.COLLECTION_DIGITAL,4);
        //設置金額
        String orderAmount = new BigDecimal(request.getAmount()).divide(new BigDecimal(100))
                .setScale(2, RoundingMode.HALF_UP).toString();
        Jedis jedis = null;
        try {
            // 处理请求原文XML拼接 此方式按照JAVA自带方法转XML 如果用DOM4J 等请自行引包开发
            DocumentBuilderFactory documentbuilderfactory = DocumentBuilderFactory
                    .newInstance();
            DocumentBuilder documentbuilder = documentbuilderfactory.newDocumentBuilder();
            Document document = documentbuilder.newDocument();
            Element element = document.createElement("response");
            document.setXmlStandalone(true);
            //xml构建header
            Element elementHead = document.createElement("head");
            Element elementRequestTime = document.createElement("requestTime");
            elementHead.appendChild(elementRequestTime);
            //xml构建body
            Element elementBody = document.createElement("body");
            Element elementMerchantNo = document.createElement("merchantNo");
            Element elementDeviceInfo = document.createElement("deviceInfo");
            Element elementNonceStr = document.createElement("nonceStr");
            Element elementProductName = document.createElement("productName");
            Element elementDetail = document.createElement("detail");
            Element elementMerOrderNo = document.createElement("merOrderNo");
            Element elementTotalAmount = document.createElement("totalAmount");
            Element elementCurrency = document.createElement("currency");
            Element elementTerminalIP = document.createElement("terminalIP");
            Element elementTimeStart = document.createElement("timeStart");
            Element elementValidity = document.createElement("validity");
            Element elementOrderChannel = document.createElement("orderChannel");
            Element elementTranCode = document.createElement("tranCode");
            Element elementTradeTypeCode = document.createElement("tradeTypeCode");
            Element elementTradePlace = document.createElement("tradePlace");
            elementBody.appendChild(elementMerchantNo);
            elementBody.appendChild(elementDeviceInfo);
            elementBody.appendChild(elementNonceStr);
            elementBody.appendChild(elementProductName);
            elementBody.appendChild(elementDetail);
            elementBody.appendChild(elementMerOrderNo);
            elementBody.appendChild(elementTotalAmount);
            elementBody.appendChild(elementCurrency);
            elementBody.appendChild(elementTerminalIP);
            elementBody.appendChild(elementTimeStart);
            elementBody.appendChild(elementValidity);
            elementBody.appendChild(elementOrderChannel);
            elementBody.appendChild(elementTranCode);
            elementBody.appendChild(elementTradeTypeCode);
            elementBody.appendChild(elementTradePlace);
            //构xml
            document.appendChild(element);
            element.appendChild(elementHead);
            element.appendChild(elementBody);
            //xml 设置值
            elementRequestTime.setTextContent( DateUtil.format(new Date(), "yyyyMMddHHmmss"));
            elementMerchantNo.setTextContent(config.get("merchantNo"));
            elementDeviceInfo.setTextContent(request.getUserId()+"交纳党费！");
            elementNonceStr.setTextContent(tradeNoGenerater.getNewTradeNo(TradeTypeCode.COLLECTION_DIGITAL));
            elementProductName.setTextContent(request.getSubject());
            elementDetail.setTextContent(request.getBody());
            elementMerOrderNo.setTextContent(tradeNo);
            elementTotalAmount.setTextContent(orderAmount);
            elementCurrency.setTextContent("001");
            elementTerminalIP.setTextContent(config.get("terminal_ip"));
            elementTimeStart.setTextContent(DateTimeUtil.getFormatDateStr(new Date(),"yyyyMMddHHmmss"));
            elementValidity.setTextContent(config.get("validityTime"));
            elementOrderChannel.setTextContent("0008");
            elementTradeTypeCode.setTextContent("TT04");
            elementTranCode.setTextContent("00500");
            elementTradePlace.setTextContent(config.get("tradePlace"));
            // 将拼接好的转 String报文
            String requestPlainText = createPrettyFormat(document, "UTF-8").trim();
            logger.info("---------- EPTPayOrder send message before base64 encoded ----------");
            logger.info("[merchantNo]=[" + merchantNo + "]");
            logger.info("[message]=[" + requestPlainText + "]");
            //这里是测试证书加签,根据生产上的环境切换成产的证书
            String pfxPath = config.get("sign_file_name_url");
            PKCS7Tool signer = PKCS7Tool.getSigner(pfxPath, config.get("sign_file_password"),config.get("sign_file_password"));
            //调用加签方法
            String signData = signer.sign(requestPlainText.getBytes(StandardCharsets.UTF_8));
            //3、商户拼装上送报文
            //将请求原文进行Base64转码 加签数据不用转码 方法里面已经转了
            BASE64Encoder encoder = new BASE64Encoder();
            String base64MerchantNo = encoder.encode(merchantNo.getBytes(StandardCharsets.UTF_8));
            String base64Version = encoder.encode("1.0.1".getBytes(StandardCharsets.UTF_8));
            String base64MessageId = encoder.encode("219701".getBytes(StandardCharsets.UTF_8));
            String base64Security = encoder.encode("P7".getBytes(StandardCharsets.UTF_8));
            String base64Message = encoder.encode(requestPlainText.getBytes(StandardCharsets.UTF_8));
            //发送支付请求到银行
            HttpClient httpClient = new DefaultHttpClient();
            List<NameValuePair> formParams = new ArrayList<NameValuePair>();
            formParams.add(new BasicNameValuePair("merchantNo",base64MerchantNo));
            formParams.add(new BasicNameValuePair("version", base64Version));
            formParams.add(new BasicNameValuePair("messageId", base64MessageId));
            formParams.add(new BasicNameValuePair("security", base64Security));
            formParams.add(new BasicNameValuePair("message", base64Message));
            formParams.add(new BasicNameValuePair("signature", signData));
            //4、商户向银行方发请求
            HttpEntity entity = new UrlEncodedFormEntity(formParams, "UTF-8");
            String tradeUrl = config.get("tradeUrl");
            HttpPost post = new HttpPost(tradeUrl);
            post.setEntity(entity);
            HttpResponse postRes = httpClient.execute(post);
            HttpEntity entityResult = postRes.getEntity();
            String rtnResult = EntityUtils.toString(entityResult);
            logger.info("---------- EPTPayOrder receive message ----------");
            logger.info("[rtnResult]=[" + rtnResult + "]");
            // 返回数据 message参数与signature参数以","分隔
            int i = rtnResult.indexOf(',');
            if (i <= 0) {
                throw new Exception("return error!");
            }
            String rtnMessage = rtnResult.substring(0, i);
            String rtnSignture = rtnResult.substring(i + 1);
            //5、商户验证银行返回数据签名
            //Base64解码 验签数据不用解码只解码原文
            BASE64Decoder decoder = new BASE64Decoder();
            String result = new String(decoder.decodeBuffer(rtnMessage), StandardCharsets.UTF_8);
            logger.info("[decoder result]=[" + result + "]");
            // 验签 优先使用boccfcaTest 不行再用boccaTest 生产上要替换为生产的验签公钥
            String pfxPathVer = config.get("verify_file_name_url");
            PKCS7Tool tool = PKCS7Tool.getVerifier(pfxPathVer);
            //调用方法验签
            tool.verify(rtnSignture, result.getBytes(StandardCharsets.UTF_8), null);
            // 不抛出异常表示验签成功
            logger.info("[VERIFY OK]");
            //解析返回结果
            Map<String, Object> mapResult = XmlUtil.xmlToMap(result);
            Map<String,Object> mapHeader= (Map<String, Object>) mapResult.get("head");
            Map<String,Object> mapBody= (Map<String, Object>) mapResult.get("body");
            if ("OK".equals(mapHeader.get("responseCode"))) {
                logger.info("执行createResponseBOCDIGITAL成功！");
                Long price = Long.valueOf(request.getAmount());
                order.setAmount(price);
                order.setBody(request.getBody());
                order.setSubject(request.getBody());
                order.setDealerId(Integer.valueOf(request.getDealerId()));
                order.setOpenId(request.getUserId());
                order.setChannel("中行数字人民币");
                order.setChannelCode(request.getChannel());
                order.setCreateTime(new Date());
                order.setStatus(TradeStatus.WAIT_PAY);
                order.setStatusDes("待支付");
                order.setUserId(request.getUserId());
                order.setBody(request.getBody());
                order.setNotifyUrl(request.getNotifyUrl());
                order.setVersion(0L);
                order.setTradeNo(tradeNo);
                order.setOutTradeNo(mapBody.get("orderNo").toString());
                this.collectionDealerTradeDao.create(order);
                //跳转地址
                String jumpUrl =String.format(config.get("jump_url"),mapBody.get("orderNo").toString(),config.get("notifyUrl")) ;
                logger.info("jumpUrl跳转地址是"+jumpUrl);
                jedis = redisPool.getResource();
                jedis.setex(RedisKey.COMPANY_RECHARGE_ALIPAY_FORM + order.getTradeNo(),
                        1200, jumpUrl);
                Map<String, Object> parameters = new HashMap<>();
                parameters.put("tradeNo", tradeNo);
                resp.setData(parameters);
            }else {
                throw new AssetServiceException(ErrorCode.MISSING_PARAMETER, mapHeader.get("responseInfo").toString());
            }
        }catch (Exception e){
            logger.error("执行createResponseBOCDIGITAL---发生异常",e);
            throw new AssetServiceException(ErrorCode.MISSING_PARAMETER, e.getMessage());
        }finally {
            if (jedis != null) {
                redisPool.returnResource(jedis);
            }
        }

    }

    public static String createPrettyFormat(Document document, String s)
            throws Exception {
        DOMSource domsource = new DOMSource(document);
        TransformerFactory transformerfactory = TransformerFactory
                .newInstance();
        Transformer transformer = transformerfactory.newTransformer();
        transformer.setOutputProperty("indent", "yes");
        transformer.setOutputProperty("encoding", s);
        StringWriter stringwriter = new StringWriter();
        transformer.transform(domsource, new StreamResult(stringwriter));
        return stringwriter.toString();
    }



}
