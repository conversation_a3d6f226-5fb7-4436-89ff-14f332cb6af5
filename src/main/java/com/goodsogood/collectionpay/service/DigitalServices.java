package com.goodsogood.collectionpay.service;

import com.bocnet.common.security.PKCS7Tool;
import com.goodsogood.collectionpay.consts.LogConfiger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.PrintWriter;
import java.io.StringReader;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Service
public class DigitalServices {

    private static final Logger logger = LoggerFactory.getLogger(LogConfiger.APP);

    @Autowired
    private AssetDepositChannelConfService assetDepositChannelConfService ;

    /**
     * 中行数字人民币 银行发送查询商户侧账户信息请求
     * @param request
     * @param response
     */
    public void queryMerAccountInfo(String channelCode,
                                    HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map<String, String> config = assetDepositChannelConfService.findOptionsByChannel(channelCode);
        //1、商户接收银行发送数据
        String base64MerchantNo = request.getParameter("merchantNo");// 商户中行商户号
        String base64Version = request.getParameter("version");// 商户中行商户号
        String base64MessageId = request.getParameter("messageId");// 交易码
        String base64Security = request.getParameter("security");// 签名方法
        String base64Message = request.getParameter("message");// 请求报文明文信息:
        // 将业务数据组成XML字符串格式进行UTF-8格式的转码
        String signature = request.getParameter("signature");// 请求报文签名信息：对报文原文XML字符串格式的字节数组（UTF-8格式）进行签名
        logger.info("[base64Message]=[" + base64Message + "]");
        logger.info("[signature]=[" + signature + "]");
        BASE64Decoder decoder = new BASE64Decoder();
        String message = new String(decoder.decodeBuffer(base64Message), StandardCharsets.UTF_8);
        String merchantNo = new String(decoder.decodeBuffer(base64MerchantNo), StandardCharsets.UTF_8);
        String messageId = new String(decoder.decodeBuffer(base64MessageId), StandardCharsets.UTF_8);
        String security = new String(decoder.decodeBuffer(base64Security), StandardCharsets.UTF_8);
        String version = new String(decoder.decodeBuffer(base64Version), StandardCharsets.UTF_8);
        // 打印解码以后的数据
        logger.info("[message]=[" + message + "]");
        logger.info("[merchantNo]=[" + merchantNo + "]");
        logger.info("[messageId]=[" + messageId + "]");
        logger.info("[security]=[" + security + "]");
        logger.info("[version]=[" + version + "]");
        logger.info("[signature]=[" + signature + "]");
        //2、商户验证银行返回数据签名
        try {
            // 验签 优先使用boccfcaTest 不行再用boccaTest 生产上要替换为生产的验签公钥
            String pfxPath = Objects.requireNonNull(DigitalServices.class.getClassLoader()
                    .getResource(config.get("verify_file_name_url"))).getPath();
            PKCS7Tool tool = PKCS7Tool.getVerifier(pfxPath);
            tool.verify(signature, message.getBytes(StandardCharsets.UTF_8), null);
            // 不抛出异常表示验签成功
            logger.info("[VERIFY OK]");
        } catch (Exception e) {
            logger.info(e.getMessage());
        }
        //3、业务字段解析
        Map<String, Object> resMap = new HashMap<>();
        try {
            // 获取XML 报文的内容转Map
            Map<String, Object> stringObjectMap = parseResultMap(message, resMap);
            //ToDo 验证调用数据是不是正确
            // 处理请求原文XML拼接 此方式按照JAVA自带方法转XML 如果用DOM4J 等请自行引包开发
            DocumentBuilderFactory documentbuilderfactory = DocumentBuilderFactory
                    .newInstance();
            DocumentBuilder documentbuilder=documentbuilderfactory.newDocumentBuilder();
            Document document = documentbuilder.newDocument();
            Element element = document.createElement("response");
            document.setXmlStandalone(true);
            //xml构建header
            Element elementHead = document.createElement("head");
            Element elementResponseCode = document.createElement("responseCode");
            Element elementResponseInfo = document.createElement("responseInfo");
            elementHead.appendChild(elementResponseCode);
            elementHead.appendChild(elementResponseInfo);
            //xml构建body
            Element elementBody = document.createElement("body");
            Element elementQueryType = document.createElement("queryType");
            Element elementAccountExistFlag = document.createElement("accountExistFlag");
            elementBody.appendChild(elementQueryType);
            elementBody.appendChild(elementAccountExistFlag);
            //构xml
            document.appendChild(element);
            element.appendChild(elementHead);
            element.appendChild(elementBody);
            //xml 设置值
            elementResponseCode.setTextContent("********");
            elementResponseInfo.setTextContent("成功返回！");
            elementQueryType.setTextContent("QT00");
            elementAccountExistFlag.setTextContent("EF01");
            // 将拼接好的转 String报文
            String requestPlainText = createPrettyFormat(document, "UTF-8").trim();
            logger.info("[requestPlainText]=[" + requestPlainText + "]");
            String pfxPathSigner = config.get("sign_file_name_url");
            //这里是测试证书加签,根据生产上的环境切换成产的证书
            PKCS7Tool signer = PKCS7Tool.getSigner(pfxPathSigner,
                    config.get("sign_file_password"),  config.get("sign_file_password"));
            //调用加签方法
            String signData = signer.sign(requestPlainText.getBytes(StandardCharsets.UTF_8));
            //将请求原文进行Base64转码 加签数据不用转码 方法里面已经转了
            BASE64Encoder encoder = new BASE64Encoder();
            String requestMessage = encoder.encode(requestPlainText.getBytes(StandardCharsets.UTF_8));
            // 返回数据 message参数与signature参数以","分隔
            String resMsg = requestMessage + "," + signData;
            response.setHeader("Content-type", "application/x-www-form-urlencoded;chatset=UTF-8");
            response.setCharacterEncoding("UTF-8");
            PrintWriter writer = response.getWriter();
            writer.write(resMsg);
            writer.flush();
            writer.close();
        } catch (Exception  ex) {
            logger.error("queryMerAccountInfo操作发生异常",ex);
        }
    }

    /**
     * 银行发送Token推送请求
     * @param request
     * @param response
     */
    public void pushToken(String channelCode,HttpServletRequest request, HttpServletResponse response) throws Exception{
        Map<String, String> config = assetDepositChannelConfService.findOptionsByChannel(channelCode);
        //1、商户接收银行发送数据
        String base64MerchantNo = request.getParameter("merchantNo");// 商户中行商户号
        String base64Version = request.getParameter("version");// 商户中行商户号
        String base64MessageId = request.getParameter("messageId");// 交易码
        String base64Security = request.getParameter("security");// 签名方法
        String base64Message = request.getParameter("message");// 请求报文明文信息:
        // 将业务数据组成XML字符串格式进行UTF-8格式的转码
        String signature = request.getParameter("signature");// 请求报文签名信息：对报文原文XML字符串格式的字节数组（UTF-8格式）进行签名
        logger.info("[base64Message]=[" + base64Message + "]");
        logger.info("[signature]=[" + signature + "]");
        BASE64Decoder decoder = new BASE64Decoder();
        String message = new String(decoder.decodeBuffer(base64Message), StandardCharsets.UTF_8);
        String merchantNo = new String(decoder.decodeBuffer(base64MerchantNo), StandardCharsets.UTF_8);
        String messageId = new String(decoder.decodeBuffer(base64MessageId), StandardCharsets.UTF_8);
        String security = new String(decoder.decodeBuffer(base64Security), StandardCharsets.UTF_8);
        String version = new String(decoder.decodeBuffer(base64Version), StandardCharsets.UTF_8);
        // 打印解码以后的数据
        logger.info("[message]=[" + message + "]");
        logger.info("[merchantNo]=[" + merchantNo + "]");
        logger.info("[messageId]=[" + messageId + "]");
        logger.info("[security]=[" + security + "]");
        logger.info("[version]=[" + version + "]");
        logger.info("[signature]=[" + signature + "]");
        //2、商户验证银行返回数据签名
        try {
            // 验签 优先使用boccfcaTest 不行再用boccaTest 生产上要替换为生产的验签公钥
            String pfxPath = config.get("verify_file_name_url");
            PKCS7Tool tool = PKCS7Tool.getVerifier(pfxPath);
            tool.verify(signature, message.getBytes(StandardCharsets.UTF_8), null);
            // 不抛出异常表示验签成功
            logger.info("[VERIFY OK]");
        } catch (Exception e) {
            logger.info(e.getMessage());
        }

    }

    //获取XML 报文的内容转Map
    private Map<String, Object> parseResultMap(String resMessage, Map<String, Object> resMap) throws Exception {
        DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
        DocumentBuilder dbBuilder = dbFactory.newDocumentBuilder();
        Document doc = dbBuilder.parse(new InputSource(new StringReader(resMessage)));
        resMap.put("merchantNo", getValue(doc.getElementsByTagName("merchantNo")));
        resMap.put("queryType", getValue(doc.getElementsByTagName("queryType")));
        resMap.put("merAccountId", getValue(doc.getElementsByTagName("merAccountId")));
        resMap.put("verifyCode", getValue(doc.getElementsByTagName("verifyCode")));
        return resMap;
    }

    //转String类型 然后加签
    public static String createPrettyFormat(Document document, String s) throws Exception {
        DOMSource domsource = new DOMSource(document);
        TransformerFactory transformerfactory = TransformerFactory
                .newInstance();
        Transformer transformer = transformerfactory.newTransformer();
        transformer.setOutputProperty("indent", "yes");
        transformer.setOutputProperty("encoding", s);
        StringWriter stringwriter = new StringWriter();
        transformer.transform(domsource, new StreamResult(stringwriter));
        return stringwriter.toString();
    }

    private String getValue(NodeList elementsByTagName2) {
        String textContent ="";
        Node node = elementsByTagName2.item(0);
        String nodeName = node.getNodeName();
        textContent = node.getTextContent();
        logger.info(nodeName+":"+textContent);
        return textContent;
    }


}
