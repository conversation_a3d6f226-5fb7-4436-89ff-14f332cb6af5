package com.goodsogood.collectionpay.service;

import com.alibaba.fastjson.JSON;
import com.goodsogood.collectionpay.consts.LogConfiger;
import com.goodsogood.collectionpay.entity.CollectionDealerTradeEntity;
import com.goodsogood.collectionpay.util.DateTimeUtil;
import com.goodsogood.collectionpay.util.HttpUtils;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;

@Service("notifyService")
public class NotifyService {
	
	private static final Logger logger = LoggerFactory.getLogger(LogConfiger.APP);
	
	private static final ThreadFactory notifyThreadFactory = new ThreadFactoryBuilder()
			.setNameFormat("Notify threadPool-%d").setDaemon(true).build();

	private static final ExecutorService notifyThreadPool = Executors
			.newFixedThreadPool(Runtime.getRuntime().availableProcessors() * 20, notifyThreadFactory);
	
	
	
	/***
	 * 提交一个异步 通知 
	 * @param notifyUrl
	 * @param params
	 */
	public void submit(String notifyUrl,Map<String, String> params){
		notifyThreadPool.submit(new TaskProcessor(notifyUrl, params));
	}


	/***
	 * 提交一个异步 通知
	 */
	public void notifyPPMD(CollectionDealerTradeEntity order){
		//处理回调
		Map<String, String> notifyParams = new HashMap<>();
		notifyParams.put("tradeNo", order.getTradeNo());
		notifyParams.put("createTime", DateTimeUtil.format(order.getCreateTime()));
		notifyParams.put("payTime", DateTimeUtil.format(order.getPayTime()));
		notifyParams.put("status", order.getStatus() + "");
		notifyParams.put("statusDes", order.getStatusDes());
		notifyParams.put("amount", order.getAmount().toString());
		notifyParams.put("openid", order.getOpenId());
		notifyParams.put("channel", order.getChannelCode());
		//设置平台信息 3是渝快办 4.渝快政
		notifyParams.put("platform", order.getChannel());
		notifyThreadPool.submit(new TaskProcessor(order.getNotifyUrl(), notifyParams));
	}
	
	
	private class TaskProcessor implements Runnable {
		private String notifyUrl;
		private Map<String, String> params;

		public TaskProcessor(String notifyUrl, Map<String, String> params) {
			this.notifyUrl = notifyUrl;
			this.params = params;
		}

		/*
		 * (non-Javadoc)
		 * 
		 * @see java.lang.Thread#run()
		 */
		@Override
		public void run() {
			boolean s = false;
			for (int i = 0; i < 3 && !s; i++) {
				String result =  HttpUtils.doPost(notifyUrl, params);
                logger.info("异步线程回调业务系统订单号="+params.get("tradeNo")+"     回调返回接口={}",result);
                String code = JSON.parseObject(result).get("code").toString();
                if("0".equals(code)){
                    s = true;
                }
				if (s) {
					logger.info("TaskProcessor [{}] respBody:{},params:{}", i, result,params);
				} else {
					logger.info("TaskProcessor [{}] post err!,params:{}", i,params);
					Double pow = Math.pow(2, (i + 1));
					try {
						Thread.sleep((1000 * pow.intValue()));
					} catch (InterruptedException e) {
						e.printStackTrace();
					}
				}
			}

		}

	}


}
