package com.goodsogood.collectionpay.service;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.goodsogood.collectionpay.consts.ErrorCode;
import com.goodsogood.collectionpay.consts.TradeStatus;
import com.goodsogood.collectionpay.consts.TradeTypeCode;
import com.goodsogood.collectionpay.dao.CollectionDealerTradeDao;
import com.goodsogood.collectionpay.entity.CollectionDealerTradeEntity;
import com.goodsogood.collectionpay.exception.AssetServiceException;
import com.goodsogood.collectionpay.model.SxPayInfoVo;
import com.goodsogood.collectionpay.model.request.TradeCreateRequest;
import com.goodsogood.collectionpay.model.response.TradeCreateResponse;
import com.goodsogood.collectionpay.util.DateTimeUtil;
import com.goodsogood.collectionpay.util.NumUtil;
import com.goodsogood.collectionpay.util.sm2.SM2NewUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.security.Security;
import java.text.DecimalFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 三峡付 服务
 */
@Service("SanXianService")
public class SanXianService {
    private static final Logger logger = LoggerFactory.getLogger(SanXianService.class);
    @Autowired
    private TradeNoGenerater tradeNoGenerater;
    @Autowired
    private CollectionDealerTradeDao collectionDealerTradeDao;

    @Autowired
    private AssetDepositChannelConfService assetDepositChannelConfService;

    @Autowired
    private NotifyService notifyService;

    @Transactional
    public TradeCreateResponse buildOrder(TradeCreateResponse resp, TradeCreateRequest request,
                                          CollectionDealerTradeEntity order,
                                          Map<String, String> config) {
        //生成三峡付单号
        String orderNo = tradeNoGenerater.getNewTradeNo(TradeTypeCode.COLLECTION_SANXIAN);
        int price = Integer.valueOf(request.getAmount());
        order.setAmount((long) price);
        order.setBody(request.getBody());
        order.setSubject(request.getBody());
        order.setDealerId(Integer.valueOf(request.getDealerId()));
        order.setOpenId(request.getOpenId());
        order.setChannel("三峡付支付");
        order.setChannelCode(request.getChannel());
        order.setCreateTime(new Date());
        order.setStatus(TradeStatus.WAIT_PAY);
        order.setStatusDes("待支付");
        order.setTradeNo(orderNo);
        Long orgId = request.getOrgId();
        order.setUserId("" + request.getUserId());
        order.setOpenId(request.getUserId());
        order.setOrgId(orgId);
        order.setBody(request.getBody());
        order.setNotifyUrl(request.getNotifyUrl());
        order.setVersion(0L);
        this.collectionDealerTradeDao.create(order);
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("tradeNo", order.getTradeNo());
        resp.setData(parameters);
        return resp;
    }

    /**
     * 三峡付回调
     *
     * @param params
     * @param response
     */
    @Transactional
    public void sxPayPayNotify(Map<String, String> params, HttpServletResponse response) throws IOException {
        Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
        String encryptInfo = params.get("encrypt_info");
        if (StringUtils.isEmpty(encryptInfo)) {
            throw new AssetServiceException(ErrorCode.MISSING_PARAMETER, "encryptInfo不能不空");
        }
        logger.info("sxPayPayNotify回调信息,encryptInfo={}", encryptInfo);
        Map<String, String> config = this.assetDepositChannelConfService.findOptionsByChannel("SAN_XIAN_PAY_1");
        String decrypt = SM2NewUtil.decrypt(config.get("privateKey"), encryptInfo);
        logger.info("sxPayPayNotify回调信息,解密过后的decrypt={}", decrypt);
        SxPayInfoVo sxPayInfoVo = JSON.parseObject(decrypt, SxPayInfoVo.class);
        if (!Objects.isNull(sxPayInfoVo)) {
            if (sxPayInfoVo.getStatus() == 1) {
                CollectionDealerTradeEntity order = this.collectionDealerTradeDao.findByTradeNo(sxPayInfoVo.getTradeNo());
                if (order == null) {
                    logger.info("sxPayPayNotify根据订单号tradeNo={},无法在数据查询到信息", sxPayInfoVo.getTradeNo());
                    return;
                }
                String payAmount = NumUtil.amountFormat(order.getAmount().intValue(), 100);
                //实际付款金额
                if (!Objects.equals(payAmount, sxPayInfoVo.getPayMoney())) {
                    logger.info("sxPayPayNotify根据订单号tradeNo={},在数据查询支付金额不一致,order={}",
                            sxPayInfoVo.getTradeNo(), order);
                    return;
                }
                //更新支付状态
                order.setPayTime(new Date());
                order.setStatus(TradeStatus.SUCCESS);
                order.setStatusDes("已支付");
                collectionDealerTradeDao.update(order);
                logger.info("sxPayPayNotify更新订单状态成功,!tradeNo={}", order.getTradeNo());
                //回调通知
                Map<String, String> notifyParams = new HashMap<>();
                notifyParams.put("tradeNo", order.getTradeNo());
                notifyParams.put("outTranNumber", order.getOutTradeNo());
                notifyParams.put("createTime", DateTimeUtil.format(order.getCreateTime()));
                notifyParams.put("payTime", DateTimeUtil.format(order.getPayTime()));
                notifyParams.put("status", order.getStatus() + "");
                notifyParams.put("statusDes", order.getStatusDes() + "");
                notifyParams.put("amount", order.getAmount().toString());
                notifyParams.put("openid", order.getOpenId());
                notifyParams.put("channel", order.getChannel());
                this.notifyService.submit(order.getNotifyUrl(), notifyParams);
                logger.info("sxPayPayNotify回调处理完成!tradeNo={}", order.getTradeNo());
                String timestamp = DateUtil.format(new Date(), "yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
                String timeString = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss.SSS");
                //业务返回参数设置
                String responseBizContent = "{\n" +
                        "    \"code\": 0,\n" +
                        "    \"timestamp\": \""+timestamp+"\",\n" +
                        "    \"message\": \"success\",\n" +
                        "    \"status\": 200,\n" +
                        "    \"time_string\": \""+timeString+"\"\n" +
                        "}";
                response.setContentType("application/json; charset=utf-8");
                PrintWriter out = response.getWriter();
                out.write(responseBizContent);
            }
        }
    }


}