package com.goodsogood.collectionpay.service;

import CCBSign.RSASig;
import ccb.pay.api.util.CCBPayUtil;
import cn.hutool.core.util.XmlUtil;
import com.bocnet.common.security.PKCS7Tool;
import com.goodsogood.collectionpay.consts.*;
import com.goodsogood.collectionpay.dao.AssetDepositChannelConfDao;
import com.goodsogood.collectionpay.dao.CollectionDealerAccountDao;
import com.goodsogood.collectionpay.dao.CollectionDealerAccountLogDao;
import com.goodsogood.collectionpay.dao.CollectionDealerTradeDao;
import com.goodsogood.collectionpay.demo.HttpClientUtil;
import com.goodsogood.collectionpay.demo.MD5;
import com.goodsogood.collectionpay.demo.QrURLDemo;
import com.goodsogood.collectionpay.entity.CollectionDealerAccountEntity;
import com.goodsogood.collectionpay.entity.CollectionDealerAccountLogEntity;
import com.goodsogood.collectionpay.entity.CollectionDealerTradeEntity;
import com.goodsogood.collectionpay.model.request.TradeCreateRequest;
import com.goodsogood.collectionpay.model.response.TradeCreateResponse;
import com.goodsogood.collectionpay.redis.RedisPool;
import com.goodsogood.collectionpay.util.*;
import com.icbc.api.internal.util.StringUtils;
import com.icbc.api.internal.util.internal.util.fastjson.JSON;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import redis.clients.jedis.Jedis;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.servlet.http.HttpServletResponse;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.xpath.XPathConstants;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;



/***
 * 
 * @Description
 * 建设银行
 * <AUTHOR>
 * @time 2019年12月25日上午10:38:37
 */
@Service("CCBService")
public class CCBService {
    /**
     * 建行签名类jar包
     */
    private  RSASig rsaSig = new RSASig();

    @Autowired
    private TradeNoGenerater tradeNoGenerater;

    @Autowired
    private CollectionDealerTradeDao collectionDealerTradeDao ;

    @Autowired
    private CollectionDealerAccountDao collectionDealerAccountDao ;

    @Autowired
    private CollectionDealerAccountLogDao collectionDealerAccountLogDao ;

    @Autowired
    private AssetDepositChannelConfDao assetDepositChannelConfDao ;

    @Autowired
    private NotifyService notifyService;

    @Autowired
    private SybPayService sybPayService;

    @Autowired
    private RedisPool redisPool ;

    @Autowired
    private AssetDepositChannelConfService assetDepositChannelConfService;

    @Autowired
    private FileServices fileServices;

    static final Logger logger = LoggerFactory.getLogger(LogConfiger.APP);


    public TradeCreateResponse buildOrder(TradeCreateResponse resp, TradeCreateRequest request,
                                             CollectionDealerTradeEntity order, Map<String, String> config)
                                            throws UnsupportedEncodingException {

        if("CCB_H5_017_JB".equals(request.getChannel())) {
            Map<String, Object> parameters = new HashMap<>();
            try {
                logger.info("CCB_H5_017接收支付系统ciphertext={}",request.getCiphertext());
                logger.info("CCB_H5_017接收支付系统transferKey={}",request.getTransferKey());
                //String ciphertext = URLDecoder.decode(request.getCiphertext(),"utf-8") ;
                String ciphertext = request.getCiphertext();
                logger.info("CCB_H5_017接收支付系统ciphertext解码过后={}",request.getCiphertext());
                String transferKey = request.getTransferKey();
                String decode = DESCoderUtil.decode(transferKey, ciphertext);
                request = com.alibaba.fastjson.JSON.parseObject(decode, TradeCreateRequest.class);
                //017订单号由党费生成
                order.setTradeNo(request.getTradeNo());
                //查询订单状态
                CollectionDealerTradeEntity oriOrder = this.collectionDealerTradeDao.findByTradeNo(request.getTradeNo());
                //订单已经存在
                if(null!=oriOrder){
                    if(oriOrder.getStatus()==1){
                        parameters.put("CCB_H5_017:err-msg", "订单已经支付成功,无须再次支付！");
                    }else {
                        //如果订单已经存在 直接返回订单号 前端做跳转
                        parameters.put("tradeNo", order.getTradeNo());
                        parameters.put("jumpUrl", config.get("jumpUrl")+order.getTradeNo());
                    }
                    resp.setData(parameters);
                    return resp;
                }
            } catch (Exception exception) {
                logger.info("CCB_H5_017:017参数解密失败", exception);
                parameters.put("err-msg", "CCB_H5_017:017参数解密失败,信息"+exception.getMessage());
                return resp;
            }
        }else {
            order.setTradeNo(tradeNoGenerater.getNewTradeNo(TradeTypeCode.COLLECTION_CCB));
        }
        String orderNo = order.getTradeNo();
        //生成建设银行订单号
        //String orderNo = tradeNoGenerater.getMerchantsTradeNo(TradeTypeCode.COLLECTION_CCB);
        String bankURL = config.get("BANKURL");
        String MERCHANTID = config.get("MERCHANTID");
        String POSID =  config.get("POSID");
        String BRANCHID = config.get("BRANCHID");
        String ORDERID =orderNo;
        String CURCODE=config.get("CURCODE");
        String TXCODE = config.get("TXCODE");
        String REMARK1 =escape(request.getUserId());
        String REMARK2 = REMARK1;
        String PUB= config.get("PUB");
        String PUB32TR2= PUB.substring(PUB.length() - 30);
        //商品信息
        String PROINFO = escape(request.getBody());
        //設置金額
        String PAYMENT= new BigDecimal(request.getAmount()).divide(new BigDecimal(100))
                .setScale(2, RoundingMode.HALF_UP).toString();

        //生成建设银行订单号
        int price = Integer.valueOf(request.getAmount());
        order.setAmount((long) price);
        order.setBody(request.getBody());
        order.setSubject(request.getBody());
        order.setDealerId(Integer.valueOf(request.getDealerId()));
        order.setOpenId(request.getOpenId());
        order.setChannel("建行H5支付");
        order.setChannelCode(request.getChannel());
        order.setCreateTime(new Date());
        order.setStatus(TradeStatus.WAIT_PAY);
        order.setStatusDes("待支付");
        order.setTradeNo(orderNo);
        Long orgId = request.getOrgId();
        order.setUserId(""+request.getUserId());
        order.setOpenId(request.getUserId());
        order.setOrgId(orgId);
        order.setBody(request.getBody());
        order.setNotifyUrl(request.getNotifyUrl());
        order.setVersion(0L);
        this.collectionDealerTradeDao.create(order);

        StringBuffer tmp = new StringBuffer();
        tmp.append("MERCHANTID=");
        tmp.append(MERCHANTID);
        tmp.append("&POSID=");
        tmp.append(POSID);
        tmp.append("&BRANCHID=");
        tmp.append(BRANCHID);
        tmp.append("&ORDERID=");
        tmp.append(ORDERID);
        tmp.append("&PAYMENT=");
        tmp.append(PAYMENT);
        tmp.append("&CURCODE=");
        tmp.append(CURCODE);
        tmp.append("&TXCODE=");
        tmp.append(TXCODE);
        tmp.append("&REMARK1=");
        tmp.append(REMARK1);
        tmp.append("&REMARK2=");
        tmp.append(REMARK2);
        tmp.append("&TYPE=");
        tmp.append("1");
        tmp.append("&PUB=");
        tmp.append(PUB32TR2);
        tmp.append("&GATEWAY=0");
        tmp.append("&CLIENTIP=");
        tmp.append("&REGINFO=");
        tmp.append("&PROINFO=");
        tmp.append(PROINFO);
        tmp.append("&REFERER=");

        logger.info("temp={}",tmp);
        String MAC = MD5.md5Str(tmp.toString());
        logger.info("MAC={}",MAC);
        String replacePUBStr="&PUB="+PUB32TR2;
        String jumpUrl =bankURL+"?"+ tmp.toString().replace(replacePUBStr,"")+"&MAC="+MAC+"&PAYMAP="+config.get("PAYMAP");
        //设定支付方式
        Jedis jedis = null;
        try {
            jedis = redisPool.getResource();
            logger.info("CCB respBody=\n{}", jumpUrl);
            jedis.setex(RedisKey.COMPANY_RECHARGE_ALIPAY_FORM + order.getTradeNo(),
                    1200, jumpUrl);
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("tradeNo", order.getTradeNo());
            parameters.put("jumpUrl", config.get("jumpUrl")+order.getTradeNo());
            resp.setData(parameters);
        } catch (Exception exception){
            logger.error("执行buildOrder-CCB---发生异常",exception);
        } finally {
            if (jedis != null) {
                redisPool.returnResource(jedis);
            }
        }
        return resp;
    }


	@SuppressWarnings("rawtypes")
	private String createSignContent(Map<String, Object> parameters) {
		StringBuffer sb = new StringBuffer();
		Set es = parameters.entrySet();
		Iterator it = es.iterator();
		while (it.hasNext()) {
			Map.Entry entry = (Map.Entry) it.next();
			String k = (String) entry.getKey();
			Object v = entry.getValue();
			sb.append(k + "=" + v + "&");
		}
		return sb.toString();
	}


    /**
     * 处理建设银行H5回调信息
     * @param param
     * @return
     */
    @Transactional
    public String recevPayResultNotify(CollectionDealerTradeEntity order,
                                       Map<String, String> param) {
        try {
            if(order!=null && order.getStatus()== TradeStatus.WAIT_PAY){
                //验证签名
                if(queryPayIsSuc(param,order)){
                    order.setPayTime(new Date());
                    order.setStatus(TradeStatus.SUCCESS);
                    order.setStatusDes("已支付");
                    order.setAttach(com.abc.pay.client.Base64Code.Decode64(param.get("MSG")));
                    this.collectionDealerTradeDao.update(order);

                    Map<String, String> notifyParams = new HashMap<>();
                    notifyParams.put("tradeNo", order.getTradeNo());
                    notifyParams.put("outTranNumber", order.getOutTradeNo());
                    notifyParams.put("createTime", DateTimeUtil.format(order.getCreateTime()));
                    notifyParams.put("payTime", DateTimeUtil.format(order.getPayTime()));
                    notifyParams.put("status", order.getStatus() + "");
                    notifyParams.put("statusDes", order.getStatusDes() + "");
                    notifyParams.put("amount", order.getAmount().toString());
                    notifyParams.put("openid", order.getOpenId());
                    notifyParams.put("channel", order.getChannel());
                    //如果017生成文件 通過ftp 傳入到內網
                    if(order.getChannelCode().startsWith("CCB_H5_017_")){
                        List<CollectionDealerTradeEntity> collectionDealerTradeEntities = Collections.singletonList(order);
                        fileServices.createFtpFile(collectionDealerTradeEntities);
                    }else {
                        this.notifyService.submit(order.getNotifyUrl(), notifyParams);
                    }
                    this.notifyService.submit(order.getNotifyUrl(), notifyParams);
                    logger.info("回调处理完成!tradeNo={},", order.getTradeNo());
                }else{
                    logger.error("订单号：{}验证失败!",order.getTradeNo());
                }
            }
        } catch (Exception ex) {
            logger.error("CCBBankService recevPayResultNotify err!",ex);
            return  "FAIL";
        }
        return  "SUCCESS";
    }


    /**
     * 查询CCB H5支付是否成功
     * @param config
     * @return
     */
    private boolean queryPayIsSuc(Map<String, String> config,CollectionDealerTradeEntity order){
        String MERCHANTID = config.get("MERCHANTID");
        String BRANCHID=config.get("BRANCHID");  //分行代码
        String POSID=config.get("POSID"); //柜台号
        String ORDERDATE = DateTimeUtil.getFormatDateStr(order.getCreateTime(), "yyyyMMdd"); //订单日期
        String BEGORDERTIME="00:00:00";
        String ENDORDERTIME="23:59:59";
        String QUPWD="Li_yong86";
        String TXCODE="410408";
        String SEL_TYPE="3";
        String OPERATOR="999";

        //txcode=410408
        String TYPE="0";
        //已结算流水
        String KIND="1";
        String STATUS="1";
        String ORDERID = order.getTradeNo();
        String PAGE = "1";
        String CHANNEL = "";
        String bankURL = config.get("BANKURL");
        String param ="MERCHANTID="+MERCHANTID+"&BRANCHID="+BRANCHID+"&POSID="+POSID+"&ORDERDATE="
                +ORDERDATE+"&BEGORDERTIME="+BEGORDERTIME+"&ENDORDERTIME="+ENDORDERTIME+"&ORDERID="
                +ORDERID+"&QUPWD=&TXCODE="+TXCODE+"&TYPE="+TYPE+"&KIND="+KIND+"&STATUS="+STATUS+
                "&SEL_TYPE="+SEL_TYPE+"&PAGE="+PAGE+"&OPERATOR="+OPERATOR+"&CHANNEL="+CHANNEL;
        Map map = new HashMap();
        map.put("MERCHANTID",MERCHANTID);
        map.put("BRANCHID",BRANCHID);
        map.put("POSID",POSID);
        map.put("ORDERDATE",ORDERDATE);
        map.put("BEGORDERTIME",BEGORDERTIME);
        map.put("ENDORDERTIME",ENDORDERTIME);
        map.put("QUPWD",QUPWD);
        map.put("TXCODE",TXCODE);
        if("410408".equals(TXCODE)){
            map.put("TYPE",TYPE);
            map.put("KIND",KIND);
            map.put("STATUS",STATUS);
            map.put("ORDERID",ORDERID);
            map.put("PAGE",PAGE);
            map.put("CHANNEL",CHANNEL);
        }
        map.put("SEL_TYPE",SEL_TYPE);
        map.put("OPERATOR",OPERATOR);
        map.put("MAC",MD5.md5Str(param));
        //主动查询支付是否成功
        String ret = HttpClientUtil.httpPost(bankURL, map).replace("\n", "").replace("\r", "");
        Document docResult=XmlUtil.parseXml(ret);
        Object returnCode = XmlUtil.getByXPath("//DOCUMENT/RETURN_CODE", docResult, XPathConstants.STRING);
        Object statusCode = XmlUtil.getByXPath("//DOCUMENT/QUERYORDER/STATUSCODE", docResult, XPathConstants.STRING);
        Object amount = XmlUtil.getByXPath("//DOCUMENT/QUERYORDER/AMOUNT", docResult, XPathConstants.STRING).toString();
        if("000000".equals(returnCode) && "1".equals(statusCode)) {
            //設置金額
            String PAYMENT= new BigDecimal(order.getAmount()).divide(new BigDecimal(100))
                    .setScale(2, RoundingMode.HALF_UP).toString();
            return Objects.equals(PAYMENT, amount);
        }else {
            return false;
        }
    }

    public  String escape(String src) {
        if(null==src){
            return "";
        }
        int i;
        char j;
        StringBuffer tmp = new StringBuffer();
        tmp.ensureCapacity(src.length() * 6);
        for (i = 0; i < src.length(); i++) {
            j = src.charAt(i);
            if (Character.isDigit(j) || Character.isLowerCase(j)
                    || Character.isUpperCase(j))
                tmp.append(j);
            else if (j < 256) {
                tmp.append("%");
                if (j < 16)
                    tmp.append("0");
                tmp.append(Integer.toString(j, 16));
            } else {
                tmp.append("%u");
                tmp.append(Integer.toString(j, 16));
            }
        }
        return tmp.toString();
    }


    /**
     * 建行数字人民币支付
     * @param resp
     * @param request
     * @param order
     * @param config
     * @return
     */
    public TradeCreateResponse createResponseCBCDIGITAL(TradeCreateResponse resp,
                                                        TradeCreateRequest request,
                                                        CollectionDealerTradeEntity order,
                                                        Map<String, String> config) throws UnsupportedEncodingException {
        //生成建设银行订单号
        String orderNo = tradeNoGenerater.getMerchantsTradeNo(TradeTypeCode.COLLECTION_DIGITAL_CBC);
        int price = Integer.valueOf(request.getAmount());
        order.setAmount((long) price);
        order.setBody(request.getSubject());
        order.setSubject(request.getSubject());
        order.setDealerId(Integer.valueOf(request.getDealerId()));
        order.setOpenId(request.getOpenId());
        order.setChannel("建行数字人民币");
        order.setChannelCode(request.getChannel());
        order.setCreateTime(new Date());
        order.setStatus(TradeStatus.WAIT_PAY);
        order.setStatusDes("待支付");
        order.setTradeNo(orderNo);
        Long orgId = request.getOrgId();
        order.setUserId(""+request.getUserId());
        order.setOrgId(orgId);
        order.setBody(request.getBody());
        order.setNotifyUrl(request.getNotifyUrl());
        order.setVersion(0L);
        this.collectionDealerTradeDao.create(order);

        String tradeUrl = config.get("tradeUrl");
        String MERCHANTID = config.get("MERCHANTID");
        String POSID =  config.get("POSID");
        String BRANCHID = config.get("BRANCHID");
        String ORDERID =orderNo;
        //設置金額
        String PAYMENT= new BigDecimal(request.getAmount()).divide(new BigDecimal(100))
                .setScale(2, RoundingMode.HALF_UP).toString();
        String CURCODE="01";
        String TXCODE = "HT0000";
        String REMARK1 = escape(request.getBody());
        String REMARK2 = REMARK1;
        String PUB=config.get("PUB");
        String PUB32TR2= PUB.substring(PUB.length() - 30);

        StringBuffer tmp = new StringBuffer();
        tmp.append("MERCHANTID=");
        tmp.append(MERCHANTID);
        tmp.append("&POSID=");
        tmp.append(POSID);
        tmp.append("&BRANCHID=");
        tmp.append(BRANCHID);
        tmp.append("&ORDERID=");
        tmp.append(ORDERID);
        tmp.append("&PAYMENT=");
        tmp.append(PAYMENT);
        tmp.append("&CURCODE=");
        tmp.append(CURCODE);
        tmp.append("&TXCODE=");
        tmp.append(TXCODE);
        tmp.append("&REMARK1=");
        tmp.append(REMARK1);
        tmp.append("&REMARK2=");
        tmp.append(REMARK2);
        tmp.append("&RETURNTYPE=1");
        tmp.append("&TIMEOUT=");
        tmp.append("&PUB=");
        tmp.append(PUB32TR2);
        String MAC = MD5.md5Str(tmp.toString());
        String tmpStr = tmp.toString().replace("&PUB="+PUB32TR2, "");
        System.out.println(MAC);
        String jumpUrl =tradeUrl+ "&"+tmpStr +"&MAC="+MAC;
        Jedis jedis = null;
        try {
            jedis = redisPool.getResource();
            logger.info("CBCDIGITAL jumpUrl={}", jumpUrl);
            jedis.setex(RedisKey.COMPANY_RECHARGE_ALIPAY_FORM + order.getTradeNo(), 1200, jumpUrl);
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("tradeNo", order.getTradeNo());
            resp.setData(parameters);
            return resp;
        } catch (Exception exception){
            logger.error("执行createResponseCBC---发生异常-{}",exception.getMessage());
            return null;
        } finally {
            if (jedis != null) {
                redisPool.returnResource(jedis);
            }
        }
    }


    /**
     * 建行数字人民币查询接口
     * @return
     */
    public void receivePayResultNotifyDIGITAL(CollectionDealerTradeEntity oriOrder,
                                              Map<String, String> config) {
                Map<String, String> queryResult = sybPayService.queryCBCDIGITAL(config, oriOrder);
                String reCode = queryResult.get("reCode");
                String statusCode=queryResult.get("statusCode");
                if (reCode.equals("true")) {
                    if("00".equals(statusCode)){
                        oriOrder.setPayTime(new Date());
                        oriOrder.setStatus(TradeStatus.SUCCESS);
                        oriOrder.setStatusDes("已支付");
                        oriOrder.setAttach(JSON.toJSONString(queryResult));
                        this.collectionDealerTradeDao.update(oriOrder);
                        CollectionDealerAccountEntity dealerAccount = this.collectionDealerAccountDao
                                .findByDealerId(oriOrder.getDealerId());
                        long newBalance = dealerAccount.getBalance().longValue() +
                                oriOrder.getAmount().longValue();
                        dealerAccount.setBalance(newBalance);
                        dealerAccount.setWxpayNum(dealerAccount.getWxpayNum().longValue() + 1);
                        dealerAccount.setWxpaySum(dealerAccount.getWxpaySum().longValue() +
                                oriOrder.getAmount().longValue());
                        this.collectionDealerAccountDao.update(dealerAccount);
                        CollectionDealerAccountLogEntity log = new CollectionDealerAccountLogEntity();
                        log.setAction("建行数字人民支付");
                        log.setAmount(oriOrder.getAmount().longValue());
                        log.setChannel("建行数字人民支付");
                        log.setTradeNo(oriOrder.getTradeNo());
                        log.setContent(oriOrder.getBody());
                        log.setCreateTime(new Date());
                        log.setDealerBalance(newBalance);
                        log.setInOrOut(FlowType.IN + "");
                        log.setDealerId(oriOrder.getDealerId());
                        log.setLogId(UUIDUtils.uuid32());
                        log.setOpenId(oriOrder.getOpenId());
                        this.collectionDealerAccountLogDao.create(log);
                        Map<String, String> notifyParams = new HashMap<>();
                        notifyParams.put("tradeNo", oriOrder.getTradeNo());
                        notifyParams.put("createTime", DateTimeUtil.format(oriOrder.getCreateTime()));
                        notifyParams.put("payTime", DateTimeUtil.format(oriOrder.getPayTime()));
                        notifyParams.put("status", oriOrder.getStatus() + "");
                        notifyParams.put("statusDes", oriOrder.getStatusDes() + "");
                        notifyParams.put("amount", oriOrder.getAmount().toString());
                        notifyParams.put("openid", oriOrder.getOpenId());
                        notifyParams.put("channel", oriOrder.getChannel());
                        this.notifyService.submit(oriOrder.getNotifyUrl(), notifyParams);
                        logger.info("回调处理完成!tradeNo={},", oriOrder.getTradeNo());
                    }
                }
    }



    @Transactional
    public String cbcDigitalNotify(Map<String, String> params, String notifyURLParam, HttpServletResponse response) {
        CollectionDealerTradeEntity oriOrder = this.collectionDealerTradeDao.findByTradeNo(params.get("ORDERID"));
        Map<String, String> config = assetDepositChannelConfService.findOptionsByChannel(oriOrder.getChannelCode());
        if (oriOrder.getStatus() == TradeStatus.SUCCESS) {
            //response.sendRedirect(config.get(redirect_url"));
            return "SUCCESS";
        }
        int i = notifyURLParam.indexOf("&SIGN=");
        //获取签名内容原串
        String strSrc = notifyURLParam.substring(0, i);
        //获取数字签名域
        String sign = notifyURLParam.substring(i+6);
        //验证签名数据
        CCBPayUtil ccbPayUtil = new CCBPayUtil();
        boolean b = ccbPayUtil.verifyNotifySign(strSrc,sign,config.get("PUB"));
        if(b){
            logger.info("cbcDigitalNotify,签名验证成功！");
            String reCode = params.get("SUCCESS");
            //如果成功了
            if("Y".equals(reCode)){
                oriOrder.setPayTime(new Date());
                oriOrder.setStatus(TradeStatus.SUCCESS);
                oriOrder.setStatusDes("已支付");
                this.collectionDealerTradeDao.update(oriOrder);

                Map<String, String> notifyParams = new HashMap<>();
                notifyParams.put("tradeNo", oriOrder.getTradeNo());
                notifyParams.put("createTime", DateTimeUtil.format(oriOrder.getCreateTime()));
                notifyParams.put("payTime", DateTimeUtil.format(oriOrder.getPayTime()));
                notifyParams.put("status", oriOrder.getStatus() + "");
                notifyParams.put("statusDes", oriOrder.getStatusDes() + "");
                notifyParams.put("amount", oriOrder.getAmount().toString());
                notifyParams.put("openid", oriOrder.getOpenId());
                notifyParams.put("channel", oriOrder.getChannel());
                this.notifyService.submit(oriOrder.getNotifyUrl(), notifyParams);
                logger.info("回调处理完成!tradeNo={},", oriOrder.getTradeNo());
                return "SUCCESS";
            }else {
                return "FAIL";
            }
        } else {
            logger.info("cbcDigitalNotify,签名验证失败！");
            return "FAIL";
        }
    }

    public void receivePayResultNotifyBocDIGITAL(CollectionDealerTradeEntity oriOrder, Map<String, String> config) {
            //1、商户业务字段拼装
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String requestTime = sdf.format(new Date()); //请求时间

            String merchantNo = config.get("merchantNo"); //中行商户号
            String merOrderNo = oriOrder.getTradeNo(); //商户系统内部订单号
            String nonceStr = UUID.randomUUID().toString().replace("-", "");

            // 处理请求原文XML拼接 此方式按照JAVA自带方法转XML 如果用DOM4J 等请自行引包开发
            DocumentBuilderFactory documentbuilderfactory = DocumentBuilderFactory.newInstance();
            DocumentBuilder documentbuilder;
            String result  ="";
            try {
                documentbuilder = documentbuilderfactory.newDocumentBuilder();
                Document document = documentbuilder.newDocument();
                document.setXmlStandalone(true);
                Element element = document.createElement("request");
                Element elementHead = document.createElement("head");
                Element elementRequestTime = document.createElement("requestTime");//请求时间
                Element elementBody = document.createElement("body");
                Element elementMerchantNo = document.createElement("merchantNo");//商户号
                Element elementMerOrderNo= document.createElement("merOrderNo");//订单号
                Element elementNonceStr = document.createElement("nonceStr");//随机字符串

                document.appendChild(element);
                element.appendChild(elementHead);
                element.appendChild(elementBody);
                elementHead.appendChild(elementRequestTime);
                elementRequestTime.setTextContent(requestTime);

                elementBody.appendChild(elementMerchantNo);
                elementBody.appendChild(elementMerOrderNo);
                elementBody.appendChild(elementNonceStr);

                elementMerchantNo.setTextContent(merchantNo);
                elementMerOrderNo.setTextContent(merOrderNo);
                elementNonceStr.setTextContent(nonceStr);
                //2、商户数据加签
                // 将拼接好的转 String报文
                String requestPlainText = DigitalServices.createPrettyFormat(document, "UTF-8").trim();
                logger.info("---------- EPTQueryPayStatus send message before base64 encoded ----------");
                logger.info("[merchantNo]=[" + merchantNo + "]");
                logger.info("[message]=[" + requestPlainText + "]");
                String signData ="";
                //这里是测试证书加签,根据生产上的环境切换成产的证书
                String pfxPath = config.get("sign_file_name_url");
                PKCS7Tool signer = PKCS7Tool.getSigner(pfxPath, config.get("sign_file_password"),
                        config.get("sign_file_password"));
                //调用加签方法
                signData = signer.sign(requestPlainText.getBytes("UTF-8"));
                //3、商户拼装上送报文
                //将请求原文进行Base64转码 加签数据不用转码 方法里面已经转了
                BASE64Encoder encoder = new BASE64Encoder();
                String base64MerchantNo = encoder.encode(merchantNo.getBytes("UTF-8"));
                String base64version = encoder.encode("1.0.1".getBytes("UTF-8"));
                String base64messageId = encoder.encode("219702".getBytes("UTF-8"));
                String base64security = encoder.encode("P7".getBytes("UTF-8"));
                String base64message = encoder.encode(requestPlainText.getBytes("UTF-8"));
                //发送支付请求到银行
                HttpClient httpClient = new DefaultHttpClient();
                List<NameValuePair> formParams = new ArrayList<NameValuePair>();
                formParams.add(new BasicNameValuePair("merchantNo", base64MerchantNo));
                formParams.add(new BasicNameValuePair("version", base64version));
                formParams.add(new BasicNameValuePair("messageId", base64messageId));
                formParams.add(new BasicNameValuePair("security", base64security));
                formParams.add(new BasicNameValuePair("message", base64message));
                formParams.add(new BasicNameValuePair("signature", signData));
                //4、商户向银行方发请求
                HttpEntity entity = new UrlEncodedFormEntity(formParams, "UTF-8");
                String queryUrl = config.get("queryUrl");
                HttpPost post = new HttpPost(queryUrl);
                post.setEntity(entity);
                HttpResponse postRes = httpClient.execute(post);
                HttpEntity entityResult = postRes.getEntity();
                String rtnResult =  EntityUtils.toString(entityResult);
                // 返回数据 message参数与signature参数以","分隔
                int i = rtnResult.indexOf(',');
                if(i<=0){
                    throw new Exception("return error!");
                }
                String rtnMessage = rtnResult.substring(0, i);
                String rtnSignture = rtnResult.substring(i + 1);
                //5、商户验证银行返回数据签名
                //Base64解码 验签数据不用解码只解码原文
                BASE64Decoder decoder = new BASE64Decoder();
                logger.info("[message]=[" + rtnMessage + "]");
                logger.info("[signature]=[" + rtnSignture + "]");
                result = new String(decoder.decodeBuffer(rtnMessage), "UTF-8");
                logger.info("[decoder result]=[" + result + "]");
                // 验签 优先使用boccfcaTest 不行再用boccaTest 生产上要替换为生产的验签公钥
                String pfxPathVer = config.get("verify_file_name_url");
                PKCS7Tool tool = PKCS7Tool.getVerifier(pfxPathVer);
                //调用方法验签
                tool.verify(rtnSignture, result.getBytes("UTF-8"), null);
                // 不抛出异常表示验签成功
                logger.info("[VERIFY OK]");
                //6、商户解析各个业务字段
                //解析返回结果
                Map<String, Object> mapResult = XmlUtil.xmlToMap(result);
                Map<String,Object> mapHeader= (Map<String, Object>) mapResult.get("head");
                Map<String,Object> mapBody= (Map<String, Object>) mapResult.get("body");
                if ("OK".equals(mapHeader.get("responseCode"))) {
                    Object tradeState = mapBody.get("tradeState");
                    if ("03".equals(tradeState)) {
                        //更新订单状态
                        oriOrder.setPayTime(new Date());
                        oriOrder.setStatus(TradeStatus.SUCCESS);
                        oriOrder.setStatusDes("已支付");
                        this.collectionDealerTradeDao.update(oriOrder);
                        //回调
                        Map<String, String> notifyParams = new HashMap<>();
                        notifyParams.put("tradeNo", oriOrder.getTradeNo());
                        notifyParams.put("outTranNumber", oriOrder.getOutTradeNo());
                        notifyParams.put("createTime", DateTimeUtil.format(oriOrder.getCreateTime()));
                        notifyParams.put("payTime", DateTimeUtil.format(oriOrder.getPayTime()));
                        notifyParams.put("status", oriOrder.getStatus() + "");
                        notifyParams.put("statusDes", oriOrder.getStatusDes() + "");
                        notifyParams.put("amount", oriOrder.getAmount().toString());
                        notifyParams.put("openid", oriOrder.getOpenId());
                        notifyParams.put("channel", oriOrder.getChannel());
                        this.notifyService.submit(oriOrder.getNotifyUrl(), notifyParams);
                        logger.info("处理完成!tradeNo={},", oriOrder.getTradeNo());
                    }
                }
            } catch (Exception e) {
                logger.error("中行人民币主动查询异常",e);
            }
    }

    @Transactional
    public void ccbH5PayNotify(Map<String, String> params, HttpServletResponse response) throws Exception {
        CollectionDealerTradeEntity order = this.collectionDealerTradeDao.findByTradeNo(params.get("ORDERID"));
        Map<String, String> config = assetDepositChannelConfService.findOptionsByChannel(order.getChannelCode());
        if(order==null){
            logger.info("ccbH5PayNotify回调没有查询到订单信息,{}",params);
            return;
        }
        if(order.getStatus()==1){
            logger.info("订单已经处理过了,order={}",order);
            return;
        }
        //設置金額
        String payAmount= new BigDecimal(order.getAmount()).divide(new BigDecimal(100))
                .setScale(2, RoundingMode.HALF_UP).toString();
        if(!Objects.equals(payAmount, params.get("PAYMENT"))) {
            logger.info("ccbH5PayNotify金额查询不一致,{}",params);
            return;
        }
        String POSID = params.get("POSID");
        String BRANCHID = params.get("BRANCHID");
        String ORDERID = params.get("ORDERID");
        String PAYMENT = params.get("PAYMENT");
        String CURCODE = params.get("CURCODE");
        String REMARK1 = params.containsKey("REMARK1")? escape(params.get("REMARK1")):"";
        String REMARK2 = params.containsKey("REMARK2")? escape(params.get("REMARK2")):"";
        String SUCCESS = params.get("SUCCESS");
        String ACC_TYPE = params.get("ACC_TYPE");
        String ACCDATE = params.get("ACCDATE");

        //验证签名是不是正确的
        StringBuffer tmp = new StringBuffer();
        tmp.append("POSID=");
        tmp.append(POSID);
        tmp.append("&BRANCHID=");
        tmp.append(BRANCHID);
        tmp.append("&ORDERID=");
        tmp.append(ORDERID);
        tmp.append("&PAYMENT=");
        tmp.append(PAYMENT);
        tmp.append("&CURCODE=");
        tmp.append(CURCODE);
        tmp.append("&REMARK1=");
        tmp.append(REMARK1);
        tmp.append("&REMARK2=");
        tmp.append(REMARK2);
        tmp.append("&ACC_TYPE=");
        tmp.append(ACC_TYPE);
        tmp.append("&SUCCESS=");
        tmp.append(SUCCESS);
        tmp.append("&ACCDATE=");
        tmp.append(ACCDATE);
        RSASig rsaSig = new RSASig();
        rsaSig.setPublicKey(config.get("PUB"));
        if(rsaSig.verifySigature(params.get("SIGN"),tmp.toString())){
            //更新订单状态
            order.setPayTime(new Date());
            order.setStatus(TradeStatus.SUCCESS);
            order.setStatusDes("已支付");
            this.collectionDealerTradeDao.update(order);
            //回调
            Map<String, String> notifyParams = new HashMap<>();
            notifyParams.put("tradeNo", order.getTradeNo());
            notifyParams.put("outTranNumber", order.getOutTradeNo());
            notifyParams.put("createTime", DateTimeUtil.format(order.getCreateTime()));
            notifyParams.put("payTime", DateTimeUtil.format(order.getPayTime()));
            notifyParams.put("status", order.getStatus() + "");
            notifyParams.put("statusDes", order.getStatusDes() + "");
            notifyParams.put("amount", order.getAmount().toString());
            notifyParams.put("openid", order.getOpenId());
            notifyParams.put("channel", order.getChannel());
            //如果017生成文件 通過ftp 傳入到內網
            if(order.getChannelCode().startsWith("CCB_H5_017_")){
                List<CollectionDealerTradeEntity> collectionDealerTradeEntities = Collections.singletonList(order);
                fileServices.createFtpFile(collectionDealerTradeEntities);
            }else {
                this.notifyService.submit(order.getNotifyUrl(), notifyParams);
            }
            logger.info("处理完成!tradeNo={},", order.getTradeNo());
        }
    }



}
