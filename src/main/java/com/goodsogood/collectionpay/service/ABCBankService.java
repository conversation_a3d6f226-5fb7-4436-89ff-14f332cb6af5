package com.goodsogood.collectionpay.service;

import com.abc.pay.client.JSON;
import com.abc.pay.client.TrxException;
import com.abc.pay.client.ebus.PaymentResult;
import com.abc.pay.client.ebus.UnifiedPaymentRequest;
import com.goodsogood.collectionpay.consts.FlowType;
import com.goodsogood.collectionpay.consts.LogConfiger;
import com.goodsogood.collectionpay.consts.TradeStatus;
import com.goodsogood.collectionpay.dao.CollectionDealerAccountDao;
import com.goodsogood.collectionpay.dao.CollectionDealerAccountLogDao;
import com.goodsogood.collectionpay.dao.CollectionDealerTradeDao;
import com.goodsogood.collectionpay.entity.CollectionDealerAccountEntity;
import com.goodsogood.collectionpay.entity.CollectionDealerAccountLogEntity;
import com.goodsogood.collectionpay.entity.CollectionDealerTradeEntity;
import com.goodsogood.collectionpay.model.request.TradeCreateRequest;
import com.goodsogood.collectionpay.util.AmountUtil;
import com.goodsogood.collectionpay.util.DateTimeUtil;
import com.goodsogood.collectionpay.util.UUIDUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 农业银行网上支付平台接口
 * @Description
 * <AUTHOR>
 * @time 2019年2月28日上午11:23:05
 */
@Service("ABCBankService")
public class ABCBankService {
	
	 static final Logger logger = LoggerFactory.getLogger(LogConfiger.APP);
	
	@Autowired
	private CollectionDealerTradeDao collectionDealerTradeDao ;
	
	@Autowired
	private CollectionDealerAccountDao collectionDealerAccountDao ;
	
	@Autowired
	private CollectionDealerAccountLogDao collectionDealerAccountLogDao ; 
	
	@Autowired
	private NotifyService notifyService;
	
	
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public JSON pay(TradeCreateRequest param, int confIndex,String orderNo) {

		SimpleDateFormat dateFormat = new SimpleDateFormat("YYYY/MM/dd");
		SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm:ss");
		// 1、生成订单对象
		UnifiedPaymentRequest tPaymentRequest = new UnifiedPaymentRequest();
		/**
		 * NATIVE： 原生扫码支付 JSAPI： 公众号支付 APP： app支付 MICROPAY： 微信刷卡支付 MWEB:微信H5支付
		 */
		tPaymentRequest.dicOrder.put("PayTypeID", param.getWxTradeType()); // 设定交易类型
		tPaymentRequest.dicOrder.put("OrderDate", dateFormat.format(new Date())); // 设定订单日期
																					// YYYY/MM/DD）
		tPaymentRequest.dicOrder.put("OrderTime", timeFormat.format(new Date())); // 设定订单时间
																					// （必要信息
																					// -
																					// HH:MM:SS）
		tPaymentRequest.dicOrder.put("OrderNo", orderNo); // 设定订单编号
															// （必要信息）
		tPaymentRequest.dicOrder.put("CurrencyCode", "156"); // 设定交易币种
		tPaymentRequest.dicOrder.put("OrderAmount", AmountUtil.divide(Double.valueOf(param.getAmount()), 100D, 2)); // 设定交易金额

		if ("JSAPI".equals(param.getWxTradeType())) {
			tPaymentRequest.dicOrder.put("OpenID", param.getOpenId()); // 设定用户在子商户公众号下面的ID
			tPaymentRequest.dicOrder.put("AccountNo", param.getWxAppId()); // 设定支付账户
		}
		tPaymentRequest.dicOrder.put("OrderDesc", param.getSubject()); // 设定订单说明
		tPaymentRequest.dicOrder.put("InstallmentMark", "0"); // 分期标识 ;1： 分期； 0： 不分期
		tPaymentRequest.dicOrder.put("CommodityType", "0499"); // 设置商品种类

		// 2、订单明细
		LinkedHashMap orderitem = new LinkedHashMap();

		orderitem.put("ProductName", param.getSubject());// 商品名称

		tPaymentRequest.orderitems.put(1, orderitem);

		// 3、生成支付请求对象
		String paymentType = "8"; // :微信支付;
		tPaymentRequest.dicRequest.put("PaymentType", paymentType); // 设定支付类型
		String paymentLinkType = "1"; // 交易渠道 必须设定， 1： internet 网络接入
		tPaymentRequest.dicRequest.put("PaymentLinkType", paymentLinkType); // 设定支付接入方式
		tPaymentRequest.dicRequest.put("NotifyType", "1"); // 设定通知方式;1： 服务器通知
		tPaymentRequest.dicRequest.put("ResultNotifyURL", param.getNotifyUrl()); // 设定通知URL地址
		tPaymentRequest.dicRequest.put("IsBreakAccount", "0"); // 设定交易是否支持向二级商户入账;0:否； 1:是
		return tPaymentRequest.extendPostRequest(confIndex);
	}
	
	public 	JSON queryOrder(String orderNo,int confIndex){
		com.abc.pay.client.ebus.QueryOrderRequest request = new com.abc.pay.client.ebus.QueryOrderRequest();
		request.queryRequest.put("PayTypeID", "ImmediatePay");
		request.queryRequest.put("OrderNo", orderNo);
		request.queryRequest.put("QueryDetail", "1");
		return request.extendPostRequest(confIndex);
	}
	
	
	public 	JSON queryTrnxRecords(String SettleDate,String SettleStartHour,String SettleEndHour,int confIndex){
		com.abc.pay.client.ebus.QueryTrnxRecords request =new com.abc.pay.client.ebus.QueryTrnxRecords();
		request.dicRequest.put("SettleDate",SettleDate);
		request.dicRequest.put("SettleStartHour", SettleStartHour);
		request.dicRequest.put("SettleEndHour", SettleEndHour);
		request.dicRequest.put("ZIP", "0");
		return request.extendPostRequest(confIndex);
	}
	
	
	@Transactional
	public String recevPayResultNotify(Map<String, String> param) {
		
		try {
			com.abc.pay.client.ebus.PaymentResult paymentResult = new PaymentResult(param.get("MSG"));
			
			if(paymentResult.isSuccess()){
				
				logger.info("OrderNo={}",paymentResult.getValue("OrderNo"));
				logger.info("Amount={}",paymentResult.getValue("Amount"));
				logger.info("BatchNo={}",paymentResult.getValue("BatchNo"));
				logger.info("VoucherNo={}",paymentResult.getValue("VoucherNo"));
				logger.info("HostDate={}",paymentResult.getValue("HostDate"));
				logger.info("HostTime={}",paymentResult.getValue("HostTime"));
				logger.info("MerchantRemarks={}",paymentResult.getValue("MerchantRemarks"));
				logger.info("PayType={}",paymentResult.getValue("PayType"));
				logger.info("NotifyType={}",paymentResult.getValue("NotifyType"));
				logger.info("iRspRef={}",paymentResult.getValue("iRspRef"));
				
				CollectionDealerTradeEntity oriOrder = 
						this.collectionDealerTradeDao.findByTradeNo(paymentResult.getValue("OrderNo"));
				
				if(oriOrder!=null && oriOrder.getStatus()==TradeStatus.WAIT_PAY){
					
					oriOrder.setPayTime(new Date());
					oriOrder.setStatus(TradeStatus.SUCCESS);
					oriOrder.setStatusDes("已支付");
					oriOrder.setChannelTxnId(paymentResult.getValue("iRspRef"));
					oriOrder.setAttach(com.abc.pay.client.Base64Code.Decode64(param.get("MSG")));
					this.collectionDealerTradeDao.update(oriOrder);

					CollectionDealerAccountEntity dealerAccount = this.collectionDealerAccountDao
							.findByDealerId(oriOrder.getDealerId());

					long newBalance = dealerAccount.getBalance().longValue() + oriOrder.getAmount().longValue();

					dealerAccount.setBalance(newBalance);
					dealerAccount.setWxpayNum(dealerAccount.getWxpayNum().longValue() + 1);
					dealerAccount.setWxpaySum(dealerAccount.getWxpaySum().longValue() + oriOrder.getAmount().longValue());
					this.collectionDealerAccountDao.update(dealerAccount);

					CollectionDealerAccountLogEntity log = new CollectionDealerAccountLogEntity();
					log.setAction("中国农业银行网上支付平台微信支付");
					log.setAmount(oriOrder.getAmount().longValue());
					log.setChannel("微信");
					log.setTradeNo(oriOrder.getTradeNo());
					log.setContent(oriOrder.getBody());
					log.setCreateTime(new Date());
					log.setDealerBalance(newBalance);
					log.setInOrOut(FlowType.IN + "");
					log.setDealerId(oriOrder.getDealerId());
					log.setLogId(UUIDUtils.uuid32());
					log.setOpenId(oriOrder.getOpenId());
					log.setChannelTxnId(paymentResult.getValue("iRspRef"));
					this.collectionDealerAccountLogDao.create(log);

					Map<String, String> notifyParams = new HashMap<>();
					notifyParams.put("tradeNo", oriOrder.getTradeNo());
					notifyParams.put("createTime", DateTimeUtil.format(oriOrder.getCreateTime()));
					notifyParams.put("payTime", DateTimeUtil.format(oriOrder.getPayTime()));
					notifyParams.put("status", oriOrder.getStatus() + "");
					notifyParams.put("statusDes", oriOrder.getStatusDes() + "");
					notifyParams.put("amount", oriOrder.getAmount().toString());
					notifyParams.put("openid", oriOrder.getOpenId());
					notifyParams.put("channel", oriOrder.getChannel());
					this.notifyService.submit(oriOrder.getNotifyUrl(), notifyParams);
					logger.info("回调处理完成!tradeNo={},", oriOrder.getTradeNo());
				}else{
					logger.info("本地订单不存在或已经处理成功!");
				}
			}else{
				logger.info("ABCBankService recevPayResultNotify paymentResult isSuccess=false!");
			}
		} catch (TrxException e) {
			logger.error("ABCBankService recevPayResultNotify err!",e);
			return  "FAIL";
		}
		return  "SUCCESS";
	}
	
	public static void main(String[] args) {
		
		/*SimpleDateFormat dateFormat =new SimpleDateFormat("YYYY/MM/dd");
		SimpleDateFormat timeFormat =new SimpleDateFormat("HH:mm:ss");
		System.out.println(dateFormat.format(new Date()));
		System.out.println(timeFormat.format(new Date()));*/
		
		
		ABCBankService service =new ABCBankService() ;
		JSON json = service.queryOrder("201905134001029269", 1);
		System.out.println(json.GetKeyValue("ReturnCode"));
		System.out.println(json.GetKeyValue("ErrorMessage"));
		
		String orderStr = com.abc.pay.client.Base64Code.Decode64(json.GetKeyValue("Order"));
		System.out.println(orderStr);
		
		Map<String,String> orderMap  =
				com.alibaba.fastjson.JSON.parseObject(orderStr, HashMap.class);
		System.out.println(orderMap.get("iRspRef"));
	
		
		
	}

}
