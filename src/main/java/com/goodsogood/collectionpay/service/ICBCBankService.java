package com.goodsogood.collectionpay.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.goodsogood.collectionpay.consts.ErrorCode;
import com.goodsogood.collectionpay.consts.RedisKey;
import com.goodsogood.collectionpay.consts.TradeStatus;
import com.goodsogood.collectionpay.consts.TradeTypeCode;
import com.goodsogood.collectionpay.dao.CollectionDealerTradeDao;
import com.goodsogood.collectionpay.entity.CollectionDealerTradeEntity;
import com.goodsogood.collectionpay.exception.AssetServiceException;
import com.goodsogood.collectionpay.model.request.TradeCreateRequest;
import com.goodsogood.collectionpay.model.response.TradeCreateResponse;
import com.goodsogood.collectionpay.redis.RedisPool;
import com.goodsogood.collectionpay.util.DESCoderUtil;
import com.goodsogood.collectionpay.util.DateTimeUtil;
import com.goodsogood.collectionpay.util.NumUtil;
import com.goodsogood.collectionpay.util.WebUtil;
import com.icbc.api.DefaultIcbcClient;
import com.icbc.api.IcbcApiException;
import com.icbc.api.IcbcConstants;
import com.icbc.api.UiIcbcClient;
import com.icbc.api.request.CardbusinessAggregatepayB2cOnlineConsumepurchaseRequestV1;
import com.icbc.api.request.CardbusinessAggregatepayB2cOnlineOrderqryRequestV1;
import com.icbc.api.request.JftUiPayH5RequestV3;
import com.icbc.api.request.QueryOrderRequestV1;
import com.icbc.api.response.CardbusinessAggregatepayB2cOnlineConsumepurchaseResponseV1;
import com.icbc.api.response.CardbusinessAggregatepayB2cOnlineOrderqryResponseV1;
import com.icbc.api.response.QueryOrderResponseV1;
import com.icbc.api.utils.IcbcSignature;
import com.icbc.api.utils.WebUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.Jedis;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service("ICBCBankService")
public class ICBCBankService {

    private static final Logger logger = LoggerFactory.getLogger(ICBCBankService.class);

    @Autowired
    private CollectionDealerTradeDao collectionDealerTradeDao;

    @Autowired
    private NotifyService notifyService;

    @Autowired
    private AssetDepositChannelConfService assetDepositChannelConfService;

    @Autowired
    private TradeNoGenerater tradeNoGenerater;

    @Autowired
    private RedisPool redisPool ;

    /**
     * 工行微信支付主动查询
     *
     * @param config
     * @param tradeNo
     * @return
     * @throws IcbcApiException
     */
    private Boolean queryICBCH5PayWxOrder(Map<String, String> config, String tradeNo) throws IcbcApiException {
        String merId = config.get("mer_id");
        String appId = config.get("app_id");
        String rsaPrivateKey = config.get("rsa_private_key");
        String gateWayPubKey = config.get("APIGW_PUBLIC_KEY");
        String tradeQueryUrl = config.get("tradeQueryUrl");


        DefaultIcbcClient client = new DefaultIcbcClient(appId, IcbcConstants.SIGN_TYPE_RSA2, rsaPrivateKey, gateWayPubKey);
        CardbusinessAggregatepayB2cOnlineOrderqryRequestV1 ICBCRequest = new CardbusinessAggregatepayB2cOnlineOrderqryRequestV1();
        ICBCRequest.setServiceUrl(tradeQueryUrl);
        CardbusinessAggregatepayB2cOnlineOrderqryRequestV1.CardbusinessAggregatepayB2cOnlineOrderqryRequestV1Biz bizContent = new CardbusinessAggregatepayB2cOnlineOrderqryRequestV1.CardbusinessAggregatepayB2cOnlineOrderqryRequestV1Biz();
        bizContent.setMer_id(merId);
        bizContent.setOut_trade_no(tradeNo);
        bizContent.setDeal_flag("0");
        bizContent.setIcbc_appid(appId);
        ICBCRequest.setBizContent(bizContent);
        CardbusinessAggregatepayB2cOnlineOrderqryResponseV1 response =
                client.execute(ICBCRequest, System.currentTimeMillis() + "");
        try {
            return response.getReturnCode() == 0 && response.getPay_status().equals("0");
        } catch (Exception ex) {
            logger.info("ICBCBankService.queryICBCH5PayWxOrder 异常", ex);
            return false;
        }

    }

    public void receivePayResultNotifyICBCH5Pay(CollectionDealerTradeEntity oriOrder,
                                                Map<String, String> config) throws IcbcApiException {
        //判断支付是否成功
        Boolean aBoolean = queryICBCH5PayWxOrder(config, oriOrder.getTradeNo());
        if (aBoolean) {
            //更新支付状态
            oriOrder.setPayTime(new Date());
            oriOrder.setStatus(TradeStatus.SUCCESS);
            oriOrder.setStatusDes("已支付");
            collectionDealerTradeDao.update(oriOrder);

            Map<String, String> notifyParams = new HashMap<>();
            notifyParams.put("tradeNo", oriOrder.getTradeNo());
            notifyParams.put("outTranNumber", oriOrder.getOutTradeNo());
            notifyParams.put("createTime", DateTimeUtil.format(oriOrder.getCreateTime()));
            notifyParams.put("payTime", DateTimeUtil.format(oriOrder.getPayTime()));
            notifyParams.put("status", oriOrder.getStatus() + "");
            notifyParams.put("statusDes", oriOrder.getStatusDes() + "");
            notifyParams.put("amount", oriOrder.getAmount().toString());
            notifyParams.put("openid", oriOrder.getOpenId());
            notifyParams.put("channel", oriOrder.getChannel());
            notifyService.submit(oriOrder.getNotifyUrl(), notifyParams);
            System.out.println(JSON.toJSONString(notifyParams));
            logger.info("receivePayResultNotifyICBCH5Pay处理完成!tradeNo={}", oriOrder.getTradeNo());
        }

    }

    /**
     * 工行最新支付文档 接入
     *
     * @param params
     * @param response
     */
    @Transactional
    public void icbcH5PayNotify(Map<String, String> params, HttpServletResponse response) {
        PrintWriter out = null;
        String bizContent = params.get("biz_content");
        Map map = JSON.parseObject(bizContent, Map.class);
        //商户订单号
        String tranNo = map.get("out_trade_no").toString();
        CollectionDealerTradeEntity order = this.collectionDealerTradeDao.findByTradeNo(tranNo);
        logger.info("icbcH5PayNotify待处理订单信息={}", order);
        try {
            Map<String, String> config =
                    this.assetDepositChannelConfService.findOptionsByChannel(order.getChannelCode());
            String responseBizContent= null;
            if (null != config) {
                String path = "/collection-pay/callback/icbc_h5pay";
                Map<String, String> signParams=new HashMap<String, String>();
                signParams.put("from", params.get("from"));
                signParams.put("api", params.get("api"));
                signParams.put("app_id",  params.get("app_id"));
                signParams.put("charset", params.get("charset"));
                signParams.put("format", params.get("format"));
                signParams.put("encrypt_type", params.get("encrypt_type"));
                signParams.put("timestamp", params.get("timestamp"));
                signParams.put("biz_content", params.get("biz_content"));
                //目前上行网关签名暂时仅支持RSA
                signParams.put("sign_type", params.get("sign_type"));
                String signStr = WebUtils.buildOrderedSignStr(path, signParams);
                String gateWayPubKey = config.get("APIGW_PUBLIC_KEY");
                boolean flag = IcbcSignature.verify(signStr, params.get("sign_type"), gateWayPubKey,
                        params.get("charset"), params.get("sign"));
                if(!flag){
                    responseBizContent= "{\"return_code\":-12345,\"return_msg\":\"icbc sign not pass.\"}";
                }else {
                    Map<String, Object> respMap = (Map<String, Object>) JSON.parse(bizContent);
                    String msg_id=respMap.get("msg_id").toString();
                    //业务请求字段获取
                    String busiParamRq=(String)respMap.get("busi_param_rq");
                    logger.info("busiParamRq={}",busiParamRq);
                    if("0".equals(respMap.get("return_code"))) {
                        //更新支付状态
                        order.setPayTime(new Date());
                        order.setStatus(TradeStatus.SUCCESS);
                        order.setStatusDes("已支付");
                        collectionDealerTradeDao.update(order);
                        logger.info("icbcH5PayNotify更新订单状态成功,!tradeNo={}",order.getTradeNo());
                        //回调通知
                        Map<String, String> notifyParams = new HashMap<>();
                        notifyParams.put("tradeNo", order.getTradeNo());
                        notifyParams.put("outTranNumber", order.getOutTradeNo());
                        notifyParams.put("createTime", DateTimeUtil.format(order.getCreateTime()));
                        notifyParams.put("payTime", DateTimeUtil.format(order.getPayTime()));
                        notifyParams.put("status", order.getStatus() + "");
                        notifyParams.put("statusDes", order.getStatusDes() + "");
                        notifyParams.put("amount", order.getAmount().toString());
                        notifyParams.put("openid", order.getOpenId());
                        notifyParams.put("channel", order.getChannel());
                        this.notifyService.submit(order.getNotifyUrl(), notifyParams);
                        logger.info("icbcH5PayNotify回调处理完成!tradeNo={}", order.getTradeNo());
                    }else {
                        logger.info("icbcH5PayNotify支付未完成,tradeNo={}", order.getTradeNo());
                    }
                    //业务返回参数设置
                    int return_code=0;
                    String return_msg="success.";
                    responseBizContent="{\"return_code\":"+return_code+",\"return_msg\":\""+return_msg+"\",\"msg_id\":\""+msg_id+"\","
                            +"\"busi_param_rp\":\"thisisresponseparameter\"}";
                }
                //设置返回参数
                signStr="\"response_biz_content\":"+responseBizContent+","+"\"sign_type\":"+"\"RSA\"";
                String sign=IcbcSignature.sign(signStr, "RSA2", config.get("rsa_private_key"), "UTF-8");
                String results="{"+signStr+",\"sign\":\""+sign+"\"}";
                response.setContentType("application/json; charset=utf-8");
                out = response.getWriter();
                out.write(results);
            }
        } catch (Exception ex) {
            logger.info("icbcH5PayNotify发生异常", ex);
        }finally {
            if(null!=out) {
                out.flush();
                out.close();
            }
        }
    }

    /**
     * 工商聚合支付
     * @param resp
     * @param request
     * @param order
     * @param config
     */
    public void createResponseCBCUNIONPAY(TradeCreateResponse resp, TradeCreateRequest request,
                                          CollectionDealerTradeEntity order, Map<String, String> config) {
        if(request.getChannel().startsWith("ICBC_UNIONPAY_017")) {
            Map<String, Object> parameters = new HashMap<>();
            try {
                logger.info("ICBC_UNIONPAY_017:接收支付系统ciphertext={}",request.getCiphertext());
                logger.info("ICBC_UNIONPAY_017:接收支付系统transferKey={}",request.getTransferKey());
                //String ciphertext = URLDecoder.decode(request.getCiphertext(),"utf-8") ;
                String ciphertext = request.getCiphertext();
                logger.info("ICBC_UNIONPAY_017接收支付系统ciphertext解码过后={}",request.getCiphertext());
                String transferKey = request.getTransferKey();
                String decode = DESCoderUtil.decode(transferKey, ciphertext);
                String openId = request.getOpenId();
                Integer payType = request.getPayType();
                request = com.alibaba.fastjson.JSON.parseObject(decode, TradeCreateRequest.class);
                //重新设置openId以及payType
                request.setOpenId(openId);
                request.setPayType(payType);
                //017订单号由党费生成
                order.setTradeNo(request.getTradeNo());
                logger.info("ICBC_UNIONPAY_017的request={}",request);
                //查询订单状态
                CollectionDealerTradeEntity oriOrder = this.collectionDealerTradeDao.findByTradeNo(request.getTradeNo());
                //订单已经存在
                if(null!=oriOrder){
                    if(order.getStatus()==1) {
                        parameters.put("err-msg", "订单号已经完成支付");
                        resp.setData(parameters);
                    }else {
                        Jedis jedis  = this.redisPool.getResource();
                        String redisResult = jedis.get(order.getTradeNo());
                        Map mapResult = JSON.parseObject(redisResult, HashMap.class);
                        resp.setData(mapResult);
                    }
                    return;
                }
            } catch (Exception exception) {
                logger.info("ICBC_UNIONPAY_017:017参数解密失败", exception);
                parameters.put("err-msg", "参数解密失败");
                resp.setData(parameters);
                return;
            }
        }else {
            order.setTradeNo(tradeNoGenerater.getNewTradeNo(TradeTypeCode.COLLECTION_H5PAY_ICBC_UNION));
        }
        //payType=1 或者为空 默认是微信支付
        if(ObjectUtil.isNull(request.getPayType())){
            request.setPayType(1);
        }
        if(request.getPayType()!=1&&request.getPayType()!=2){
            throw new AssetServiceException(ErrorCode.INVALID_PARAMETER, "PayType参数异常！");
        }
        Long price = Long.valueOf(request.getAmount());
        order.setAmount(price);
        order.setBody(request.getBody());
        order.setSubject(request.getBody());
        order.setDealerId(Integer.valueOf(request.getDealerId()));
        order.setOpenId(request.getUserId());
        order.setChannel("工行union支付"+request.getPayType());
        order.setChannelCode(request.getChannel());
        order.setCreateTime(new Date());
        order.setStatus(TradeStatus.WAIT_PAY);
        order.setStatusDes("待支付");
        order.setUserId(request.getUserId());
        order.setBody(request.getBody());
        order.setNotifyUrl(request.getNotifyUrl());
        order.setVersion(0L);
        this.collectionDealerTradeDao.create(order);

        String merId = config.get("mer_id");
        String appId = config.get("app_id");
        String rsaPrivateKey = config.get("rsa_private_key");
        //网关公钥
        String gateWayPublicKey = config.get("icbc_pub_key");
        String tradeUrl = config.get("tradeUrl");
        String protocolNo=config.get("protocol_number");
        DefaultIcbcClient client = new DefaultIcbcClient(appId, IcbcConstants.SIGN_TYPE_RSA2, rsaPrivateKey, gateWayPublicKey);
        CardbusinessAggregatepayB2cOnlineConsumepurchaseRequestV1 ICBCRequest = new CardbusinessAggregatepayB2cOnlineConsumepurchaseRequestV1();
        ICBCRequest.setServiceUrl(tradeUrl);
        CardbusinessAggregatepayB2cOnlineConsumepurchaseRequestV1.CardbusinessAggregatepayB2cOnlineConsumepurchaseRequestV1Biz bizContent = new CardbusinessAggregatepayB2cOnlineConsumepurchaseRequestV1.CardbusinessAggregatepayB2cOnlineConsumepurchaseRequestV1Biz();
        bizContent.setMer_id(merId);
        bizContent.setOut_trade_no(order.getTradeNo());
        if( request.getPayType()==1){
            bizContent.setPay_mode("9");
            bizContent.setAccess_type("7");
            //微信公众号的appid
            bizContent.setShop_appid( config.get("wx_app_id"));
            bizContent.setOpen_id(request.getOpenId());
        }else {
            //支付宝生活号
            bizContent.setPay_mode("10");
            bizContent.setAccess_type("8");
            bizContent.setUnion_id(request.getOpenId());
        }
        bizContent.setMer_prtcl_no(protocolNo);
        bizContent.setOrig_date_time(DateTimeUtil.formatICBCH5(new Date()));
        bizContent.setDecive_info("013467007045764");
        bizContent.setBody(order.getBody());
        bizContent.setFee_type("001");
        bizContent.setIcbc_appid(appId);
        //支付成功过后回调地址
        bizContent.setMer_url(config.get("notifyUrl"));
        bizContent.setSpbill_create_ip( "***************" );
        bizContent.setTotal_fee(""+order.getAmount());
        bizContent.setNotify_type("HS");
        //银行只向商户发送交易成功的通知信息
        bizContent.setResult_type("1");
        //设置内容
        ICBCRequest.setBizContent(bizContent);
        logger.info("bizContent信息={}",JSON.toJSONString(ICBCRequest));
        try {
            CardbusinessAggregatepayB2cOnlineConsumepurchaseResponseV1 response
                    = client.execute(ICBCRequest, System.currentTimeMillis() + "");
            logger.info("response信息={}",JSON.toJSONString(response));
            if (ObjectUtil.isNull(request.getPayType())||response.getReturnCode() == 0) {
                Map<String, Object> parameters = new HashMap<>();
                parameters.put("tradeNo", order.getTradeNo());
                if(request.getPayType()==1) {
                    //解析微信参数
                    String wx_data_package = response.getWx_data_package();
                    Map map = JSON.parseObject(wx_data_package, Map.class);
                    parameters.put("timeStamp", map.get("timestamp"));
                    parameters.put("package", map.get("package"));
                    parameters.put("paySign", map.get("sign"));
                    parameters.put("appId", map.get("appid"));
                    parameters.put("signType", map.get("signType"));
                    parameters.put("nonceStr", map.get("noncestr"));
                }else {
                    String zfbDataPackage = response.getZfb_data_package();
                    //支付宝唤醒的参数
                    parameters.put("zfbDataPackage",zfbDataPackage);
                }
                resp.setData(parameters);
                //如果为017进来 这里党费自己生成订单 所以添加缓存
                if(request.getChannel().startsWith("ICBC_UNIONPAY_017")) {
                    Jedis jedis  = this.redisPool.getResource();
                    jedis.setex( order.getTradeNo(), 1200, JSON.toJSONString(parameters));
                }
            } else {
                throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "下单失败!");
            }
        } catch (Exception exception) {
            logger.error("执行createResponseCBCH5---发生异常", exception);
        }
    }

    public static void main(String[] args) {
        System.out.println("2020040322001446311434026197".length());
    }

    /**
     * B2C线上消费查询接口
     * @param oriOrder
     * @param config
     */
    public void receivePayResultNotifyICBCUNIONPAY(CollectionDealerTradeEntity oriOrder, Map<String, String> config) {
        String merId = config.get("mer_id");
        String appId = config.get("app_id");
        String rsaPrivateKey = config.get("rsa_private_key");
        String gateWayPubKey = config.get("APIGW_PUBLIC_KEY");
        String tradeQueryUrl = config.get("tradeQueryUrl");


        DefaultIcbcClient client = new DefaultIcbcClient(appId, IcbcConstants.SIGN_TYPE_RSA2, rsaPrivateKey, gateWayPubKey);
        CardbusinessAggregatepayB2cOnlineOrderqryRequestV1 ICBCRequest = new CardbusinessAggregatepayB2cOnlineOrderqryRequestV1();
        ICBCRequest.setServiceUrl(tradeQueryUrl);
        CardbusinessAggregatepayB2cOnlineOrderqryRequestV1.CardbusinessAggregatepayB2cOnlineOrderqryRequestV1Biz bizContent = new CardbusinessAggregatepayB2cOnlineOrderqryRequestV1.CardbusinessAggregatepayB2cOnlineOrderqryRequestV1Biz();
        bizContent.setMer_id(merId);
        bizContent.setOut_trade_no(oriOrder.getTradeNo());
        bizContent.setDeal_flag("0");
        bizContent.setIcbc_appid(appId);
        ICBCRequest.setBizContent(bizContent);
        try {
            CardbusinessAggregatepayB2cOnlineOrderqryResponseV1 response =
                    client.execute(ICBCRequest, System.currentTimeMillis() + "");
            if( response.getReturnCode() == 0 && response.getPay_status().equals("0")){
                //更新支付状态
                oriOrder.setPayTime(new Date());
                oriOrder.setStatus(TradeStatus.SUCCESS);
                oriOrder.setStatusDes("已支付");
                collectionDealerTradeDao.update(oriOrder);

                Map<String, String> notifyParams = new HashMap<>();
                notifyParams.put("tradeNo", oriOrder.getTradeNo());
                notifyParams.put("outTranNumber", oriOrder.getOutTradeNo());
                notifyParams.put("createTime", DateTimeUtil.format(oriOrder.getCreateTime()));
                notifyParams.put("payTime", DateTimeUtil.format(oriOrder.getPayTime()));
                notifyParams.put("status", oriOrder.getStatus() + "");
                notifyParams.put("statusDes", oriOrder.getStatusDes() + "");
                notifyParams.put("amount", oriOrder.getAmount().toString());
                notifyParams.put("openid", oriOrder.getOpenId());
                notifyParams.put("channel", oriOrder.getChannel());
                notifyService.submit(oriOrder.getNotifyUrl(), notifyParams);
                System.out.println(JSON.toJSONString(notifyParams));
                logger.info("receivePayResultNotifyICBCUNIONPAY处理完成!tradeNo={}", oriOrder.getTradeNo());
            }
        } catch (Exception ex) {
            logger.info("ICBCBankService.receivePayResultNotifyICBCUNIONPAY 异常", ex);
        }
    }

    /**
     * 聚富通支付
     * @param resp
     * @param order
     * @param config
     */
    public void createResponseJFTPAY(TradeCreateResponse resp, TradeCreateRequest requestInfo,
                                     CollectionDealerTradeEntity order, Map<String, String> config,
                                     HttpServletRequest req) {
        try {
            String APP_ID = config.get("app_id");
            String MY_PRIVATE_KEY =config.get("rsa_private_key");
            UiIcbcClient client = new UiIcbcClient(APP_ID,"RSA2", MY_PRIVATE_KEY, IcbcConstants.CHARSET_UTF8);
            JftUiPayH5RequestV3 request = new JftUiPayH5RequestV3();
            request.setServiceUrl( config.get("tradeUrl"));
            JftUiPayH5RequestV3.JftUiPayH5RequestV3Biz bizContent = new JftUiPayH5RequestV3.JftUiPayH5RequestV3Biz();
            bizContent.setAppId(APP_ID);//平台商户标识：提交支付请求的平台商户编号
            bizContent.setOutVendorId(config.get("mer_id"));//子商户标识：提交支付请求的子商户编号
            bizContent.setOutUserId(requestInfo.getUserId());//用户标识
            bizContent.setNotifyUrl(config.get("notifyUrl"));//商户通知URL：商户接受支付成功通知消息URL
            //bizContent.setJumpUrl("www.xxx.com"); //商户跳转URL:支付完成后回调地址（跳转商户页面），不支持传参
            //平台对接方ID：平台商户订单ID，确保唯一
            String tradeNo = tradeNoGenerater.getNewTradeNo(TradeTypeCode.COLLECTION_H5PAY_ICBC_JFT);
            bizContent.setOutOrderId(tradeNo);
            bizContent.setGoodsName(requestInfo.getSubject());//商品名称（长度单位：字节）
            bizContent.setPayType("01");//支付类型‐单笔支付
            //如果支持数字货币
            bizContent.setMac(requestInfo.getMac());//手机Mac:支付提交用户手机Mac地址
            bizContent.setImei(requestInfo.getImei());
            bizContent.setNotifyUrl(config.get("notifyUrl"));
            //支付成功以后回退地址
            bizContent.setJumpUrl(config.get("returnUrl"));
            bizContent.setTrxIp(WebUtil.getIpAddr(req));//交易IP：支付提交用户IP地址
            bizContent.setTrxChannel("03");//交易渠道:固定取03
            String payAmount = NumUtil.amountFormat(Integer.parseInt(requestInfo.getAmount()), 100);
            bizContent.setPayAmount(payAmount);//支付金额(元)整数长度不能超过8位，小数位不能超过2位
            bizContent.setVarNote(requestInfo.getSubject());//商户备注
            request.setBizContent(bizContent);


            order.setAmount(Long.valueOf(requestInfo.getAmount()));
            order.setBody(requestInfo.getBody());
            order.setTradeNo(tradeNo);
            order.setSubject(requestInfo.getBody());
            order.setDealerId(Integer.valueOf(requestInfo.getDealerId()));
            order.setOpenId(requestInfo.getUserId());
            order.setChannel("工行union支付"+requestInfo.getPayType());
            order.setChannelCode(requestInfo.getChannel());
            order.setCreateTime(new Date());
            order.setStatus(TradeStatus.WAIT_PAY);
            order.setStatusDes("待支付");
            order.setUserId(requestInfo.getUserId());
            order.setBody(requestInfo.getBody());
            order.setNotifyUrl(requestInfo.getNotifyUrl());
            order.setVersion(0L);
            this.collectionDealerTradeDao.create(order);

            String form = "";
            Jedis jedis = null;
            try {
                jedis = redisPool.getResource();
                form = client.buildPostForm(request);
                logger.info("ICBC respBody=\n{}", form);
                jedis.setex(RedisKey.COMPANY_RECHARGE_ALIPAY_FORM + order.getTradeNo(),
                        1200, form);
                Map<String, Object> parameters = new HashMap<>();
                parameters.put("tradeNo", order.getTradeNo());
                resp.setData(parameters);
            } catch (Exception exception){
                logger.error("执行createResponseCBC---发生异常-{}",exception.getMessage());
            } finally {
                if (jedis != null) {
                    redisPool.returnResource(jedis);
                }
            }
        } catch (Exception ex) {
            logger.error("createResponseJFTPAY发生异常",ex);
        }
    }


    /**
     * 主动查询工行聚富通
     * @param oriOrder
     * @param config
     */
    public void receivePayResultNotifyICBCJFT(CollectionDealerTradeEntity oriOrder, Map<String, String> config) {
        try {
            String AES_Key = config.get("encrypt_key");
            String APP_ID = config.get("app_id");
            String MY_PRIVATE_KEY = config.get("rsa_private_key");
            String APIGW_PUBLIC_KEY = config.get("icbc_pub_key");
            String tradeQueryUrl = config.get("tradeQueryUrl");

            DefaultIcbcClient client = new DefaultIcbcClient(APP_ID, "RSA2", MY_PRIVATE_KEY, "UTF-8", "json", APIGW_PUBLIC_KEY, "AES", AES_Key, "", "");
            QueryOrderRequestV1 request = new QueryOrderRequestV1();
            request.setServiceUrl(tradeQueryUrl);
            QueryOrderRequestV1.QueryOrderRequestV1Biz bizContent = new QueryOrderRequestV1.QueryOrderRequestV1Biz();
            bizContent.setAppId(APP_ID);//平台商户标识
            bizContent.setOutVendorId(config.get("mer_id"));//子商户标识
            bizContent.setOutOrderId(oriOrder.getTradeNo());//平台订单ID
            request.setBizContent(bizContent);
            QueryOrderResponseV1 response = client.execute(request, ""+System.currentTimeMillis());
            //开始处理业务
            if (response.isSuccess()&&"02".equals(response.getOrderStatus())) {
                //更新支付状态
                oriOrder.setPayTime(new Date());
                oriOrder.setStatus(TradeStatus.SUCCESS);
                oriOrder.setStatusDes("已支付");
                collectionDealerTradeDao.update(oriOrder);

                Map<String, String> notifyParams = new HashMap<>();
                notifyParams.put("tradeNo", oriOrder.getTradeNo());
                notifyParams.put("outTranNumber", oriOrder.getOutTradeNo());
                notifyParams.put("createTime", DateTimeUtil.format(oriOrder.getCreateTime()));
                notifyParams.put("payTime", DateTimeUtil.format(oriOrder.getPayTime()));
                notifyParams.put("status", oriOrder.getStatus() + "");
                notifyParams.put("statusDes", oriOrder.getStatusDes() + "");
                notifyParams.put("amount", oriOrder.getAmount().toString());
                notifyParams.put("openid", oriOrder.getOpenId());
                notifyParams.put("channel", oriOrder.getChannel());
                notifyService.submit(oriOrder.getNotifyUrl(), notifyParams);
                System.out.println(JSON.toJSONString(notifyParams));
                logger.info("receivePayResultNotifyICBCJFT处理完成!tradeNo={}", oriOrder.getTradeNo());
            }
        } catch (Exception e) {
           logger.error("receivePayResultNotifyICBCJFT发生异常",e);
        }


    }
}

