package com.goodsogood.collectionpay.service;

import java.util.HashMap;
import java.util.Map;

import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.goodsogood.collectionpay.consts.LogConfiger;
import com.goodsogood.collectionpay.model.response.ProxyTaskCreateResp;
import com.goodsogood.collectionpay.util.HttpClientUtil;

//@Service("proxyTaskService")
public class ProxyTaskService {

	private static final Logger logger = LoggerFactory.getLogger(LogConfiger.APP);

	@Value("${gateway.service.proxyTask.url}")
	private String serviceUrl;
	

	/**
	 * @param serviceUrl the serviceUrl to set
	 */
	public void setServiceUrl(String serviceUrl) {
		this.serviceUrl = serviceUrl;
	}

	private static String METHOD_NEWTASK = "/newtask";

	private static CloseableHttpClient httpClient;

	private static final int MAX_TOTAL = 200;
	private static final int MAX_PERROUTE = 1000;

	static {
		PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
		cm.setMaxTotal(MAX_TOTAL);
		cm.setDefaultMaxPerRoute(MAX_PERROUTE);
		httpClient = HttpClients.custom().setConnectionManager(cm).build();
	}

	public ProxyTaskCreateResp createProxyTask(String destaddr, String method, Map<String, String> headers,
			Map<String, String> querys, String body, boolean bodybase64, int retrytimes) {
		HttpPost httpPost =null ;
		try {
			Map<String, Object> paraMap = new HashMap<>();
			paraMap.put("destaddr", destaddr);
			paraMap.put("method", method);
			paraMap.put("headers", headers);
			paraMap.put("querys", querys);
			paraMap.put("body", body);
			paraMap.put("bodybase64", bodybase64);
			paraMap.put("retrytimes", retrytimes);
			httpPost = new HttpPost(serviceUrl + METHOD_NEWTASK);
			StringEntity entity = new StringEntity(JSON.toJSONString(paraMap), ContentType.APPLICATION_JSON);
			httpPost.setEntity(entity);
			HttpResponse response = httpClient.execute(httpPost);
			if (response != null) {
				HttpEntity resEntity = response.getEntity();
				if (resEntity != null) {
					String respBody = EntityUtils.toString(resEntity, Consts.UTF_8);
					return JSON.parseObject(respBody, ProxyTaskCreateResp.class);
				}
			}
		} catch (Exception ex) {
			logger.error(" createProxyTask error :{}", ex.getMessage());
		}finally {
			if(httpPost!=null)
				httpPost.reset();
		}
		return null; //http://172.16.252.219:20002/checktask?id=
	}
	
	
	public String checktask(String id){
		Map<String,String> param = new HashMap<>();
		param.put("id", id);
		return HttpClientUtil.sendGet(serviceUrl +"/checktask" , param);
	}

}
