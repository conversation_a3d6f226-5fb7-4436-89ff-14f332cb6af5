package com.goodsogood.collectionpay.service;

import com.alibaba.fastjson.JSON;
import com.goodsogood.collectionpay.consts.LogConfiger;
import com.goodsogood.collectionpay.entity.CollectionDealerTradeEntity;
import com.goodsogood.collectionpay.model.OrderVo;
import com.goodsogood.collectionpay.util.DESCoderUtil;
import com.goodsogood.collectionpay.util.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class FileServices {

    private static final Logger logger = LoggerFactory.getLogger(LogConfiger.APP);

    @Autowired
    private DESCoderUtil desCoderUtil;

    @Autowired
    private FileUtils fileUtils;

    /**
     * 批量生成ftp文件
     */
    @Async("fileWriteExecutor")
    public void  createFtpFile(  List<CollectionDealerTradeEntity> listOrders   ){
        logger.info("开始生成文件,listOrders={}",listOrders);
        List<OrderVo> listOrder = new ArrayList<>();
        //如果是单个订单
        listOrders.forEach(item->{
            OrderVo orderVo = new OrderVo();
            orderVo.setTradeNo(item.getTradeNo());
            orderVo.setAmount(item.getAmount());
            orderVo.setStatus(item.getStatus());
            orderVo.setUserId(item.getUserId());
            orderVo.setPaySuccessTime(item.getPayTime());
            orderVo.setCreateTime(item.getCreateTime());
            listOrder.add(orderVo);
        });
        logger.info("开始生成文件2");
        String jsonStr = JSON.toJSONString(listOrder);
        try {
            Map<String, String> encrypt = desCoderUtil.encrypt(jsonStr);
            if(listOrders.size()==1){
                logger.info("开始生成文件3");
                CollectionDealerTradeEntity collectionDealerTrade = listOrders.get(0);
                fileUtils.createFileContent(collectionDealerTrade.getUserId(),collectionDealerTrade.getTradeNo()
                        ,JSON.toJSONString(encrypt));
            }else {
                //fileUtils.createBatchFileContent(JSON.toJSONString(encrypt));
            }
        }catch (Exception ex){
            logger.error("createFtpFile失败",ex,JSON.toJSONString(listOrders));
        }
    }
}
