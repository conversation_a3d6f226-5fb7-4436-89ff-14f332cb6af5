package com.goodsogood.collectionpay.service;

import com.alibaba.fastjson.JSON;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.internal.util.StringUtils;
import com.alipay.api.request.*;
import com.alipay.api.response.AlipayOpenAuthTokenAppResponse;
import com.alipay.api.response.AlipayTradeAppPayResponse;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.goodsogood.collectionpay.consts.*;
import com.goodsogood.collectionpay.dao.AssetDepositAlipayConfDao;
import com.goodsogood.collectionpay.dao.CollectionDealerAccountDao;
import com.goodsogood.collectionpay.dao.CollectionDealerAccountLogDao;
import com.goodsogood.collectionpay.dao.CollectionDealerTradeDao;
import com.goodsogood.collectionpay.entity.AssetDepositAlipayConfEntity;
import com.goodsogood.collectionpay.entity.CollectionDealerAccountEntity;
import com.goodsogood.collectionpay.entity.CollectionDealerAccountLogEntity;
import com.goodsogood.collectionpay.entity.CollectionDealerTradeEntity;
import com.goodsogood.collectionpay.exception.AssetServiceException;
import com.goodsogood.collectionpay.model.request.TradeCreateRequest;
import com.goodsogood.collectionpay.model.response.TradeCreateResponse;
import com.goodsogood.collectionpay.redis.RedisPool;
import com.goodsogood.collectionpay.util.DateTimeUtil;
import com.goodsogood.collectionpay.util.UUIDUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.Jedis;

import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service("alipayService")
public class AlipayService {
	
	static final Logger logger = LoggerFactory.getLogger(LogConfiger.APP);
	@Autowired
	private CollectionDealerTradeDao collectionDealerTradeDao ;
	
	@Autowired
	private AssetDepositChannelConfService assetDepositChannelConfService ;
	
	@Autowired
	private CollectionDealerAccountDao collectionDealerAccountDao ;
	
	@Autowired
	private CollectionDealerAccountLogDao collectionDealerAccountLogDao ;

    @Autowired
    private AssetDepositAlipayConfDao assetDepositAlipayConfDao;
	
	@Autowired
	private NotifyService notifyService ;

    @Autowired
    private RedisPool redisPool;

    @Autowired
    private TradeNoGenerater tradeNoGenerater;
	
	public AlipayTradeQueryResponse queryOrder(String orderNo,String channelCode) throws AlipayApiException {
		Map<String, String> config = assetDepositChannelConfService.findOptionsByChannel(channelCode);
		String URL = "https://openapi.alipay.com/gateway.do";
		String APP_ID = config.get("appId").trim();
		String APP_PRIVATE_KEY = config.get("privateKey").trim();
		String FORMAT = "json";
		String CHARSET = "UTF-8";
        String ALIPAY_PUBLIC_KEY = config.get("aliPublicKey")==null?"":config.get("aliPublicKey").trim();
		String SIGN_TYPE = config.get("signType").trim();
        //如果用RSA签名的话 ALIPAY_PUBLIC_KEY 就用 publicKey
		if(SIGN_TYPE.equals("RSA")){
            ALIPAY_PUBLIC_KEY = config.get("publicKey").trim();
        }
		AlipayClient alipayClient = new DefaultAlipayClient(URL, APP_ID, APP_PRIVATE_KEY, FORMAT, CHARSET,
				ALIPAY_PUBLIC_KEY, SIGN_TYPE);

		AlipayTradeQueryRequest alipayRequest = new AlipayTradeQueryRequest();// 创建API对应的request

		Map<String, Object> signParams = new HashMap<>();
		signParams.put("out_trade_no", orderNo);
		alipayRequest.setBizContent(JSON.toJSONString(signParams));
		return alipayClient.execute(alipayRequest);
	}

    public AlipayTradeQueryResponse queryOrderISV(String orderNo,String channelCode,String appAuthToken) throws AlipayApiException {
        Map<String, String> config = assetDepositChannelConfService.findOptionsByChannel(channelCode);
        String URL = "https://openapi.alipay.com/gateway.do";
        String APP_ID = config.get("appId").trim();
        String APP_PRIVATE_KEY = config.get("privateKey").trim();
        String FORMAT = "json";
        String CHARSET = "UTF-8";
        String ALIPAY_PUBLIC_KEY = config.get("publicKey").trim();
        String SIGN_TYPE = config.get("signType").trim();
        //如果用RSA签名的话 ALIPAY_PUBLIC_KEY 就用 publicKey
        if(SIGN_TYPE.equals("RSA")){
            ALIPAY_PUBLIC_KEY = config.get("publicKey").trim();
        }
        AlipayClient alipayClient = new DefaultAlipayClient(URL, APP_ID, APP_PRIVATE_KEY, FORMAT, CHARSET,
                ALIPAY_PUBLIC_KEY, SIGN_TYPE);

        AlipayTradeQueryRequest alipayRequest = new AlipayTradeQueryRequest();// 创建API对应的request
        alipayRequest.putOtherTextParam("app_auth_token", appAuthToken);
        Map<String, Object> signParams = new HashMap<>();
        signParams.put("out_trade_no", orderNo);
        alipayRequest.setBizContent(JSON.toJSONString(signParams));
        return alipayClient.execute(alipayRequest);
    }



    public TradeCreateResponse createResponse(TradeCreateResponse resp,
                                TradeCreateRequest request,
                                CollectionDealerTradeEntity order,
                                Map<String, String> config){
        int price = Integer.valueOf(request.getAmount());
        order.setAmount((long) price);
        order.setBody(request.getSubject());
        order.setSubject(request.getSubject());
        order.setDealerId(Integer.valueOf(request.getDealerId()));
        order.setOpenId(request.getOpenId());
        order.setChannel("支付宝");
        order.setChannelCode(request.getChannel());
        order.setCreateTime(new Date());
        order.setStatus(TradeStatus.WAIT_PAY);
        order.setStatusDes("待支付");
        order.setTradeNo(tradeNoGenerater.getNewTradeNo(TradeTypeCode.COLLECTION_WX));
        order.setUserId(request.getUserId());
        order.setBody(request.getBody());
        order.setNotifyUrl(request.getNotifyUrl());
        order.setVersion(0L);
        this.collectionDealerTradeDao.create(order);
        try {
            float yuan = price / 100f;
            try {
                String URL="https://openapi.alipay.com/gateway.do";
                String APP_ID=config.get("appId").trim();
                String APP_PRIVATE_KEY=config.get("privateKey").trim();
                String FORMAT="json";
                String CHARSET="UTF-8";
                String ALIPAY_PUBLIC_KEY=config.get("publicKey").trim();
                String SIGN_TYPE=config.get("signType").trim();
                String redirectUrl=config.get("pageUrl").trim();

                AlipayClient alipayClient =new DefaultAlipayClient(URL,APP_ID,APP_PRIVATE_KEY,
                        FORMAT,CHARSET,ALIPAY_PUBLIC_KEY,SIGN_TYPE);
                AlipayTradePagePayRequest alipayRequest = new AlipayTradePagePayRequest();//创建API对应的request
                alipayRequest.setReturnUrl(URLDecoder.decode(request.getReturnUrl(),"utf-8"));
                alipayRequest.setNotifyUrl(config.get("notifyUrl"));//在公共参数中设置回跳和通知地址

                Map<String,Object> signParams =new HashMap<>();
                signParams.put("out_trade_no", order.getTradeNo());
                signParams.put("product_code", "FAST_INSTANT_TRADE_PAY");
                signParams.put("subject", request.getSubject().length()>256? request.getSubject().
                        substring(0,256) :request.getSubject() );
                signParams.put("body", request.getBody().length()>128? request.getBody().
                        substring(0,128) :request.getBody() );
                signParams.put("total_amount", yuan);
                alipayRequest.setBizContent(JSON.toJSONString(signParams));

                String form="";
                Jedis jedis = null;
                try {
                    jedis = redisPool.getResource() ;
                    form = alipayClient.pageExecute(alipayRequest).getBody(); //调用SDK生成表单
                    jedis.setex(RedisKey.COMPANY_RECHARGE_ALIPAY_FORM + order.getTradeNo(),
                            1200, form);
                } finally {
                    if(jedis!=null){
                        redisPool.returnResource(jedis);
                    }
                }
                Map<String, Object> respParameters = new HashMap<String, Object>();
                respParameters.put("html", URLEncoder.encode(form, "UTF-8"));
                respParameters.put("tradeNo",order.getTradeNo());
                respParameters.put("pageUrl", redirectUrl+order.getTradeNo());
                resp.setData(respParameters);
                return resp;
            } catch (AlipayApiException e) {
                order.setStatus(TradeStatus.FAIL);
                order.setStatusDes("支付失败");
                logger.error("error!",e);
                resp.setCode(Integer.valueOf(ErrorCode.BUSINESS_ERROR));
                resp.setMessage("支付异常(082)请稍后再试");
                return resp ;
            }
            catch (Exception e) {
                logger.error("error!",e);
                throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "支付数据生成失败");
            }
        } catch (Exception e) {
            logger.error("error!",e);
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "支付数据返回失败");
        }

    }


    public TradeCreateResponse createResponseWeb(TradeCreateResponse resp,
                                              TradeCreateRequest request,
                                              CollectionDealerTradeEntity order,
                                              Map<String, String> config){
        int price = Integer.valueOf(request.getAmount());
        order.setAmount((long) price);
        order.setBody(request.getSubject());
        order.setSubject(request.getSubject());
        order.setDealerId(Integer.valueOf(request.getDealerId()));
        order.setOpenId(request.getOpenId());
        order.setChannel("支付宝");
        order.setChannelCode(request.getChannel());
        order.setCreateTime(new Date());
        order.setStatus(TradeStatus.WAIT_PAY);
        order.setStatusDes("待支付");
        order.setTradeNo(tradeNoGenerater.getNewTradeNo(TradeTypeCode.COLLECTION_WX));
        Long orgId = request.getOrgId();
        order.setUserId(""+request.getUserId());
        order.setOrgId(orgId);
        order.setBody(request.getBody());
        order.setNotifyUrl(request.getNotifyUrl());
        order.setVersion(0L);

        this.collectionDealerTradeDao.create(order);
        try {
            float yuan = price / 100f;
            try {
                String URL="https://openapi.alipay.com/gateway.do";
                String APP_ID=config.get("appId").trim();
                String APP_PRIVATE_KEY=config.get("privateKey").trim();
                String FORMAT="json";
                String CHARSET="UTF-8";
                String ALIPAY_PUBLIC_KEY=config.get("publicKey").trim();
                String SIGN_TYPE=config.get("signType").trim();
                String redirectUrl=config.get("pageUrl").trim();

                AlipayClient alipayClient =new DefaultAlipayClient(URL,APP_ID,APP_PRIVATE_KEY,
                        FORMAT,CHARSET,ALIPAY_PUBLIC_KEY,SIGN_TYPE);
                AlipayTradeWapPayRequest alipayRequest = new AlipayTradeWapPayRequest();//创建API对应的request
                logger.info("前端跳转地址={}",request.getReturnUrl());
                alipayRequest.setReturnUrl(URLDecoder.decode(request.getReturnUrl(),"utf-8"));
                if(null!=orgId){
                    AssetDepositAlipayConfEntity  assetDepositAlipayConfEntity= assetDepositAlipayConfDao.findByOrgId(orgId);
                    if(null==assetDepositAlipayConfEntity){
                        throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "无法根据组织id查询授权信息");
                    }
                    alipayRequest.putOtherTextParam("app_auth_token",assetDepositAlipayConfEntity.getAppAuthToken());
                }
                alipayRequest.setNotifyUrl(config.get("notifyUrl"));//在公共参数中设置回跳和通知地址

                Map<String,Object> signParams =new HashMap<>();
                signParams.put("out_trade_no", order.getTradeNo());
                signParams.put("product_code", "FAST_INSTANT_TRADE_PAY");
                signParams.put("subject", request.getSubject().length()>256? request.getSubject().
                        substring(0,256) :request.getSubject() );
                signParams.put("body", request.getBody().length()>128? request.getBody().
                        substring(0,128) :request.getBody() );
                signParams.put("total_amount", yuan);
                //订单失效是45分钟
                signParams.put("timeout_express", "40m");
                alipayRequest.setBizContent(JSON.toJSONString(signParams));

                String form="";
                Jedis jedis = null;
                try {
                    jedis = redisPool.getResource() ;
                    //调用SDK生成表单
                    form = alipayClient.pageExecute(alipayRequest).getBody();
                    jedis.setex(RedisKey.COMPANY_RECHARGE_ALIPAY_FORM + order.getTradeNo(),
                            1200, form);
                } finally {
                    if(jedis!=null){
                        redisPool.returnResource(jedis);
                    }
                }
                Map<String, Object> respParameters = new HashMap<String, Object>();
                respParameters.put("tradeNo",order.getTradeNo());
                respParameters.put("pageUrl", redirectUrl+order.getTradeNo());
                resp.setData(respParameters);
                return resp;
            } catch (AlipayApiException e) {
                order.setStatus(TradeStatus.FAIL);
                order.setStatusDes("支付失败");
                logger.error("error!",e);
                resp.setCode(Integer.valueOf(ErrorCode.BUSINESS_ERROR));
                resp.setMessage("支付异常(082)请稍后再试");
                return resp ;
            }
            catch (Exception e) {
                logger.error("error!",e);
                throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "支付数据生成失败");
            }
        } catch (Exception e) {
            logger.error("error!",e);
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "支付数据返回失败");
        }
    }

    /**
     * 换取token
     */
    @Transactional
    public Integer authToken(String code,
                          Map<String, String> config) {
        String URL="https://openapi.alipay.com/gateway.do";
        String APP_ID=config.get("appId").trim();
        String APP_PRIVATE_KEY=config.get("privateKey").trim();
        String FORMAT="json";
        String CHARSET="UTF-8";
        String ALIPAY_PUBLIC_KEY=config.get("publicKey").trim();
        String SIGN_TYPE=config.get("signType").trim();
        AlipayClient alipayClient = new DefaultAlipayClient(URL,APP_ID,APP_PRIVATE_KEY,
                FORMAT,CHARSET,ALIPAY_PUBLIC_KEY,SIGN_TYPE);
        AlipayOpenAuthTokenAppRequest  alipayRequest = new AlipayOpenAuthTokenAppRequest();//创建API对应的request
        Map<String,Object> signParams =new HashMap<>();
        signParams.put("grant_type", "authorization_code");
        signParams.put("code", code);
        alipayRequest.setBizContent(JSON.toJSONString(signParams));
        try {
            AlipayOpenAuthTokenAppResponse response = alipayClient.execute(alipayRequest);
            logger.info("调用授权返回参数:{}",response);
            //授权成功
            if(response.isSuccess()){
                String body = response.getBody();
                String appRefreshToken = JSON.parseObject(body).getJSONObject("alipay_open_auth_token_app_response")
                        .getJSONArray("tokens").getJSONObject(0).get("app_refresh_token").toString();
                System.out.println(body);
                signParams.put("grant_type", "refresh_token");
                signParams.put("refresh_token", appRefreshToken);
                alipayRequest.setBizContent(JSON.toJSONString(signParams));
                AlipayOpenAuthTokenAppResponse responseAgain = alipayClient.execute(alipayRequest);
                if(responseAgain.isSuccess()) {
                    String authAppId = responseAgain.getAuthAppId();
                    String appAuthToken = responseAgain.getAppAuthToken();
                    logger.info("authAppId={}", authAppId);
                    logger.info("appAuthToken={}", appAuthToken);
                    AssetDepositAlipayConfEntity assetDepositAlipayConfEntity = assetDepositAlipayConfDao.findByAppId(authAppId);
                    //就更新token
                    if(null!=assetDepositAlipayConfEntity) {
                        assetDepositAlipayConfEntity.setAppAuthToken(appAuthToken);
                        assetDepositAlipayConfEntity.setUpdateTime(new Date());
                        this.assetDepositAlipayConfDao.update(assetDepositAlipayConfEntity);
                    }else {
                         assetDepositAlipayConfEntity = new AssetDepositAlipayConfEntity();
                         assetDepositAlipayConfEntity.setOrgId(-999L);
                         assetDepositAlipayConfEntity.setOrgName("未配置组织名称");
                         assetDepositAlipayConfEntity.setAppId(authAppId);
                         assetDepositAlipayConfEntity.setAppAuthToken(appAuthToken);
                         assetDepositAlipayConfEntity.setCreateTime(new Date());
                         assetDepositAlipayConfEntity.setAccountId(responseAgain.getUserId());
                         assetDepositAlipayConfDao.save(assetDepositAlipayConfEntity);
                    }
                }
                return 1;
            }
        }catch (Exception ex){
            logger.error("调用支付宝授权异常,异常信息",ex);
            return -1;
        }
        return -1;
    }

    public static void main(String[] args) {
        String body="{\"alipay_open_auth_token_app_response\":{\"code\":\"10000\",\"msg\":\"Success\",\"tokens\":[{\"app_auth_token\":\"202112BB5751f6d3d476434eaa5f6a8a86e64X13\",\"app_refresh_token\":\"202112BB43217b9a54074630a3796d946aa17X13\",\"auth_app_id\":\"****************\",\"expires_in\":********,\"re_expires_in\":********,\"user_id\":\"****************\"}]},\"sign\":\"OM/gcb/eJWVvlVC4BkcomcKY9R8K72f/wq6HmfhfQF9UFXE3xA8VkWpE8atrNhfH7CGD36osXqawffaVuU7vXsMomuzWzH/SNP7+MVaFJ+cKrVvzpLne2xyxyJseJ7mWZ1aXrpClCqp+3QiwZvQo8ov8ngfKs5wg53DRN+WuKyzMqjMd69/ubsqYDj4XQ0Zz7UtlH2B2QXgjx4EOZXM54dOME4xZrxDiXgA7Q6oLu5BZZ1BZc+vFZh0VP3dotfOQliScSlDKdmu3kW7aDg/8iQ+c45IIBIkFzLQguYDt78OADzWMvx8lV+vkVc8DjhzFCnxa3P+qUj4IujnFeCB9Cw==\"}";
        String app_refresh_token = JSON.parseObject(body).getJSONObject("alipay_open_auth_token_app_response")
                .getJSONArray("tokens").getJSONObject(0).get("app_refresh_token").toString();
        System.out.println(app_refresh_token);

    }


    public TradeCreateResponse createDingDingResponse(TradeCreateResponse resp,
                                                      TradeCreateRequest request,
                                                      CollectionDealerTradeEntity order,
                                                      Map<String, String> config) {
        int price = Integer.valueOf(request.getAmount());
        order.setAmount((long) price);
        order.setBody(request.getSubject());
        order.setSubject(request.getSubject());
        order.setDealerId(Integer.valueOf(request.getDealerId()));
        order.setOpenId(request.getOpenId());
        order.setChannel("支付宝");
        order.setChannelCode(request.getChannel());
        order.setCreateTime(new Date());
        order.setStatus(TradeStatus.WAIT_PAY);
        order.setStatusDes("待支付");
        order.setTradeNo(tradeNoGenerater.getNewTradeNo(TradeTypeCode.COLLECTION_WX));
        order.setUserId(request.getUserId());
        order.setBody(request.getBody());
        order.setNotifyUrl(request.getNotifyUrl());
        order.setVersion(0L);
        this.collectionDealerTradeDao.create(order);
        try {
            float yuan = price / 100f;
            String APP_ID=config.get("appId").trim();
            String METHOD="alipay.trade.app.pay";
            String APP_PRIVATE_KEY=config.get("privateKey").trim();
            String FORMAT="json";
            String CHARSET="UTF-8";
            String ALIPAY_PUBLIC_KEY=config.get("publicKey").trim();
            String SIGN_TYPE = config.get("signType");;
            String notifyUrl=config.get("notifyUrl");
            String timestamp = DateTimeUtil.format(new Date());
            Map<String,Object> signParams =new HashMap<>();
            signParams.put("out_trade_no", order.getTradeNo());
            signParams.put("product_code", "FAST_INSTANT_TRADE_PAY");
            signParams.put("subject", request.getSubject().length()>256? request.getSubject().
                    substring(0,256) :request.getSubject() );
            signParams.put("body", request.getBody().length()>128? request.getBody().
                    substring(0,128) :request.getBody() );
            signParams.put("total_amount", yuan);
            signParams.put("product_code", "QUICK_MSECURITY_PAY");
            signParams.put("notify_url", notifyUrl);
            String biz_content=JSON.toJSONString(signParams);

            String URL="https://openapi.alipay.com/gateway.do";
            //进行签名
            AlipayClient alipayClient =new DefaultAlipayClient(URL,APP_ID,APP_PRIVATE_KEY,
                    FORMAT,CHARSET,ALIPAY_PUBLIC_KEY,SIGN_TYPE);
            //创建API对应的request
            AlipayTradePayRequest alipayRequest = new AlipayTradePayRequest();
            alipayRequest.setReturnUrl(request.getReturnUrl());
            //在公共参数中设置回跳和通知地址
            alipayRequest.setNotifyUrl(notifyUrl);
            alipayRequest.setBizContent(JSON.toJSONString(signParams));
            //实例化具体API对应的request类,类名称和接口名称对应,当前调用接口名称：
            // alipay.open.public.template.message.industry.modify
            AlipayTradeAppPayRequest requestDingDing = new AlipayTradeAppPayRequest();
            requestDingDing.setBizContent(biz_content);
            //AlipayTradePayResponse response = alipayClient.execute(requestDingDing);
            AlipayTradeAppPayResponse  response = alipayClient.sdkExecute(requestDingDing);
            if(response.isSuccess()){
                Map<String, Object> respParameters = new HashMap<String, Object>();
                String info=response.getBody();
                respParameters.put("info", info);
                respParameters.put("tradeNo",order.getTradeNo());
                resp.setData(respParameters);
            }else {
                throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "支付宝签名失败！");
            }
            return resp;
        } catch (Exception e) {
            logger.error("error!",e);
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "支付数据返回失败");
        }
    }
	
	/**
	 * 回调处理
	 * @param params
	 * @return
	 * @throws AlipayApiException 
	 */
	@Transactional
	public String notifyHandle(Map<String, String> params) throws AlipayApiException {
		String out_trade_no = params.get("out_trade_no");

		CollectionDealerTradeEntity oriOrder = this.collectionDealerTradeDao.findByTradeNo(out_trade_no);
		if (oriOrder == null) {
			throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "未找到订单信息");
		}
		Map<String, String> config = assetDepositChannelConfService.
                findOptionsByChannel(oriOrder.getChannelCode());
		String URL = "https://openapi.alipay.com/gateway.do";
		String APP_ID = config.get("appId").trim();
		String APP_PRIVATE_KEY = config.get("privateKey").trim();
		String FORMAT = "json";
		String CHARSET = "UTF-8";
		//使用应用对应的支付的公钥 而非应用公钥
        String ALIPAY_PUBLIC_KEY = config.get("aliPublicKey") == null ? "" : config.get("aliPublicKey").trim();
		String SIGN_TYPE = config.get("signType").trim();
        //如果用RSA签名的话 ALIPAY_PUBLIC_KEY 就用 publicKey
		if(SIGN_TYPE.equals("RSA")){
            ALIPAY_PUBLIC_KEY = config.get("publicKey");
        }
        // 调用SDK验证签名
		boolean signVerified = AlipaySignature.rsaCheckV2(params,
                ALIPAY_PUBLIC_KEY,
                CHARSET, SIGN_TYPE);
		if (signVerified) {
			logger.info("验签成功");
			// TODO
			// 验签成功后，按照支付结果异步通知中的描述，对支付结果中的业务内容进行二次校验
			//，校验成功后在response中返回success并继续商户自身业务处理，校验失败返回failure
		} else {
			logger.info("验签失败");
			return "failure";
		}
		AlipayTradeQueryResponse aliQryResp = this.queryOrder(out_trade_no, oriOrder.getChannelCode());
		logger.info("notifyHandle  out_trade_no={},aliQryResp={}", out_trade_no, aliQryResp);

		if (aliQryResp.isSuccess() && aliQryResp.getTradeStatus().equals("TRADE_SUCCESS")) {
            handlerResult(oriOrder,aliQryResp);
			return "success";
		}
		return "failure";
	}

    /**
     * 回调处理
     * @param params
     * @return
     * @throws AlipayApiException
     */
    @Transactional
    public String notifyHandleH5(Map<String, String> params) throws AlipayApiException {
        String out_trade_no = params.get("out_trade_no");
        CollectionDealerTradeEntity oriOrder = this.collectionDealerTradeDao.findByTradeNo(out_trade_no);
        if (oriOrder == null) {
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "未找到订单信息");
        }
        Map<String, String> config = assetDepositChannelConfService.
                findOptionsByChannel(oriOrder.getChannelCode());
        String URL = "https://openapi.alipay.com/gateway.do";
        String APP_ID = config.get("appId").trim();
        String APP_PRIVATE_KEY = config.get("privateKey").trim();
        String ALIPAY_PUBLIC_KEY= config.get("publicKey").trim();
        String CHARSET = "UTF-8";
        AlipayClient alipayClient = new DefaultAlipayClient(URL,
                APP_ID, APP_PRIVATE_KEY, "json", CHARSET, ALIPAY_PUBLIC_KEY, "RSA2");
        AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
        request.setBizContent(JSON.toJSONString(params));
        //ISV代理商户调用需要传入app_auth_token
        AssetDepositAlipayConfEntity assetDepositAlipayConfEntity = assetDepositAlipayConfDao.findByOrgId(oriOrder.getOrgId());
        if(null!=oriOrder.getOrgId()){
            if(null==assetDepositAlipayConfEntity){
                throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "根据组织id无法查询配置信息");
            }
            request.putOtherTextParam("app_auth_token", assetDepositAlipayConfEntity.getAppAuthToken());
        }
        AlipayTradeQueryResponse aliQryResp = alipayClient.execute(request);
        if (aliQryResp.isSuccess() && aliQryResp.getTradeStatus().equals("TRADE_SUCCESS")) {
            //调用成功，则处理业务逻辑
            logger.info("notifyHandle  out_trade_no={},aliQryResp={}", out_trade_no, aliQryResp);
            handlerResult(oriOrder,aliQryResp);
            return "success";
        }
        return "failure";
    }

    /**
     * 普通模式
     * @param oriOrder
     * @param config
     * @throws AlipayApiException
     */
    public void receivePayResultNotify(CollectionDealerTradeEntity oriOrder,
                                       Map<String, String> config) throws AlipayApiException {
        AlipayTradeQueryResponse aliQryResp = this.queryOrder(oriOrder.getTradeNo(), oriOrder.getChannelCode());
        if (aliQryResp.isSuccess() && aliQryResp.getTradeStatus().equals("TRADE_SUCCESS")) {
            handlerResult(oriOrder,aliQryResp);
        }
    }

    /**
     * 商户模式
     * @param oriOrder
     * @param config
     * @throws AlipayApiException
     */
    public void receivePayResultNotifyISV(CollectionDealerTradeEntity oriOrder,
                                          Map<String, String> config) throws AlipayApiException {
        //ISV代理商户调用需要传入app_auth_token
        AssetDepositAlipayConfEntity assetDepositAlipayConfEntity = assetDepositAlipayConfDao.findByOrgId(oriOrder.getOrgId());
        String appAuthToken = null;
        if(null!=oriOrder.getOrgId()){
            if(null==assetDepositAlipayConfEntity){
                throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "根据组织id无法查询配置信息");
            }
            appAuthToken=assetDepositAlipayConfEntity.getAppAuthToken();
        }
        AlipayTradeQueryResponse aliQryResp = null;
        if(StringUtils.isEmpty(appAuthToken)) {
             aliQryResp = this.queryOrder(oriOrder.getTradeNo(), oriOrder.getChannelCode());
        }else {
             aliQryResp = this.queryOrderISV(oriOrder.getTradeNo(), oriOrder.getChannelCode(),appAuthToken);
        }
        if (aliQryResp.isSuccess() && aliQryResp.getTradeStatus().equals("TRADE_SUCCESS")) {
            handlerResult(oriOrder,aliQryResp);
        }
    }

    private void handlerResult(CollectionDealerTradeEntity oriOrder,AlipayTradeQueryResponse aliQryResp){
        {
            oriOrder.setPayTime(new Date());
            oriOrder.setStatus(TradeStatus.SUCCESS);
            oriOrder.setStatusDes("已支付");
            oriOrder.setChannelTxnId(aliQryResp.getTradeNo());
            oriOrder.setAttach(JSON.toJSONString(aliQryResp));
            this.collectionDealerTradeDao.update(oriOrder);

            CollectionDealerAccountEntity dealerAccount = this.collectionDealerAccountDao
                    .findByDealerId(oriOrder.getDealerId());

            long newBalance = dealerAccount.getBalance().longValue() + oriOrder.getAmount().longValue();

            dealerAccount.setBalance(newBalance);
            dealerAccount.setWxpayNum(dealerAccount.getWxpayNum().longValue() + 1);
            dealerAccount.setWxpaySum(dealerAccount.getWxpaySum().longValue() +
                    oriOrder.getAmount().longValue());
            this.collectionDealerAccountDao.update(dealerAccount);

            CollectionDealerAccountLogEntity log = new CollectionDealerAccountLogEntity();
            log.setAction("支付宝支付");
            log.setAmount(oriOrder.getAmount().longValue());
            log.setChannel("支付宝");
            log.setTradeNo(oriOrder.getTradeNo());
            log.setContent(oriOrder.getBody());
            log.setCreateTime(new Date());
            log.setDealerBalance(newBalance);
            log.setInOrOut(FlowType.IN + "");
            log.setDealerId(oriOrder.getDealerId());
            log.setLogId(UUIDUtils.uuid32());
            log.setOpenId(oriOrder.getOpenId());
            log.setChannelTxnId(oriOrder.getChannelTxnId());
            this.collectionDealerAccountLogDao.create(log);

            Map<String, String> notifyParams = new HashMap<>();
            notifyParams.put("tradeNo", oriOrder.getTradeNo());
            notifyParams.put("outTranNumber", oriOrder.getOutTradeNo());
            notifyParams.put("createTime", DateTimeUtil.format(oriOrder.getCreateTime()));
            notifyParams.put("payTime", DateTimeUtil.format(oriOrder.getPayTime()));
            notifyParams.put("status", oriOrder.getStatus() + "");
            notifyParams.put("statusDes", oriOrder.getStatusDes() + "");
            notifyParams.put("amount", oriOrder.getAmount().toString());
            notifyParams.put("openid", oriOrder.getOpenId());
            notifyParams.put("channel", oriOrder.getChannel());
            this.notifyService.submit(oriOrder.getNotifyUrl(), notifyParams);
            logger.info("回调处理完成!tradeNo={},", oriOrder.getTradeNo());
        }
    }
}
