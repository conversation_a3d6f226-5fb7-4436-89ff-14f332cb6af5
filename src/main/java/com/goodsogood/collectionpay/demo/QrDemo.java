package com.goodsogood.collectionpay.demo;

import com.alibaba.fastjson.JSON;

import java.util.HashMap;
import java.util.Map;

public class QrDemo {
	
	public static void main(String[] args)throws Exception  {
		
		String bankURL="https://ibsbjstar.ccb.com.cn/CCBIS/ccbMain";
		String MERCHANTID = "***************";
        String POSID = "*********";
        String BRANCHID = "*********";
        String ORDERID = "************";
        String PAYMENT= "0.01";
        String CURCODE="01";
        String TXCODE = "530550";
        String REMARK1 = escape("测试");
        String REMARK2 = escape("测试");
        String RETURNTYPE="1";
        String TIMEOUT = "";
        String PUB32TR2= "f6528d5c335b7092fc9ec1b3020111";
        
        StringBuffer tmp = new StringBuffer(); //��ǩ�ֶ�
        tmp.append("MERCHANTID=");
        tmp.append(MERCHANTID);
        tmp.append("&POSID=");
        tmp.append(POSID);
        tmp.append("&BRANCHID=");
        tmp.append(BRANCHID);
        tmp.append("&ORDERID=");
        tmp.append(ORDERID);
        tmp.append("&PAYMENT=");
        tmp.append(PAYMENT);
        tmp.append("&CURCODE=");
        tmp.append(CURCODE);
        tmp.append("&TXCODE=");
        tmp.append(TXCODE);
        tmp.append("&REMARK1=");
        tmp.append(REMARK1);
        tmp.append("&REMARK2=");
        tmp.append(REMARK2);
        tmp.append("&RETURNTYPE=");
        tmp.append(RETURNTYPE);
        tmp.append("&TIMEOUT=");
        tmp.append(TIMEOUT);
        tmp.append("&PUB=");
        tmp.append(PUB32TR2);
     
        Map map = new HashMap();
        map.put("CCB_IBSVersion","V6");
        map.put("MERCHANTID",MERCHANTID);
        map.put("BRANCHID",BRANCHID);
        map.put("POSID",POSID);
        map.put("ORDERID",ORDERID);
        map.put("PAYMENT",PAYMENT);
        map.put("CURCODE",CURCODE);
        map.put("TXCODE",TXCODE);
        map.put("REMARK1",REMARK1);
        map.put("REMARK2",REMARK2);
        map.put("RETURNTYPE",RETURNTYPE);
        map.put("TIMEOUT",TIMEOUT);
        map.put("MAC",MD5.md5Str(tmp.toString()));
        
        String ret = HttpClientUtil.httpPost(bankURL, map);
        System.out.println(ret);

        QrURLDemo qrurl = JSON.parseObject(ret, QrURLDemo.class);
        System.out.println(qrurl.getPAYURL());
    }

    public static String escape(String src) {
        int i;
        char j;
        StringBuffer tmp = new StringBuffer();
        tmp.ensureCapacity(src.length() * 6);
        for (i = 0; i < src.length(); i++) {
            j = src.charAt(i);
            if (Character.isDigit(j) || Character.isLowerCase(j)
                    || Character.isUpperCase(j))
                tmp.append(j);
            else if (j < 256) {
                tmp.append("%");
                if (j < 16)
                    tmp.append("0");
                tmp.append(Integer.toString(j, 16));
            } else {
                tmp.append("%u");
                tmp.append(Integer.toString(j, 16));
            }
        }
        return tmp.toString();
    }
}
