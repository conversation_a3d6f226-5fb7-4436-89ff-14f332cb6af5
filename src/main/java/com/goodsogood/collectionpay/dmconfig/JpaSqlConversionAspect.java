package com.goodsogood.collectionpay.dmconfig;

import lombok.extern.log4j.Log4j2;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

/**
 * JPA原生SQL转换切面
 * 针对collection-pay项目中ExtJpaRepository的原生SQL查询进行MySQL到达梦数据库的语法转换
 * 
 * <AUTHOR>
 * @date 2024-08-28
 */
@Aspect
@Component
@Log4j2
@ConditionalOnProperty(name = "dm.sql.conversion.enabled", havingValue = "true", matchIfMissing = true)
public class JpaSqlConversionAspect {

    // 从配置文件读取是否启用SQL转换功能
    @Value("${dm.sql.conversion.enabled:true}")
    private boolean sqlConversionEnabled;

    // 从配置文件读取是否启用除法保护
    @Value("${dm.sql.division.protection.enabled:true}")
    private boolean enableDivisionProtection;

    // 从配置文件读取是否启用详细日志
    @Value("${dm.sql.detailed.logging.enabled:false}")
    private boolean enableDetailedLogging;

    // 从配置文件读取除法保护策略
    @Value("${dm.sql.division.protection.strategy:INTELLIGENT}")
    private String divisionProtectionStrategy;

    /**
     * 拦截ExtJpaRepository中的findBySql方法
     */
    @Around("execution(* com.goodsogood.collectionpay.dao.support.ExtJpaRepository+.findBySql(..))")
    public Object aroundFindBySql(ProceedingJoinPoint joinPoint) throws Throwable {
        return processSqlMethod(joinPoint, "findBySql");
    }

    /**
     * 拦截ExtJpaRepository中的updateBySql方法
     */
    @Around("execution(* com.goodsogood.collectionpay.dao.support.ExtJpaRepository+.updateBySql(..))")
    public Object aroundUpdateBySql(ProceedingJoinPoint joinPoint) throws Throwable {
        return processSqlMethod(joinPoint, "updateBySql");
    }

    /**
     * 拦截ExtJpaRepository中的findOneBySql方法
     */
    @Around("execution(* com.goodsogood.collectionpay.dao.support.ExtJpaRepository+.findOneBySql(..))")
    public Object aroundFindOneBySql(ProceedingJoinPoint joinPoint) throws Throwable {
        return processSqlMethod(joinPoint, "findOneBySql");
    }

    /**
     * 拦截ExtJpaRepository中的queryUniqueResultBySql方法
     */
    @Around("execution(* com.goodsogood.collectionpay.dao.support.ExtJpaRepository+.queryUniqueResultBySql(..))")
    public Object aroundQueryUniqueResultBySql(ProceedingJoinPoint joinPoint) throws Throwable {
        return processSqlMethod(joinPoint, "queryUniqueResultBySql");
    }

    /**
     * 拦截ExtJpaRepository中的pageQuery方法
     */
    @Around("execution(* com.goodsogood.collectionpay.dao.support.ExtJpaRepository+.pageQuery(..))")
    public Object aroundPageQuery(ProceedingJoinPoint joinPoint) throws Throwable {
        return processPageQueryMethod(joinPoint);
    }

    /**
     * 处理SQL方法的通用逻辑
     */
    private Object processSqlMethod(ProceedingJoinPoint joinPoint, String methodName) throws Throwable {
        if (!sqlConversionEnabled) {
            return joinPoint.proceed();
        }

        Object[] args = joinPoint.getArgs();
        if (args.length > 0 && args[0] instanceof String) {
            String originalSql = (String) args[0];
            
            if (log.isDebugEnabled()) {
                log.debug("🔍 拦截到{}方法的SQL: {}", methodName, originalSql.trim());
            }

            // 检查是否需要转换
            if (SqlCompatibilityUtil.needsConversion(originalSql)) {
                log.info("检测到需要转换的MySQL语法，开始转换...");

                try {
                    // 转换SQL
                    String convertedSql = SqlCompatibilityUtil.convertSql(originalSql);

                    // 智能处理除法保护（如果启用）
                    if (enableDivisionProtection) {
                        convertedSql = applyDivisionProtection(convertedSql, originalSql);
                    }

                    if (!originalSql.equals(convertedSql)) {
                        log.info("✅ SQL转换成功 - 方法: {}", methodName);

                        if (enableDetailedLogging) {
                            log.info("原始SQL: {}", originalSql.trim());
                            log.info("转换后SQL: {}", convertedSql.trim());
                        }

                        // 替换SQL参数
                        args[0] = convertedSql;
                    } else {
                        if (log.isDebugEnabled()) {
                            log.debug("SQL转换后无变化，可能已经是达梦兼容语法");
                        }
                    }
                } catch (Exception e) {
                    log.error("SQL转换过程中发生异常，将使用原始SQL继续执行: {}", e.getMessage());
                    if (log.isDebugEnabled()) {
                        log.debug("SQL转换异常详情", e);
                    }
                }
            } else {
                if (log.isDebugEnabled()) {
                    log.debug("SQL无需转换，已经兼容达梦数据库");
                }
            }
        }

        return joinPoint.proceed(args);
    }

    /**
     * 处理pageQuery方法（包含两个SQL参数）
     */
    private Object processPageQueryMethod(ProceedingJoinPoint joinPoint) throws Throwable {
        if (!sqlConversionEnabled) {
            return joinPoint.proceed();
        }

        Object[] args = joinPoint.getArgs();
        boolean sqlConverted = false;

        // pageQuery方法参数: (int pageIndex, int pageSize, Object[] params, String sql, String countSql)
        if (args.length >= 5) {
            // 转换主查询SQL
            if (args[3] instanceof String) {
                String originalSql = (String) args[3];
                if (SqlCompatibilityUtil.needsConversion(originalSql)) {
                    try {
                        String convertedSql = SqlCompatibilityUtil.convertSql(originalSql);
                        if (enableDivisionProtection) {
                            convertedSql = applyDivisionProtection(convertedSql, originalSql);
                        }
                        if (!originalSql.equals(convertedSql)) {
                            args[3] = convertedSql;
                            sqlConverted = true;
                            if (enableDetailedLogging) {
                                log.info("pageQuery主查询SQL已转换: {} -> {}", originalSql.trim(), convertedSql.trim());
                            }
                        }
                    } catch (Exception e) {
                        log.error("pageQuery主查询SQL转换失败: {}", e.getMessage());
                    }
                }
            }

            // 转换计数SQL
            if (args[4] instanceof String) {
                String originalCountSql = (String) args[4];
                if (SqlCompatibilityUtil.needsConversion(originalCountSql)) {
                    try {
                        String convertedCountSql = SqlCompatibilityUtil.convertSql(originalCountSql);
                        if (enableDivisionProtection) {
                            convertedCountSql = applyDivisionProtection(convertedCountSql, originalCountSql);
                        }
                        if (!originalCountSql.equals(convertedCountSql)) {
                            args[4] = convertedCountSql;
                            sqlConverted = true;
                            if (enableDetailedLogging) {
                                log.info("pageQuery计数SQL已转换: {} -> {}", originalCountSql.trim(), convertedCountSql.trim());
                            }
                        }
                    } catch (Exception e) {
                        log.error("pageQuery计数SQL转换失败: {}", e.getMessage());
                    }
                }
            }
        }

        if (sqlConverted) {
            log.info("✅ pageQuery方法SQL转换完成");
        }

        return joinPoint.proceed(args);
    }

    /**
     * 应用除法保护
     */
    private String applyDivisionProtection(String sql, String originalSql) {
        if (sql == null || !sql.contains("/")) {
            return sql;
        }

        try {
            if (log.isDebugEnabled()) {
                log.debug("🛡️ 检测到除法运算，应用保护策略: {}", divisionProtectionStrategy);
            }

            // 根据保护策略决定处理方式
            switch (divisionProtectionStrategy.toUpperCase()) {
                case "NONE":
                    return sql;

                case "BASIC":
                    return applyBasicDivisionProtection(sql);

                case "STATISTICAL":
                    return SqlCompatibilityUtil.fixComplexStatisticalSql(sql);

                case "INTELLIGENT":
                default:
                    // 智能判断SQL类型并应用相应保护
                    if (isPaymentStatisticalQuery(sql)) {
                        log.info("检测到支付统计查询，应用专门的除法保护");
                        return SqlCompatibilityUtil.fixComplexStatisticalSql(sql);
                    } else if (hasSimpleDivision(sql)) {
                        if (log.isDebugEnabled()) {
                            log.debug("检测到简单除法运算，应用基本保护");
                        }
                        return applyBasicDivisionProtection(sql);
                    }
                    break;
            }

            // 获取并记录修复建议
            String suggestions = SqlCompatibilityUtil.getSqlFixSuggestions(originalSql);
            if (!suggestions.equals("SQL语法检查通过")) {
                log.warn("SQL优化建议: {}", suggestions);
            }

        } catch (Exception e) {
            log.error("应用除法保护时发生错误: {}", e.getMessage());
            if (log.isDebugEnabled()) {
                log.debug("除法保护异常详情", e);
            }
        }

        return sql;
    }

    /**
     * 检查是否是支付相关的统计查询
     */
    private boolean isPaymentStatisticalQuery(String sql) {
        String upperSql = sql.toUpperCase();
        return (upperSql.contains("ROUND(") && upperSql.contains("NVL(") && sql.contains("*100")) ||
               (upperSql.contains("AVG(") && upperSql.contains("SUM(")) ||
               // 支付相关的表名和字段
               sql.contains("collection_dealer") ||
               sql.contains("trade_amount") ||
               sql.contains("fee_amount") ||
               sql.contains("success_rate") ||
               // 统计相关的关键字
               upperSql.contains("GROUP BY") && upperSql.contains("COUNT(") ||
               // 复杂的嵌套查询
               (sql.contains("tmp1.") && sql.contains("tmp2."));
    }

    /**
     * 检查是否有简单的除法运算
     */
    private boolean hasSimpleDivision(String sql) {
        return sql.contains("/") &&
               !sql.contains("CASE WHEN") &&
               !isPaymentStatisticalQuery(sql);
    }

    /**
     * 应用基本的除法保护
     */
    private String applyBasicDivisionProtection(String sql) {
        try {
            // 检查是否包含常见的支付业务除法模式
            if (sql.matches(".*SELECT.*\\w+/\\w+.*FROM.*")) {
                if (log.isDebugEnabled()) {
                    log.debug("检测到SELECT中的除法运算，建议在业务层进行除0检查");
                }
            }
            
            // 对于支付成功率等计算，建议使用CASE WHEN保护
            if (sql.toLowerCase().contains("success") && sql.contains("/")) {
                log.info("检测到成功率计算，建议使用CASE WHEN进行除0保护");
            }
            
        } catch (Exception e) {
            log.warn("基本除法保护检查时发生异常: {}", e.getMessage());
        }

        // 暂时返回原SQL，避免引入语法错误
        // 具体的除法保护逻辑建议在业务层或通过SqlCompatibilityUtil处理
        return sql;
    }
}
