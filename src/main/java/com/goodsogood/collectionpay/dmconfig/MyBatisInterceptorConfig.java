package com.goodsogood.collectionpay.dmconfig;

import lombok.extern.log4j.Log4j2;
import org.apache.ibatis.plugin.Interceptor;
import org.mybatis.spring.boot.autoconfigure.ConfigurationCustomizer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MyBatis拦截器配置
 * 为collection-pay项目配置达梦数据库SQL转换拦截器
 * 使用ConfigurationCustomizer来确保拦截器在正确的时机注册
 *
 * <AUTHOR>
 * @date 2024-08-07
 */
@Configuration
@Log4j2
@ConditionalOnProperty(name = "dm.sql.conversion.enabled", havingValue = "true", matchIfMissing = true)
public class MyBatisInterceptorConfig {

    @Value("${dm.sql.conversion.enabled:true}")
    private boolean sqlConversionEnabled;

    @Autowired
    private StatementHandlerInterceptor statementHandlerInterceptor;

    /**
     * 使用ConfigurationCustomizer来注册拦截器
     * 确保在SqlSessionFactory创建时注册达梦数据库SQL转换拦截器
     */
    @Bean
    public ConfigurationCustomizer configurationCustomizer() {
        return new ConfigurationCustomizer() {
            @Override
            public void customize(org.apache.ibatis.session.Configuration configuration) {
                if (!sqlConversionEnabled) {
                    log.info("SQL转换功能已禁用，跳过拦截器注册");
                    return;
                }

                log.info("=== 开始注册collection-pay达梦数据库SQL转换拦截器 ===");

                // 注册StatementHandler拦截器用于SQL转换
                log.info("注册StatementHandlerInterceptor用于MySQL到达梦SQL转换...");
                configuration.addInterceptor(statementHandlerInterceptor);

                log.info("✅ 拦截器注册完成，当前拦截器总数: {}", configuration.getInterceptors().size());

                // 打印所有拦截器信息（调试模式下）
                if (log.isDebugEnabled()) {
                    for (int i = 0; i < configuration.getInterceptors().size(); i++) {
                        Interceptor interceptor = configuration.getInterceptors().get(i);
                        log.debug("拦截器 #{}: {}", i + 1, interceptor.getClass().getName());
                    }
                }

                log.info("collection-pay项目达梦数据库适配拦截器配置完成");
            }
        };
    }

    /**
     * 备用方案：直接创建拦截器数组
     * 如果ConfigurationCustomizer不工作，可以尝试这种方式
     * 适用于某些特殊的MyBatis配置场景
     */
    @Bean
    @ConditionalOnProperty(name = "dm.sql.fallback.interceptor.enabled", havingValue = "true", matchIfMissing = false)
    public Interceptor[] interceptors() {
        if (!sqlConversionEnabled) {
            log.info("SQL转换功能已禁用，不创建拦截器数组");
            return new Interceptor[0];
        }

        log.info("=== 使用备用方案创建达梦SQL转换拦截器数组 ===");
        log.warn("正在使用备用的拦截器注册方式，如果遇到问题请检查MyBatis配置");

        return new Interceptor[]{
            statementHandlerInterceptor
        };
    }
}
