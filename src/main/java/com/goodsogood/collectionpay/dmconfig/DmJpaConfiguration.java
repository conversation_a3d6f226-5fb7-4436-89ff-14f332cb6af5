package com.goodsogood.collectionpay.dmconfig;

import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * 达梦数据库JPA配置类
 * 为collection-pay项目配置达梦数据库相关的JPA组件和SQL转换功能
 * 
 * <AUTHOR>
 * @date 2024-08-28
 */
@Configuration
@Log4j2
@EnableAspectJAutoProxy
@ConditionalOnProperty(name = "dm.sql.conversion.enabled", havingValue = "true", matchIfMissing = true)
public class DmJpaConfiguration {

    @Value("${dm.sql.conversion.enabled:true}")
    private boolean sqlConversionEnabled;

    @Value("${dm.sql.division.protection.enabled:true}")
    private boolean divisionProtectionEnabled;

    @Value("${dm.sql.division.protection.strategy:INTELLIGENT}")
    private String divisionProtectionStrategy;

    @Value("${dm.sql.detailed.logging.enabled:false}")
    private boolean detailedLoggingEnabled;

    /**
     * 创建JPA SQL转换切面Bean
     * 只有在启用SQL转换功能时才创建
     */
    @Bean
    @ConditionalOnProperty(name = "dm.sql.conversion.enabled", havingValue = "true", matchIfMissing = true)
    public JpaSqlConversionAspect jpaSqlConversionAspect() {
        log.info("=== 初始化collection-pay达梦数据库JPA SQL转换切面 ===");
        log.info("SQL转换功能: {}", sqlConversionEnabled ? "启用" : "禁用");
        log.info("除法保护功能: {} (策略: {})", divisionProtectionEnabled ? "启用" : "禁用", divisionProtectionStrategy);
        log.info("详细日志功能: {}", detailedLoggingEnabled ? "启用" : "禁用");
        
        JpaSqlConversionAspect aspect = new JpaSqlConversionAspect();
        
        log.info("✅ collection-pay达梦数据库JPA SQL转换切面初始化完成");
        return aspect;
    }

    /**
     * 创建SQL兼容性工具Bean
     */
    @Bean
    public SqlCompatibilityUtil sqlCompatibilityUtil() {
        log.info("初始化SQL兼容性工具类");
        return new SqlCompatibilityUtil();
    }

    /**
     * 配置信息输出
     */
    public void logConfiguration() {
        log.info("=== 达梦数据库JPA配置信息 ===");
        log.info("数据源类型: 达梦数据库 (DM)");
        log.info("JPA实现: Hibernate");
        log.info("SQL转换: {} (切面模式)", sqlConversionEnabled ? "启用" : "禁用");
        log.info("转换范围: ExtJpaRepository原生SQL方法");
        log.info("支持方法: findBySql, updateBySql, findOneBySql, queryUniqueResultBySql, pageQuery");
        log.info("除法保护: {} (策略: {})", divisionProtectionEnabled ? "启用" : "禁用", divisionProtectionStrategy);
        log.info("详细日志: {}", detailedLoggingEnabled ? "启用" : "禁用");
        log.info("=== 配置信息输出完成 ===");
    }
}
