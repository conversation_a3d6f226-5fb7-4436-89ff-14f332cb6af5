package com.goodsogood.collectionpay.dmconfig;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 达梦数据库SQL转换配置属性类
 * 用于集中管理SQL转换相关的配置参数
 * 
 * <AUTHOR>
 * @date 2024-08-28
 */
@Component
@ConfigurationProperties(prefix = "dm.sql")
public class DmSqlConversionProperties {

    /**
     * 是否启用SQL转换功能
     */
    private boolean conversionEnabled = true;

    /**
     * 除法保护配置
     */
    private Division division = new Division();

    /**
     * 日志配置
     */
    private Logging logging = new Logging();

    /**
     * 备用配置
     */
    private Fallback fallback = new Fallback();

    /**
     * 性能配置
     */
    private Performance performance = new Performance();

    // Getters and Setters
    public boolean isConversionEnabled() {
        return conversionEnabled;
    }

    public void setConversionEnabled(boolean conversionEnabled) {
        this.conversionEnabled = conversionEnabled;
    }

    public Division getDivision() {
        return division;
    }

    public void setDivision(Division division) {
        this.division = division;
    }

    public Logging getLogging() {
        return logging;
    }

    public void setLogging(Logging logging) {
        this.logging = logging;
    }

    public Fallback getFallback() {
        return fallback;
    }

    public void setFallback(Fallback fallback) {
        this.fallback = fallback;
    }

    public Performance getPerformance() {
        return performance;
    }

    public void setPerformance(Performance performance) {
        this.performance = performance;
    }

    /**
     * 除法保护配置
     */
    public static class Division {
        /**
         * 是否启用除法保护
         */
        private boolean protectionEnabled = true;

        /**
         * 除法保护策略
         */
        private String strategy = "INTELLIGENT";

        public boolean isProtectionEnabled() {
            return protectionEnabled;
        }

        public void setProtectionEnabled(boolean protectionEnabled) {
            this.protectionEnabled = protectionEnabled;
        }

        public String getStrategy() {
            return strategy;
        }

        public void setStrategy(String strategy) {
            this.strategy = strategy;
        }
    }

    /**
     * 日志配置
     */
    public static class Logging {
        /**
         * 是否启用详细日志
         */
        private boolean detailedEnabled = false;

        /**
         * 是否记录转换统计
         */
        private boolean statisticsEnabled = false;

        /**
         * 日志级别
         */
        private String level = "INFO";

        public boolean isDetailedEnabled() {
            return detailedEnabled;
        }

        public void setDetailedEnabled(boolean detailedEnabled) {
            this.detailedEnabled = detailedEnabled;
        }

        public boolean isStatisticsEnabled() {
            return statisticsEnabled;
        }

        public void setStatisticsEnabled(boolean statisticsEnabled) {
            this.statisticsEnabled = statisticsEnabled;
        }

        public String getLevel() {
            return level;
        }

        public void setLevel(String level) {
            this.level = level;
        }
    }

    /**
     * 备用配置
     */
    public static class Fallback {
        /**
         * 是否启用备用拦截器注册方式
         */
        private boolean interceptorEnabled = false;

        /**
         * 转换失败时的处理策略
         */
        private String onFailure = "CONTINUE";

        public boolean isInterceptorEnabled() {
            return interceptorEnabled;
        }

        public void setInterceptorEnabled(boolean interceptorEnabled) {
            this.interceptorEnabled = interceptorEnabled;
        }

        public String getOnFailure() {
            return onFailure;
        }

        public void setOnFailure(String onFailure) {
            this.onFailure = onFailure;
        }
    }

    /**
     * 性能配置
     */
    public static class Performance {
        /**
         * 是否启用转换缓存
         */
        private boolean cacheEnabled = false;

        /**
         * 缓存大小
         */
        private int cacheSize = 1000;

        /**
         * 是否启用性能监控
         */
        private boolean monitoringEnabled = false;

        public boolean isCacheEnabled() {
            return cacheEnabled;
        }

        public void setCacheEnabled(boolean cacheEnabled) {
            this.cacheEnabled = cacheEnabled;
        }

        public int getCacheSize() {
            return cacheSize;
        }

        public void setCacheSize(int cacheSize) {
            this.cacheSize = cacheSize;
        }

        public boolean isMonitoringEnabled() {
            return monitoringEnabled;
        }

        public void setMonitoringEnabled(boolean monitoringEnabled) {
            this.monitoringEnabled = monitoringEnabled;
        }
    }
}
