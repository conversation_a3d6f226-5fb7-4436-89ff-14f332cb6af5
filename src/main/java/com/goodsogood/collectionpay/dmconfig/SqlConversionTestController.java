package com.goodsogood.collectionpay.dmconfig;

import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * SQL转换测试控制器
 * 用于测试达梦数据库SQL转换功能是否正常工作
 * 仅在开发和测试环境中启用
 * 
 * <AUTHOR>
 * @date 2024-08-28
 */
@RestController
@RequestMapping("/dm/sql/test")
@Log4j2
@ConditionalOnProperty(name = "dm.sql.conversion.test.enabled", havingValue = "true", matchIfMissing = false)
public class SqlConversionTestController {

    @Autowired(required = false)
    private JpaSqlConversionAspect jpaSqlConversionAspect;

    /**
     * 测试SQL转换功能状态
     */
    @GetMapping("/status")
    public Map<String, Object> getStatus() {
        Map<String, Object> result = new HashMap<>();
        
        result.put("sqlConversionAspectEnabled", jpaSqlConversionAspect != null);
        result.put("sqlCompatibilityUtilAvailable", SqlCompatibilityUtil.class != null);
        result.put("timestamp", System.currentTimeMillis());
        
        log.info("SQL转换功能状态检查: {}", result);
        
        return result;
    }

    /**
     * 测试SQL转换逻辑
     */
    @GetMapping("/convert")
    public Map<String, Object> testSqlConversion(@RequestParam String sql) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("测试SQL转换: {}", sql);
            
            // 检查是否需要转换
            boolean needsConversion = SqlCompatibilityUtil.needsConversion(sql);
            result.put("needsConversion", needsConversion);
            result.put("originalSql", sql);
            
            if (needsConversion) {
                // 执行转换
                String convertedSql = SqlCompatibilityUtil.convertSql(sql);
                result.put("convertedSql", convertedSql);
                result.put("conversionSuccess", true);
                
                // 获取转换建议
                String suggestions = SqlCompatibilityUtil.getSqlOptimizationSuggestions(sql);
                result.put("suggestions", suggestions);
                
                log.info("SQL转换结果: {} -> {}", sql, convertedSql);
            } else {
                result.put("convertedSql", sql);
                result.put("conversionSuccess", true);
                result.put("message", "SQL无需转换，已经兼容达梦数据库");
                
                log.info("SQL无需转换: {}", sql);
            }
            
        } catch (Exception e) {
            result.put("conversionSuccess", false);
            result.put("error", e.getMessage());
            result.put("convertedSql", sql);
            
            log.error("SQL转换测试失败: {}", e.getMessage(), e);
        }
        
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }

    /**
     * 获取支持的转换类型
     */
    @GetMapping("/supported-conversions")
    public Map<String, Object> getSupportedConversions() {
        Map<String, Object> result = new HashMap<>();
        
        // 支持的转换类型
        Map<String, String> conversions = new HashMap<>();
        conversions.put("LIMIT语法", "LIMIT 10, 20 → LIMIT 20 OFFSET 10");
        conversions.put("时间函数", "NOW() → SYSDATE, DATE_FORMAT() → TO_CHAR()");
        conversions.put("字符串函数", "IFNULL() → NVL(), SUBSTRING() → SUBSTR()");
        conversions.put("数学函数", "CEIL() → CEILING(), POW() → POWER()");
        conversions.put("反引号", "`field` → field 或 \"field\"");
        conversions.put("MySQL变量", "@var := @var + 1 → ROW_NUMBER() OVER()");
        
        result.put("supportedConversions", conversions);
        
        // 除法保护策略
        Map<String, String> strategies = new HashMap<>();
        strategies.put("NONE", "不进行除法保护");
        strategies.put("BASIC", "基本除法检查和提醒");
        strategies.put("STATISTICAL", "统计查询专用保护");
        strategies.put("INTELLIGENT", "智能识别并选择合适的保护策略");
        
        result.put("divisionProtectionStrategies", strategies);
        
        // 拦截的JPA方法
        String[] interceptedMethods = {
            "findBySql(String sql, Object[] args)",
            "updateBySql(String sql, Object... args)",
            "findOneBySql(String sql, Object[] args)",
            "queryUniqueResultBySql(String sql, Object[] args)",
            "pageQuery(int pageIndex, int pageSize, Object[] params, String sql, String countSql)"
        };
        
        result.put("interceptedJpaMethods", interceptedMethods);
        result.put("timestamp", System.currentTimeMillis());
        
        return result;
    }

    /**
     * 测试常见的SQL转换示例
     */
    @GetMapping("/examples")
    public Map<String, Object> getConversionExamples() {
        Map<String, Object> result = new HashMap<>();
        Map<String, Map<String, String>> examples = new HashMap<>();
        
        // LIMIT语法示例
        Map<String, String> limitExample = new HashMap<>();
        limitExample.put("mysql", "SELECT * FROM table LIMIT 10, 20");
        limitExample.put("dameng", "SELECT * FROM table LIMIT 20 OFFSET 10");
        examples.put("LIMIT语法", limitExample);
        
        // 时间函数示例
        Map<String, String> timeExample = new HashMap<>();
        timeExample.put("mysql", "SELECT NOW(), DATE_FORMAT(create_time, '%Y-%m-%d') FROM table");
        timeExample.put("dameng", "SELECT SYSDATE, TO_CHAR(create_time, 'YYYY-MM-DD') FROM table");
        examples.put("时间函数", timeExample);
        
        // 字符串函数示例
        Map<String, String> stringExample = new HashMap<>();
        stringExample.put("mysql", "SELECT IFNULL(name, 'N/A'), SUBSTRING(code, 1, 5) FROM table");
        stringExample.put("dameng", "SELECT NVL(name, 'N/A'), SUBSTR(code, 1, 5) FROM table");
        examples.put("字符串函数", stringExample);
        
        // 支付业务示例
        Map<String, String> paymentExample = new HashMap<>();
        paymentExample.put("mysql", "SELECT dealer_id, trade_amount/100 as amount FROM collection_dealer_trade LIMIT 10");
        paymentExample.put("dameng", "SELECT dealer_id, trade_amount/100 as amount FROM collection_dealer_trade LIMIT 10");
        paymentExample.put("note", "支付金额计算，建议添加除0保护");
        examples.put("支付业务", paymentExample);
        
        result.put("examples", examples);
        result.put("timestamp", System.currentTimeMillis());
        
        return result;
    }
}
