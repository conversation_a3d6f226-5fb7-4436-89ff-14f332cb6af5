package com.goodsogood.collectionpay.dmconfig;

import lombok.extern.log4j.Log4j2;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.plugin.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.Properties;

/**
 * StatementHandler拦截器
 * 专门拦截StatementHandler的prepare方法来转换SQL
 * 适配collection-pay项目的达梦数据库SQL转换需求
 *
 * <AUTHOR>
 * @date 2024-08-07
 */
@Component
@Log4j2
@Intercepts({
    @Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})
})
public class StatementHandlerInterceptor implements Interceptor {

    // 从配置文件读取是否启用SQL转换功能
    @Value("${dm.sql.conversion.enabled:true}")
    private boolean sqlConversionEnabled;

    // 从配置文件读取是否启用除法保护
    @Value("${dm.sql.division.protection.enabled:true}")
    private boolean enableDivisionProtection;

    // 从配置文件读取是否启用详细日志
    @Value("${dm.sql.detailed.logging.enabled:false}")
    private boolean enableDetailedLogging;

    // 从配置文件读取除法保护策略
    @Value("${dm.sql.division.protection.strategy:INTELLIGENT}")
    private String divisionProtectionStrategy;

    // 数据源标识
    private String dataSourceKey = "collection-pay-dm";

    // 数据源描述
    private String dataSourceDescription = "收单支付系统达梦数据源";

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 如果SQL转换功能被禁用，直接执行原方法
        if (!sqlConversionEnabled) {
            return invocation.proceed();
        }

        if (log.isDebugEnabled()) {
            log.debug("🔍 StatementHandler拦截器开始处理SQL转换");
        }

        try {
            StatementHandler statementHandler = (StatementHandler) invocation.getTarget();

            // 获取BoundSql
            BoundSql boundSql = statementHandler.getBoundSql();
            String originalSql = boundSql.getSql();

            if (log.isDebugEnabled()) {
                log.debug("拦截到SQL: {}", originalSql.trim());
            }

            // 检查是否需要转换
            if (SqlCompatibilityUtil.needsConversion(originalSql)) {
                log.info("检测到需要转换的MySQL语法，开始转换...");

                // 转换SQL
                String convertedSql = SqlCompatibilityUtil.convertSql(originalSql);

                // 智能处理除法保护（如果启用）
                if (enableDivisionProtection) {
                    convertedSql = applyIntelligentDivisionProtection(convertedSql, originalSql);
                }

                if (!originalSql.equals(convertedSql)) {
                    log.info("✅ SQL转换成功");

                    if (enableDetailedLogging) {
                        log.info("原始SQL: {}", originalSql.trim());
                        log.info("转换后SQL: {}", convertedSql.trim());
                    }

                    // 直接修改BoundSql中的SQL字符串
                    modifySqlInBoundSql(boundSql, convertedSql);

                    log.info("SQL已成功替换为达梦兼容语法");
                } else {
                    log.debug("SQL转换后无变化，可能已经是达梦兼容语法");
                }
            } else {
                if (log.isDebugEnabled()) {
                    log.debug("SQL无需转换，已经兼容达梦数据库");
                }
            }

        } catch (Exception e) {
            log.error("SQL转换过程中发生异常，将使用原始SQL继续执行: {}", e.getMessage());
            if (log.isDebugEnabled()) {
                log.debug("SQL转换异常详情", e);
            }
            // 发生错误时继续使用原始SQL，确保业务不受影响
        }

        // 继续执行原方法
        return invocation.proceed();
    }

    /**
     * 智能应用除法保护
     * 根据收单支付业务特点和保护策略选择合适的除法保护方式
     */
    private String applyIntelligentDivisionProtection(String sql, String originalSql) {
        if (sql == null || !sql.contains("/")) {
            return sql;
        }

        try {
            if (log.isDebugEnabled()) {
                log.debug("🛡️ 检测到除法运算，应用保护策略: {}", divisionProtectionStrategy);
            }

            // 根据保护策略决定处理方式
            switch (divisionProtectionStrategy.toUpperCase()) {
                case "NONE":
                    if (log.isDebugEnabled()) {
                        log.debug("除法保护策略为NONE，跳过保护");
                    }
                    return sql;

                case "BASIC":
                    log.info("应用基本除法保护");
                    sql = applyBasicDivisionProtection(sql);
                    break;

                case "STATISTICAL":
                    log.info("应用统计查询除法保护");
                    sql = SqlCompatibilityUtil.fixComplexStatisticalSql(sql);
                    break;

                case "INTELLIGENT":
                default:
                    // 智能判断SQL类型并应用相应保护
                    if (isPaymentStatisticalQuery(sql)) {
                        log.info("检测到支付统计查询，应用专门的除法保护");
                        sql = SqlCompatibilityUtil.fixComplexStatisticalSql(sql);
                    } else if (hasSimpleDivision(sql)) {
                        if (log.isDebugEnabled()) {
                            log.debug("检测到简单除法运算，应用基本保护");
                        }
                        sql = applyBasicDivisionProtection(sql);
                    }
                    break;
            }

            // 获取并记录修复建议
            String suggestions = SqlCompatibilityUtil.getSqlFixSuggestions(originalSql);
            if (!suggestions.equals("SQL语法检查通过")) {
                log.warn("SQL优化建议: {}", suggestions);
            }

            if (log.isDebugEnabled()) {
                log.debug("✅ 除法保护应用完成，策略: {}", divisionProtectionStrategy);
            }

        } catch (Exception e) {
            log.error("应用除法保护时发生错误: {}", e.getMessage());
            log.warn("继续使用未保护的SQL，请注意可能的除0风险");
            if (log.isDebugEnabled()) {
                log.debug("除法保护异常详情", e);
            }
        }

        return sql;
    }

    /**
     * 检查是否是支付相关的统计查询
     * 根据collection-pay项目的业务特点进行判断
     */
    private boolean isPaymentStatisticalQuery(String sql) {
        String upperSql = sql.toUpperCase();
        return (upperSql.contains("ROUND(") && upperSql.contains("NVL(") && sql.contains("*100")) ||
               (upperSql.contains("AVG(") && upperSql.contains("SUM(")) ||
               // 支付相关的表名和字段
               sql.contains("collection_dealer") ||
               sql.contains("trade_amount") ||
               sql.contains("fee_amount") ||
               sql.contains("success_rate") ||
               // 统计相关的关键字
               upperSql.contains("GROUP BY") && upperSql.contains("COUNT(") ||
               // 复杂的嵌套查询
               (sql.contains("tmp1.") && sql.contains("tmp2."));
    }

    /**
     * 检查是否有简单的除法运算
     */
    private boolean hasSimpleDivision(String sql) {
        // 简单除法：不在复杂函数中的基本除法运算
        return sql.contains("/") &&
               !sql.contains("CASE WHEN") &&
               !isComplexStatisticalQuery(sql);
    }

    /**
     * 应用基本的除法保护
     * 针对收单支付业务中常见的除法运算进行保护
     */
    private String applyBasicDivisionProtection(String sql) {
        // 对于简单的除法，应用基本保护
        // 采用保守策略，避免引入语法错误

        try {
            // 检查是否包含常见的支付业务除法模式
            if (sql.matches(".*SELECT.*\\w+/\\w+.*FROM.*")) {
                if (log.isDebugEnabled()) {
                    log.debug("检测到SELECT中的除法运算，建议在业务层进行除0检查");
                }
            }

            // 对于支付成功率等计算，建议使用CASE WHEN保护
            if (sql.toLowerCase().contains("success") && sql.contains("/")) {
                log.info("检测到成功率计算，建议使用CASE WHEN进行除0保护");
            }

        } catch (Exception e) {
            log.warn("基本除法保护检查时发生异常: {}", e.getMessage());
        }

        // 暂时返回原SQL，避免引入语法错误
        // 具体的除法保护逻辑建议在业务层或通过SqlCompatibilityUtil处理
        return sql;
    }

    /**
     * 直接修改BoundSql中的SQL字符串
     * 使用反射机制替换SQL，确保达梦数据库兼容性
     */
    private void modifySqlInBoundSql(BoundSql boundSql, String newSql) {
        try {
            Field sqlField = BoundSql.class.getDeclaredField("sql");
            sqlField.setAccessible(true);
            sqlField.set(boundSql, newSql);

            if (log.isDebugEnabled()) {
                log.debug("成功修改BoundSql中的SQL为达梦兼容语法");
            }
        } catch (NoSuchFieldException e) {
            log.error("无法找到BoundSql的sql字段，可能MyBatis版本不兼容: {}", e.getMessage());
        } catch (IllegalAccessException e) {
            log.error("无法访问BoundSql的sql字段: {}", e.getMessage());
        } catch (Exception e) {
            log.error("修改BoundSql中的SQL时发生未知错误: {}", e.getMessage());
            if (log.isDebugEnabled()) {
                log.debug("SQL修改异常详情", e);
            }
        }
    }

    @Override
    public Object plugin(Object target) {
        if (log.isDebugEnabled()) {
            log.debug("StatementHandlerInterceptor.plugin被调用，target: {}", target.getClass().getName());
        }
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        log.info("初始化collection-pay项目的SQL转换拦截器");

        // 读取配置
        if (properties != null) {
            // 从Properties读取配置（优先级高于@Value注解）
            String conversionEnabled = properties.getProperty("sqlConversionEnabled");
            if (conversionEnabled != null) {
                this.sqlConversionEnabled = Boolean.parseBoolean(conversionEnabled);
            }

            String divisionProtection = properties.getProperty("enableDivisionProtection");
            if (divisionProtection != null) {
                this.enableDivisionProtection = Boolean.parseBoolean(divisionProtection);
            }

            String detailedLogging = properties.getProperty("enableDetailedLogging");
            if (detailedLogging != null) {
                this.enableDetailedLogging = Boolean.parseBoolean(detailedLogging);
            }

            String strategy = properties.getProperty("divisionProtectionStrategy");
            if (strategy != null) {
                this.divisionProtectionStrategy = strategy;
            }

            String dsKey = properties.getProperty("dataSourceKey");
            if (dsKey != null) {
                this.dataSourceKey = dsKey;
            }

            String dsDesc = properties.getProperty("dataSourceDescription");
            if (dsDesc != null) {
                this.dataSourceDescription = dsDesc;
            }
        }

        log.info("拦截器配置完成 - 数据源: {} ({})", dataSourceKey, dataSourceDescription);
        log.info("SQL转换功能: {}", sqlConversionEnabled ? "启用" : "禁用");
        log.info("除法保护功能: {} (策略: {})", enableDivisionProtection ? "启用" : "禁用", divisionProtectionStrategy);
        log.info("详细日志功能: {}", enableDetailedLogging ? "启用" : "禁用");
        log.info("collection-pay达梦数据库SQL转换拦截器初始化完成");
    }
}
