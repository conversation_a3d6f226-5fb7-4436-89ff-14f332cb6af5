package com.goodsogood.collectionpay.dmconfig;

import lombok.extern.log4j.Log4j2;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.plugin.*;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.Properties;

/**
 * StatementHandler拦截器
 * 专门拦截StatementHandler的prepare方法来转换SQL
 * 这是MyBatis执行SQL的关键点
 * 
 * <AUTHOR>
 * @date 2024-08-07
 */
@Component
@Log4j2
@Intercepts({
    @Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})
})
public class StatementHandlerInterceptor implements Interceptor {

    // 配置选项：是否启用除法保护
    private boolean enableDivisionProtection = true;

    // 配置选项：是否启用详细日志
    private boolean enableDetailedLogging = true;

    // 配置选项：除法保护策略
    private String divisionProtectionStrategy = "INTELLIGENT";

    // 配置选项：数据源标识
    private String dataSourceKey = "default";

    // 配置选项：数据源描述
    private String dataSourceDescription = "默认数据源";

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        log.info("🚀🚀🚀 StatementHandler拦截器被调用！🚀🚀🚀");
        
        try {
            StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
            
            // 获取BoundSql
            BoundSql boundSql = statementHandler.getBoundSql();
            String originalSql = boundSql.getSql();
            
            log.info("StatementHandler拦截到SQL: {}", originalSql.trim());
            
            // 检查是否需要转换
            if (SqlCompatibilityUtil.needsConversion(originalSql)) {
                log.info("SQL需要转换");

                // 转换SQL
                String convertedSql = SqlCompatibilityUtil.convertSql(originalSql);

                // 智能处理除法保护（如果启用）
                if (enableDivisionProtection) {
                    convertedSql = applyIntelligentDivisionProtection(convertedSql, originalSql);
                }

                if (!originalSql.equals(convertedSql)) {
                    log.info("🔄 SQL转换完成！");

                    if (enableDetailedLogging) {
                        log.info("原SQL: {}", originalSql.trim());
                        log.info("转换后: {}", convertedSql.trim());
                    } else {
                        log.info("SQL已转换（详细日志已禁用）");
                    }

                    // 直接修改BoundSql中的SQL字符串
                    modifySqlInBoundSql(boundSql, convertedSql);

                    log.info("✅ SQL已成功替换");
                } else {
                    log.info("SQL转换后无变化");
                }
            } else {
                log.debug("SQL不需要转换");
            }
            
        } catch (Exception e) {
            log.error("StatementHandler拦截器处理过程中发生错误: {}", e.getMessage(), e);
            // 发生错误时继续使用原始SQL
        }
        
        // 继续执行原方法
        Object result = invocation.proceed();
        
        log.info("🚀🚀🚀 StatementHandler拦截器执行完成！🚀🚀🚀");
        
        return result;
    }

    /**
     * 智能应用除法保护
     * 根据数据源类型、保护策略和SQL特征选择合适的除法保护方式
     */
    private String applyIntelligentDivisionProtection(String sql, String originalSql) {
        if (sql == null || !sql.contains("/")) {
            return sql;
        }

        try {
            log.info("🛡️ 检测到除法运算，数据源: {} ({}), 策略: {}",
                    dataSourceKey, dataSourceDescription, divisionProtectionStrategy);

            // 根据保护策略决定处理方式
            switch (divisionProtectionStrategy.toUpperCase()) {
                case "NONE":
                    log.info("除法保护策略为NONE，跳过保护");
                    return sql;

                case "BASIC":
                    log.info("应用基本除法保护");
                    sql = applyBasicDivisionProtection(sql);
                    break;

                case "STATISTICAL":
                    log.info("应用统计查询除法保护");
                    sql = SqlCompatibilityUtil.fixComplexStatisticalSql(sql);
                    break;

                case "INTELLIGENT":
                default:
                    // 智能判断SQL类型并应用相应保护
                    if (isComplexStatisticalQuery(sql)) {
                        log.info("检测到复杂统计查询，应用专门的除法保护");
                        sql = SqlCompatibilityUtil.fixComplexStatisticalSql(sql);
                    } else if (hasSimpleDivision(sql)) {
                        log.info("检测到简单除法运算，应用基本保护");
                        sql = applyBasicDivisionProtection(sql);
                    }
                    break;
            }

            // 获取并记录修复建议
            String suggestions = SqlCompatibilityUtil.getSqlFixSuggestions(originalSql);
            if (!suggestions.equals("SQL语法检查通过")) {
                log.warn("除法保护建议: {}", suggestions);
            }

            log.info("✅ 除法保护应用完成 - 数据源: {}, 策略: {}", dataSourceKey, divisionProtectionStrategy);

        } catch (Exception e) {
            log.error("应用除法保护时发生错误: {}", e.getMessage(), e);
            log.warn("继续使用未保护的SQL，可能存在除0风险");
        }

        return sql;
    }

    /**
     * 检查是否是复杂统计查询
     */
    private boolean isComplexStatisticalQuery(String sql) {
        String upperSql = sql.toUpperCase();
        return (upperSql.contains("ROUND(") && upperSql.contains("NVL(") && sql.contains("*100")) ||
               (sql.contains("tmp1.") && sql.contains("tmp2.") && sql.contains("scoreMedian")) ||
               (upperSql.contains("AVG(") && upperSql.contains("SUM(")) ||
               sql.contains("organization_id");
    }

    /**
     * 检查是否有简单的除法运算
     */
    private boolean hasSimpleDivision(String sql) {
        // 简单除法：不在复杂函数中的基本除法运算
        return sql.contains("/") &&
               !sql.contains("CASE WHEN") &&
               !isComplexStatisticalQuery(sql);
    }

    /**
     * 应用基本的除法保护
     */
    private String applyBasicDivisionProtection(String sql) {
        // 对于简单的除法，应用基本保护
        // 这里使用简单的模式匹配，避免复杂的正则表达式

        // 处理 SELECT 中的简单除法
        if (sql.matches(".*SELECT.*\\w+/\\w+.*FROM.*")) {
            log.info("为SELECT中的除法添加保护");
            // 这里可以根据具体需求添加简单的保护逻辑
            // 暂时返回原SQL，避免引入错误
        }

        return sql;
    }

    /**
     * 直接修改BoundSql中的SQL字符串
     */
    private void modifySqlInBoundSql(BoundSql boundSql, String newSql) {
        try {
            Field sqlField = BoundSql.class.getDeclaredField("sql");
            sqlField.setAccessible(true);
            sqlField.set(boundSql, newSql);
            log.info("成功修改BoundSql中的SQL");
        } catch (Exception e) {
            log.error("修改BoundSql中的SQL失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public Object plugin(Object target) {
        log.debug("StatementHandlerInterceptor.plugin被调用，target: {}", target.getClass().getName());
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        log.info("StatementHandlerInterceptor.setProperties被调用");

        // 读取配置
        if (properties != null) {
            String divisionProtection = properties.getProperty("enableDivisionProtection", "true");
            this.enableDivisionProtection = Boolean.parseBoolean(divisionProtection);

            String detailedLogging = properties.getProperty("enableDetailedLogging", "true");
            this.enableDetailedLogging = Boolean.parseBoolean(detailedLogging);

            this.divisionProtectionStrategy = properties.getProperty("divisionProtectionStrategy", "INTELLIGENT");
            this.dataSourceKey = properties.getProperty("dataSourceKey", "default");
            this.dataSourceDescription = properties.getProperty("dataSourceDescription", "默认数据源");

            log.info("拦截器配置 - 数据源: {} ({})", dataSourceKey, dataSourceDescription);
            log.info("除法保护功能: {} (策略: {})", enableDivisionProtection ? "启用" : "禁用", divisionProtectionStrategy);
            log.info("详细日志功能: {}", enableDetailedLogging ? "启用" : "禁用");
        }

        log.info("StatementHandler拦截器初始化完成");
    }
}
