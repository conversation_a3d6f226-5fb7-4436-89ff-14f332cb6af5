# 达梦数据库SQL转换切面使用说明

## 概述

本模块为collection-pay收单支付系统提供MySQL到达梦数据库的SQL自动转换功能，通过MyBatis拦截器实现SQL语法的实时转换，确保系统在达梦数据库环境下的正常运行。

## 核心组件

### 1. StatementHandlerInterceptor
- **功能**: 拦截MyBatis的SQL执行，自动转换MySQL语法为达梦兼容语法
- **拦截点**: StatementHandler.prepare方法
- **转换范围**: LIMIT语法、时间函数、字符串函数、数学函数等

### 2. SqlCompatibilityUtil
- **功能**: 提供具体的SQL转换逻辑
- **支持转换**: 
  - MySQL变量语法 → 达梦窗口函数
  - LIMIT语法转换
  - 函数名转换（NOW()→SYSDATE, IFNULL()→NVL()等）
  - 除法保护（防止除0错误）

### 3. MyBatisInterceptorConfig
- **功能**: 配置和注册拦截器
- **特性**: 支持条件化启用/禁用

## 配置说明

### application.properties配置项

```properties
# 是否启用SQL转换功能（默认：true）
dm.sql.conversion.enabled=true

# 是否启用除法保护（默认：true）
dm.sql.division.protection.enabled=true

# 除法保护策略（默认：INTELLIGENT）
# 可选值：NONE, BASIC, STATISTICAL, INTELLIGENT
dm.sql.division.protection.strategy=INTELLIGENT

# 是否启用详细日志（默认：false，生产环境建议关闭）
dm.sql.detailed.logging.enabled=false

# 是否启用备用拦截器注册方式（默认：false）
dm.sql.fallback.interceptor.enabled=false
```

### 环境配置建议

#### 开发环境
```properties
dm.sql.conversion.enabled=true
dm.sql.detailed.logging.enabled=true
```

#### 测试环境
```properties
dm.sql.conversion.enabled=true
dm.sql.detailed.logging.enabled=true
dm.sql.division.protection.strategy=INTELLIGENT
```

#### 生产环境
```properties
dm.sql.conversion.enabled=true
dm.sql.detailed.logging.enabled=false
dm.sql.division.protection.strategy=INTELLIGENT
```

## 支持的SQL转换

### 1. LIMIT语法转换
```sql
-- MySQL语法
SELECT * FROM table LIMIT 10, 20

-- 转换为达梦语法
SELECT * FROM table LIMIT 20 OFFSET 10
```

### 2. 时间函数转换
```sql
-- MySQL → 达梦
NOW() → SYSDATE
UNIX_TIMESTAMP() → TIMESTAMPDIFF(SECOND, '1970-01-01 00:00:00', SYSDATE)
DATE_FORMAT(date, format) → TO_CHAR(date, format)
```

### 3. 字符串函数转换
```sql
-- MySQL → 达梦
IFNULL(expr1, expr2) → NVL(expr1, expr2)
SUBSTRING(str, pos, len) → SUBSTR(str, pos, len)
LEFT(str, len) → SUBSTR(str, 1, len)
```

### 4. 数学函数转换
```sql
-- MySQL → 达梦
CEIL(x) → CEILING(x)
POW(x, y) → POWER(x, y)
RAND() → RANDOM()
```

## 除法保护策略

### NONE
不进行任何除法保护，保持原始SQL。

### BASIC
对简单的除法运算进行基本检查和提醒。

### STATISTICAL
专门针对统计查询中的除法运算进行保护。

### INTELLIGENT（推荐）
智能识别SQL类型，根据业务场景选择合适的保护策略：
- 支付统计查询：应用统计专用保护
- 简单除法：应用基本保护
- 复杂查询：提供优化建议

## 日志说明

### 正常转换日志
```
INFO  - 检测到需要转换的MySQL语法，开始转换...
INFO  - ✅ SQL转换成功
INFO  - SQL已成功替换为达梦兼容语法
```

### 详细日志（开发调试用）
```
INFO  - 原始SQL: SELECT * FROM table LIMIT 10
INFO  - 转换后SQL: SELECT * FROM table LIMIT 10
```

### 异常处理日志
```
ERROR - SQL转换过程中发生异常，将使用原始SQL继续执行: [异常信息]
```

## 性能影响

- **拦截开销**: 每个SQL执行前进行一次拦截，开销极小
- **转换开销**: 仅对需要转换的SQL进行处理，大部分SQL直接通过
- **内存影响**: 转换过程中会创建新的SQL字符串，但会及时释放

## 故障排除

### 1. 拦截器未生效
检查配置：
```properties
dm.sql.conversion.enabled=true
```

### 2. SQL转换异常
查看日志中的异常信息，通常是由于：
- SQL语法过于复杂
- 包含不支持的MySQL特性
- 达梦数据库版本兼容性问题

### 3. 性能问题
如果发现性能影响，可以：
- 关闭详细日志：`dm.sql.detailed.logging.enabled=false`
- 调整除法保护策略：`dm.sql.division.protection.strategy=NONE`

## 注意事项

1. **备份重要数据**: 在生产环境启用前，请确保数据已备份
2. **充分测试**: 建议在测试环境充分验证所有业务功能
3. **监控日志**: 关注转换异常日志，及时处理问题SQL
4. **版本兼容**: 确保达梦数据库版本与转换逻辑兼容

## 联系支持

如遇到问题，请：
1. 查看详细日志信息
2. 记录问题SQL和转换结果
3. 联系系统开发团队

---
*最后更新: 2024-08-28*
