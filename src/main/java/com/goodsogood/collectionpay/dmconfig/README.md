# 达梦数据库JPA SQL转换切面使用说明

## 概述

本模块为collection-pay收单支付系统提供MySQL到达梦数据库的SQL自动转换功能，通过Spring AOP切面拦截JPA原生SQL查询，实现SQL语法的实时转换，确保系统在达梦数据库环境下的正常运行。

## 核心组件

### 1. JpaSqlConversionAspect
- **功能**: 拦截ExtJpaRepository的原生SQL方法，自动转换MySQL语法为达梦兼容语法
- **拦截点**: ExtJpaRepository中的SQL方法（findBySql, updateBySql, findOneBySql, queryUniqueResultBySql, pageQuery）
- **转换范围**: LIMIT语法、时间函数、字符串函数、数学函数等

### 2. SqlCompatibilityUtil
- **功能**: 提供具体的SQL转换逻辑
- **支持转换**:
  - MySQL变量语法 → 达梦窗口函数
  - LIMIT语法转换
  - 函数名转换（NOW()→SYSDATE, IFNULL()→NVL()等）
  - 除法保护（防止除0错误）

### 3. DmJpaConfiguration
- **功能**: 配置和管理JPA SQL转换切面
- **特性**: 支持条件化启用/禁用，集中管理配置

## 配置说明

### application.properties配置项

```properties
# 是否启用SQL转换功能（默认：true）
dm.sql.conversion.enabled=true

# 是否启用除法保护（默认：true）
dm.sql.division.protection.enabled=true

# 除法保护策略（默认：INTELLIGENT）
# 可选值：NONE, BASIC, STATISTICAL, INTELLIGENT
dm.sql.division.protection.strategy=INTELLIGENT

# 是否启用详细日志（默认：false，生产环境建议关闭）
dm.sql.detailed.logging.enabled=false

# 是否启用备用拦截器注册方式（默认：false）
dm.sql.fallback.interceptor.enabled=false
```

### 环境配置建议

#### 开发环境
```properties
dm.sql.conversion.enabled=true
dm.sql.detailed.logging.enabled=true
```

#### 测试环境
```properties
dm.sql.conversion.enabled=true
dm.sql.detailed.logging.enabled=true
dm.sql.division.protection.strategy=INTELLIGENT
```

#### 生产环境
```properties
dm.sql.conversion.enabled=true
dm.sql.detailed.logging.enabled=false
dm.sql.division.protection.strategy=INTELLIGENT
```

## 支持的SQL转换

### 1. LIMIT语法转换
```sql
-- MySQL语法
SELECT * FROM table LIMIT 10, 20

-- 转换为达梦语法
SELECT * FROM table LIMIT 20 OFFSET 10
```

### 2. 时间函数转换
```sql
-- MySQL → 达梦
NOW() → SYSDATE
UNIX_TIMESTAMP() → TIMESTAMPDIFF(SECOND, '1970-01-01 00:00:00', SYSDATE)
DATE_FORMAT(date, format) → TO_CHAR(date, format)
```

### 3. 字符串函数转换
```sql
-- MySQL → 达梦
IFNULL(expr1, expr2) → NVL(expr1, expr2)
SUBSTRING(str, pos, len) → SUBSTR(str, pos, len)
LEFT(str, len) → SUBSTR(str, 1, len)
```

### 4. 数学函数转换
```sql
-- MySQL → 达梦
CEIL(x) → CEILING(x)
POW(x, y) → POWER(x, y)
RAND() → RANDOM()
```

## 除法保护策略

### NONE
不进行任何除法保护，保持原始SQL。

### BASIC
对简单的除法运算进行基本检查和提醒。

### STATISTICAL
专门针对统计查询中的除法运算进行保护。

### INTELLIGENT（推荐）
智能识别SQL类型，根据业务场景选择合适的保护策略：
- 支付统计查询：应用统计专用保护
- 简单除法：应用基本保护
- 复杂查询：提供优化建议

## 日志说明

### 正常转换日志
```
INFO  - 检测到需要转换的MySQL语法，开始转换...
INFO  - ✅ SQL转换成功
INFO  - SQL已成功替换为达梦兼容语法
```

### 详细日志（开发调试用）
```
INFO  - 原始SQL: SELECT * FROM table LIMIT 10
INFO  - 转换后SQL: SELECT * FROM table LIMIT 10
```

### 异常处理日志
```
ERROR - SQL转换过程中发生异常，将使用原始SQL继续执行: [异常信息]
```

## 拦截的JPA方法

本切面专门拦截ExtJpaRepository中的以下原生SQL方法：

### 1. findBySql(String sql, Object[] args)
- **用途**: 执行查询SQL，返回Map列表
- **转换**: 自动转换SQL中的MySQL语法

### 2. updateBySql(String sql, Object... args)
- **用途**: 执行更新SQL
- **转换**: 自动转换SQL中的MySQL语法

### 3. findOneBySql(String sql, Object[] args)
- **用途**: 执行查询SQL，返回单个Map对象
- **转换**: 自动转换SQL中的MySQL语法

### 4. queryUniqueResultBySql(String sql, Object[] args)
- **用途**: 执行查询SQL，返回唯一结果
- **转换**: 自动转换SQL中的MySQL语法

### 5. pageQuery(int pageIndex, int pageSize, Object[] params, String sql, String countSql)
- **用途**: 执行分页查询
- **转换**: 同时转换主查询SQL和计数SQL

## 性能影响

- **切面开销**: 每个原生SQL方法调用前进行一次拦截，开销极小
- **转换开销**: 仅对需要转换的SQL进行处理，大部分SQL直接通过
- **内存影响**: 转换过程中会创建新的SQL字符串，但会及时释放
- **JPA缓存**: 不影响JPA的一级缓存和二级缓存机制

## 故障排除

### 1. 切面未生效
检查配置：
```properties
dm.sql.conversion.enabled=true
spring.aop.auto=true
spring.aop.proxy-target-class=true
```

### 2. SQL转换异常
查看日志中的异常信息，通常是由于：
- SQL语法过于复杂
- 包含不支持的MySQL特性
- 达梦数据库版本兼容性问题

### 3. 性能问题
如果发现性能影响，可以：
- 关闭详细日志：`dm.sql.detailed.logging.enabled=false`
- 调整除法保护策略：`dm.sql.division.protection.strategy=NONE`

### 4. JPA相关问题
- 确保使用的是ExtJpaRepository接口
- 检查原生SQL方法的参数类型是否正确
- 验证Hibernate方言配置是否正确

## 测试接口

在测试环境中，可以通过以下接口测试SQL转换功能：

### 1. 检查状态
```
GET /dm/sql/test/status
```
返回SQL转换功能的启用状态

### 2. 测试SQL转换
```
GET /dm/sql/test/convert?sql=SELECT * FROM table LIMIT 10
```
测试指定SQL的转换结果

### 3. 查看支持的转换
```
GET /dm/sql/test/supported-conversions
```
查看所有支持的SQL转换类型

### 4. 查看转换示例
```
GET /dm/sql/test/examples
```
查看常见的SQL转换示例

## 注意事项

1. **备份重要数据**: 在生产环境启用前，请确保数据已备份
2. **充分测试**: 建议在测试环境充分验证所有业务功能
3. **监控日志**: 关注转换异常日志，及时处理问题SQL
4. **版本兼容**: 确保达梦数据库版本与转换逻辑兼容
5. **JPA方法**: 只有使用ExtJpaRepository的原生SQL方法才会被拦截转换
6. **测试接口**: 测试接口仅在测试环境启用，生产环境自动禁用

## 架构优势

1. **非侵入性**: 通过AOP切面实现，不需要修改现有业务代码
2. **灵活配置**: 支持通过配置文件灵活控制转换功能
3. **JPA兼容**: 专门针对JPA原生SQL查询设计，完全兼容现有架构
4. **性能优化**: 只拦截需要转换的SQL方法，对性能影响极小
5. **易于维护**: 集中管理SQL转换逻辑，便于维护和扩展

## 联系支持

如遇到问题，请：
1. 查看详细日志信息
2. 使用测试接口验证转换逻辑
3. 记录问题SQL和转换结果
4. 联系系统开发团队

---
*最后更新: 2024-08-28*
*适用于: collection-pay JPA项目*
