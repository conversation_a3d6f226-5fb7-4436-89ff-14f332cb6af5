package com.goodsogood.collectionpay.model;

import java.util.Date;

/**
 * 订单Vo
 */
public class OrderVo {

    /**
     * 订单编号 党费传递过来
     */
    private String tradeNo;
    /**
     * 支付成功时间
     */
    private Date paySuccessTime;
    /**
     * 系统创建时间
     */
    private Date createTime;
    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户id
     */
    private Integer status;

    /**
     *  全额 单位分
     */
    private Long amount;


    public Date getPaySuccessTime() {
        return paySuccessTime;
    }

    public void setPaySuccessTime(Date paySuccessTime) {
        this.paySuccessTime = paySuccessTime;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }
}
