package com.goodsogood.collectionpay.model.response;

import com.alibaba.fastjson.JSON;

public class ProxyTaskCreateResp {

	protected int code;
	protected String message;

	public String getTaskId() {
		if (code == 0) {
			return JSON.parseObject(message).getString("id");
		}
		return null;
	}

	/**
	 * @return the code
	 */
	public int getCode() {
		return code;
	}

	/**
	 * @param code
	 *            the code to set
	 */
	public void setCode(int code) {
		this.code = code;
	}

	/**
	 * @return the message
	 */
	public String getMessage() {
		return message;
	}

	/**
	 * @param message
	 *            the message to set
	 */
	public void setMessage(String message) {
		this.message = message;
	}

}