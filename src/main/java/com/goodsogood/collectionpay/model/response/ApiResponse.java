package com.goodsogood.collectionpay.model.response;

public class ApiResponse<T> {

	public ApiResponse() {

	}

	public ApiResponse(T data) {
		this.data = data;
	}

	public ApiResponse(int code, String message, T data) {
		this.data = data;
		this.code = code;
		this.message = message;
	}

	private int code = 0;

	private String message = "success";

	private T data;

	/**
	 * @return the code
	 */
	public int getCode() {
		return code;
	}

	/**
	 * @param code
	 *            the code to set
	 */
	public void setCode(int code) {
		this.code = code;
	}

	/**
	 * @return the message
	 */
	public String getMessage() {
		return message;
	}

	/**
	 * @param message
	 *            the message to set
	 */
	public ApiResponse<T> setMessage(String message) {
		this.message = message;
		return this;
	}

	/**
	 * @return the data
	 */
	public T getData() {
		return data;
	}

	/**
	 * @param data
	 *            the data to set
	 */
	public void setData(T data) {
		this.data = data;
	}

}
