package com.goodsogood.collectionpay.model.response;

public class UnionpayOrderQueryResponse {

	private String respCode;
	private String respMsg;
	private String origRespCode;
	private String  origRespMsg;
	private String  queryId;
	
	/**
	 * @return the origRespMsg
	 */
	public String getOrigRespMsg() {
		return origRespMsg;
	}

	/**
	 * @param origRespMsg the origRespMsg to set
	 */
	public void setOrigRespMsg(String origRespMsg) {
		this.origRespMsg = origRespMsg;
	}

	/**
	 * @return the respCode
	 */
	public String getRespCode() {
		return respCode;
	}

	/**
	 * @param respCode
	 *            the respCode to set
	 */
	public void setRespCode(String respCode) {
		this.respCode = respCode;
	}

	/**
	 * @return the respMsg
	 */
	public String getRespMsg() {
		return respMsg;
	}

	/**
	 * @param respMsg
	 *            the respMsg to set
	 */
	public void setRespMsg(String respMsg) {
		this.respMsg = respMsg;
	}

	/**
	 * @return the origRespCode
	 */
	public String getOrigRespCode() {
		return origRespCode;
	}

	/**
	 * @param origRespCode
	 *            the origRespCode to set
	 */
	public void setOrigRespCode(String origRespCode) {
		this.origRespCode = origRespCode;
	}

	
	/**
	 * @return the queryId
	 */
	public String getQueryId() {
		return queryId;
	}

	/**
	 * @param queryId the queryId to set
	 */
	public void setQueryId(String queryId) {
		this.queryId = queryId;
	}

	public boolean isSuccess() {
		return "00".equals(this.respCode);
	}

}
