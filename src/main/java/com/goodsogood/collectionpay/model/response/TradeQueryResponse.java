package com.goodsogood.collectionpay.model.response;

import java.util.HashMap;
import java.util.Map;

import com.goodsogood.collectionpay.entity.CollectionDealerTradeEntity;
import com.goodsogood.collectionpay.util.DateTimeUtil;

public class TradeQueryResponse extends ApiResponse<Map<String, Object>>{
	
	public TradeQueryResponse(){
		
	}
	
	public TradeQueryResponse(CollectionDealerTradeEntity entity){
		Map<String, Object> data = new HashMap<>();
		data.put("tradeNo", entity.getTradeNo());
		data.put("amount", entity.getAmount());
		data.put("status", entity.getStatus());
		data.put("statusDes", entity.getStatusDes());
		data.put("subject", entity.getSubject());
		data.put("body", entity.getBody());
		data.put("channel", entity.getChannel());
		data.put("refundedAmount", entity.getRefundedAmount());
		data.put("channelTxnId", entity.getChannelTxnId());
        data.put("outTranNumber", entity.getOutTradeNo());
		data.put("outTradeNo", entity.getOutTradeNo());
		data.put("channelTxnId", entity.getChannelTxnId());
		data.put("createTime", DateTimeUtil.format(entity.getCreateTime()));
		data.put("payTime", DateTimeUtil.format(entity.getPayTime()));
		super.setData(data);
	}
	

}
