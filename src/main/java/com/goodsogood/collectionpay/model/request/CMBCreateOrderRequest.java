package com.goodsogood.collectionpay.model.request;

import com.goodsogood.collectionpay.consts.CMBConstant;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @describe 招行账单明细新增
 * @date 2020-06-19
 */
@Data
@NoArgsConstructor
public class CMBCreateOrderRequest  extends  CMBBaseRequest{


    //固定值：5102
    private String txcode;
    //用户ID
    private String opr_usr;
    //商户编号
    private String merch_id;
    //批次号
    private String merch_batch;
    //缴费编号
    private String cust_merch_id;
    //客户姓名
    private String cust_name;
    //客户手机号
    private String cust_phone;
    //客户邮箱
    private String cust_mail;
    //应缴金额（单位：元）
    private String fee_amt;
    //时间戳 /秒
    private String t;


    public static void main(String[] args) {
        CMBCreateOrderRequest cmbCreateOrderRequest = new CMBCreateOrderRequest();
        cmbCreateOrderRequest.setTxcode("5102");
        cmbCreateOrderRequest.setOpr_usr("JF007901");
        cmbCreateOrderRequest.setMerch_id("JF0079");
        cmbCreateOrderRequest.setMerch_batch("20200831015591123");
        cmbCreateOrderRequest.setCust_merch_id("1111");
        cmbCreateOrderRequest.setCust_name("李国勇2");
        cmbCreateOrderRequest.setCust_phone("18623052831");
        cmbCreateOrderRequest.setFee_amt("1.01");
        cmbCreateOrderRequest.setT(System.currentTimeMillis() + "");
        CMBRequest<CMBCreateOrderRequest> request = new CMBRequest<>(
                cmbCreateOrderRequest, CMBConstant.MERCH_ID, CMBConstant.DES_KEY,
                CMBConstant.RSA_PRIVATE_KEY, CMBConstant.RSA_PUBLIC_KEY);

        System.out.println(request.getResponse(String.class));

    }
}
