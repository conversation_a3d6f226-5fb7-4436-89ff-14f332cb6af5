package com.goodsogood.collectionpay.model.request;

import javax.validation.constraints.Pattern;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.util.StringUtils;

public class PageApiRequest extends ApiRequest {

	private final static Integer DEFAULT_PAGE_NO = 1;
	private final static Integer DEFAULT_PAGE_SIZE = 20;
	private final static Integer MAX_PAGE_SIZE = Integer.MAX_VALUE;
	
	private String sortField;
	
	private String sortDirection;

	@Pattern(regexp = "(^\\d+$)", message = "参数pageSize只能是数字")
	@Length(max=11,message="pageSize长度过大,最多11位.")
	@NotBlank(message="参数pageSize不能为空")
	private String pageSize;

	@Pattern(regexp = "(^\\d+$)", message = "参数pageNo只能是数字")
	@Length(max=11,message="pageNo长度过大,最多11位.")
	@NotBlank(message="参数pageNo不能为空")
	private String pageNo;
	

	/**
	 * @return the sortField
	 */
	public String getSortField() {
		return sortField;
	}

	/**
	 * @param sortField the sortField to set
	 */
	public void setSortField(String sortField) {
		this.sortField = sortField;
	}

	/**
	 * @return the sortDirection
	 */
	public String getSortDirection() {
		return sortDirection;
	}

	/**
	 * @param sortDirection the sortDirection to set
	 */
	public void setSortDirection(String sortDirection) {
		this.sortDirection = sortDirection;
	}

	/**
	 * @return the pageSize
	 */
	public String getPageSize() {
		if (StringUtils.isEmpty(pageSize))
			return DEFAULT_PAGE_SIZE.toString();
		return Integer.valueOf(pageSize).intValue() > MAX_PAGE_SIZE.intValue() ? MAX_PAGE_SIZE.toString() : pageSize;
	}

	/**
	 * @param pageSize
	 *            the pageSize to set
	 */
	public void setPageSize(String pageSize) {
		this.pageSize = pageSize;
	}

	/**
	 * @return the pageNo
	 */
	public String getPageNo() {
		if (StringUtils.isEmpty(pageNo) || Integer.valueOf(pageNo).intValue() < DEFAULT_PAGE_NO.intValue() )
			return DEFAULT_PAGE_NO.toString();
		return pageNo;
	}

	/**
	 * @param pageNo
	 *            the pageNo to set
	 */
	public void setPageNo(String pageNo) {
		this.pageNo = pageNo;
	}

}
