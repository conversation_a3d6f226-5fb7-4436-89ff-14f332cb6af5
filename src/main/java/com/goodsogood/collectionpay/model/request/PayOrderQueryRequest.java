package com.goodsogood.collectionpay.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

public class PayOrderQueryRequest {

    /**
     * 交纳人的信息
     */
    @JsonProperty(value = "user_name")
    private String userName;

    /**
     * 交纳金额
     */
    @JsonProperty(value = "amount")
    private Integer amount;

    /**
     * 交纳时间
     */
    @JsonProperty(value = "pay_date")
    private String  payDate;

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public String getPayDate() {
        return payDate;
    }

    public void setPayDate(String payDate) {
        this.payDate = payDate;
    }
}
