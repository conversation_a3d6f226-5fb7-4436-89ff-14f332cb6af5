package com.goodsogood.collectionpay.model.request;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

public class TradeRefundRequest extends ApiRequest{
	
	@NotEmpty(message = "缺少参数[tradeNo]")
	private String tradeNo;
	
	@NotEmpty(message = "缺少参数操作人[operator]")
	@Length(max=64,message="操作人[operator]最大长度64")
	private String operator;

	/**
	 * @return the tradeNo
	 */
	public String getTradeNo() {
		return tradeNo;
	}

	/**
	 * @param tradeNo the tradeNo to set
	 */
	public void setTradeNo(String tradeNo) {
		this.tradeNo = tradeNo;
	}

	/**
	 * @return the operator
	 */
	public String getOperator() {
		return operator;
	}

	/**
	 * @param operator the operator to set
	 */
	public void setOperator(String operator) {
		this.operator = operator;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		return "TradeRefundRequest [tradeNo=" + tradeNo + ", operator=" + operator + "]";
	}
	

}
