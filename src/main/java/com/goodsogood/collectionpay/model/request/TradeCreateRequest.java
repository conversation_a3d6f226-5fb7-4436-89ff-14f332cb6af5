package com.goodsogood.collectionpay.model.request;

import javax.validation.constraints.Pattern;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;
import org.hibernate.validator.constraints.URL;

import com.goodsogood.collectionpay.annotation.PositiveInt;

public class TradeCreateRequest extends ApiRequest {
	
	
	@NotEmpty(message = "缺少参数notifyUrl")
	@URL(message = "参数notifyUrl不是一个有效的URL")
	private String notifyUrl;

	@NotEmpty(message = "缺少参数 充值渠道[channel]")
	private String channel;
	
	private String userId;

	private String userName;

	private String mobile;


    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    /**
	 * @return the userId
	 */
	public String getUserId() {
		return userId;
	}

    public Long orgId;

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    /**
     * 支付方式 1.微信 2.支付宝
     */
    public Integer payType;

    public Integer getPayType() {
        return payType;
    }

    public void setPayType(Integer payType) {
        this.payType = payType;
    }

    /**
	 * @param userId the userId to set
	 */
	public void setUserId(String userId) {
		this.userId = userId;
	}

	@NotEmpty(message = "缺少参数 支付金额[amount]")
	@Pattern(regexp="^[1-9]{1,1}[0-9]{0,9}$",message="参数支付金额[amount]无效,只能为大于0的整数,最大长度10位")
	@PositiveInt(message="参数支付金额[amount]无效,取值范围[1~500000000]",max=500000000) 
	private String amount ;
	
	private String openId;
	

	@NotEmpty(message = "缺少参数[subject]")
	@Length(message="参数[subject]最大长度128字符",max=128)
	private String subject;
	
	
	//@NotEmpty(message = "缺少参数[outTradeNo]")
	@Length(message="参数[outTradeNo]最大长度32字符",max=32)
	@Pattern(regexp="^[0-9a-zA-Z_-]{1,}$",message="参数[outTradeNo]格式错误,只能出现数字、字母、-、_")
	private String outTradeNo;
	
	@NotEmpty(message = "缺少参数商品描述[body]")
	@Length(message="参数商品描述[body]最大长度100字",max=100)
	private String body;
	
	
	@URL(message="参数[returnUrl] 不是一个有效的URL")
	private String returnUrl;


	private String channelNew;

	public String getChannelNew() {
		return channelNew;
	}

	public void setChannelNew(String channelNew) {
		this.channelNew = channelNew;
	}

	/**
     * 加密的內容
     */
    private String ciphertext;

    /**
     * 加密的key
     */
    private String transferKey;


    /**
     * 017訂單號 由黨費生成
     */
    private String tradeNo;

    /**
     * 支付提交用户PC的Mac地址,使用数字货币时必填
     */
    private String mac;

    /**
     * 终端设备imei,使用数字货币时必填
     */
    private String imei;

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getCiphertext() {
        return ciphertext;
    }

    public void setCiphertext(String ciphertext) {
        this.ciphertext = ciphertext;
    }

    public String getTransferKey() {
        return transferKey;
    }

    public void setTransferKey(String transferKey) {
        this.transferKey = transferKey;
    }

    /**
	 * @return the returnUrl
	 */
	public String getReturnUrl() {
		return returnUrl;
	}


	/**
	 * @param returnUrl the returnUrl to set
	 */
	public void setReturnUrl(String returnUrl) {
		this.returnUrl = returnUrl;
	}


	/**
	 * @return the body
	 */
	public String getBody() {
		return body;
	}


	/**
	 * @param body the body to set
	 */
	public void setBody(String body) {
		this.body = body;
	}




	/**
	 * JSAPI 公众号支付
       NATIVE 扫码支付
        APP APP支付
	 */
	@Pattern(regexp = "^JSAPI$|^NATIVE$|^APP$", message = "无效的参数 微信支付类型[wxTradeType]")
	private String wxTradeType;
	
	//微信公众号app_id
	private String wxAppId;
	
	
	/**
	 * @return the wxAppId
	 */
	public String getWxAppId() {
		return wxAppId;
	}


	/**
	 * @param wxAppId the wxAppId to set
	 */
	public void setWxAppId(String wxAppId) {
		this.wxAppId = wxAppId;
	}


	/**
	 * @return the wxTradeType
	 */
	public String getWxTradeType() {
		return wxTradeType;
	}


	/**
	 * @param wxTradeType the wxTradeType to set
	 */
	public void setWxTradeType(String wxTradeType) {
		this.wxTradeType = wxTradeType;
	}


	/**
	 * @return the notifyUrl
	 */
	public String getNotifyUrl() {
		return notifyUrl;
	}


	/**
	 * @param notifyUrl the notifyUrl to set
	 */
	public void setNotifyUrl(String notifyUrl) {
		this.notifyUrl = notifyUrl;
	}


	/**
	 * @return the channel
	 */
	public String getChannel() {
		return channel;
	}


	/**
	 * @param channel the channel to set
	 */
	public void setChannel(String channel) {
		this.channel = channel;
	}

	/**

     * @return the amount
	 */
	public String getAmount() {
		return amount;
	}


	/**
	 * @param amount the amount to set
	 */
	public void setAmount(String amount) {
		this.amount = amount;
	}


	/**
	 * @return the openId
	 */
	public String getOpenId() {
		return openId;
	}


	/**
	 * @param openId the openId to set
	 */
	public void setOpenId(String openId) {
		this.openId = openId;
	}


	/**
	 * @return the subject
	 */
	public String getSubject() {
		return subject;
	}


	/**
	 * @param subject the subject to set
	 */
	public void setSubject(String subject) {
		this.subject = subject;
	}


	/**
	 * @return the outTradeNo
	 */
	public String getOutTradeNo() {
		return outTradeNo;
	}


	/**
	 * @param outTradeNo the outTradeNo to set
	 */
	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}


	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		return "TradeCreateRequest [notifyUrl=" + notifyUrl + ", channel=" + channel + ", userId=" + userId
				+ ", amount=" + amount + ", openId=" + openId + ", subject=" + subject + ", outTradeNo=" + outTradeNo
				+ ", body=" + body + ", returnUrl=" + returnUrl + ", wxTradeType=" + wxTradeType + ", wxAppId="
				+ wxAppId + "]";
	}

}
