package com.goodsogood.collectionpay.model.request;

import javax.validation.constraints.Pattern;

import org.hibernate.validator.constraints.Length;

public class TradeRecordQueryRequest extends PageApiRequest {

	@Length(max = 64, message = "参数单号[orderNo]长度最多64位.")
	private String orderNo;

	@Pattern(regexp = "^(\\d+[,])*(\\d+)$", message = "参数 状态[status]无效!")
	private String status;

	@Pattern(regexp = "(^\\d+$)", message = "参数 开始时间[beginTime]只能是数字")
	@Length(max = 10, message = "参数开始时间[beginTime]长度过大,最多10位.")
	private String beginTime;

	@Pattern(regexp = "(^\\d+$)", message = "参数 结束时间[endTime]只能是数字")
	@Length(max = 10, message = "参数结束时间[endTime]长度过大,最多10位.")
	private String endTime;
	
	// true :只查询 退款订单
	private boolean refundOrder;


	/**
	 * @param refundOrder the refundOrder to set
	 */
	public void setRefundOrder(boolean refundOrder) {
		this.refundOrder = refundOrder;
	}

	/**
	 * @return the orderNo
	 */
	public String getOrderNo() {
		return orderNo;
	}

	/**
	 * @param orderNo the orderNo to set
	 */
	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	/**
	 * @return the status
	 */
	public String getStatus() {
		return status;
	}

	/**
	 * @param status the status to set
	 */
	public void setStatus(String status) {
		this.status = status;
	}

	/**
	 * @return the beginTime
	 */
	public String getBeginTime() {
		return beginTime;
	}

	/**
	 * @param beginTime the beginTime to set
	 */
	public void setBeginTime(String beginTime) {
		this.beginTime = beginTime;
	}

	/**
	 * @return the endTime
	 */
	public String getEndTime() {
		return endTime;
	}

	/**
	 * @param endTime the endTime to set
	 */
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public boolean isRefundOrder() {
		// TODO Auto-generated method stub
		return refundOrder;
	}
	

}
