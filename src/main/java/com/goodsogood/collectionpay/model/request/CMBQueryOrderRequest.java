package com.goodsogood.collectionpay.model.request;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.goodsogood.collectionpay.consts.CMBConstant;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @describe 招行订单查询
 * @date 2020-06-19
 */
@Data
public class CMBQueryOrderRequest extends CMBBaseRequest{

        //固定值：5105
        private String txcode;
        //用户ID
        private String opr_usr;
        //商户编号
        private String merch_id;
        //批次号
        private String merch_batch;
        //批次号
        private String ext_merch_batch;
        //状态（A-全部，N-未缴费，Y-已缴费）
        private String status;
        //页数
        private String pag_nbr;
        //每页条数
        private String pag_siz;

        public static void main(String[] args) {
            CMBQueryOrderRequest cmbQueryOrderRequest = new CMBQueryOrderRequest();
            cmbQueryOrderRequest.setTxcode("5105");
            cmbQueryOrderRequest.setOpr_usr("JF007901");
            cmbQueryOrderRequest.setMerch_id("JF0079");
            cmbQueryOrderRequest.setMerch_batch("20200903015665");
//            cmbQueryOrderRequest.setExt_merch_batch("CMB20200902015656");
            cmbQueryOrderRequest.setStatus("A");
            cmbQueryOrderRequest.setPag_nbr("1");
            cmbQueryOrderRequest.setPag_siz("10");
            CMBRequest<CMBQueryOrderRequest> request = new CMBRequest<>(
            cmbQueryOrderRequest, CMBConstant.MERCH_ID,
            CMBConstant.DES_KEY, CMBConstant.RSA_PRIVATE_KEY,
            CMBConstant.RSA_PUBLIC_KEY);
            String verifyMapString = request.getResponse(String.class);
            Map<String, Object> mapStr = JSON.parseObject(JSON.toJSONString
                    (JSON.parseObject(verifyMapString), filter)).getInnerMap();
            System.out.println(mapStr);
        }

        private static PropertyFilter filter = (obj, s, v) -> {
            if (v instanceof String) {
                return !StringUtils.isEmpty(v);
            }
            return null != v;
        };
}
