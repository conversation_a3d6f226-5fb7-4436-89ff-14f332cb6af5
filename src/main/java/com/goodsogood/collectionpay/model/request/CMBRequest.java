package com.goodsogood.collectionpay.model.request;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.goodsogood.collectionpay.consts.ErrorCode;
import com.goodsogood.collectionpay.consts.LogConfiger;
import com.goodsogood.collectionpay.consts.SignType;
import com.goodsogood.collectionpay.exception.AssetServiceException;
import com.goodsogood.collectionpay.util.BeanUtil;
import com.goodsogood.collectionpay.util.EncryptUtil;
import com.goodsogood.collectionpay.util.HttpClientUtil;
import com.goodsogood.collectionpay.util.SignUtil;
import lombok.Getter;
import lombok.extern.java.Log;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.security.PublicKey;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @describe 招行请求公共request
 * @date 2020-06-24
 */
@Getter
@Log
public class CMBRequest<T> {

    static final Logger logger = LoggerFactory.getLogger(LogConfiger.APP);

    private T t;

    //商户参数
    private String id;

    private String url;

    private String descKey;

    private String aesKey;

    private String rsaPrivateKey;

    private String rsaPublicKey;


    public CMBRequest(T t, String id, String descKey, String rsaPrivateKey, String rsaPublicKey) {
        this.t = t;
        this.url = "http://yjf.cmbuat.com:8996/api/gateway/jfy";
        this.descKey = descKey;
        this.rsaPrivateKey = rsaPrivateKey;
        this.rsaPublicKey = rsaPublicKey;
        this.id = id;

        System.out.println("privatekey======="+rsaPrivateKey);
        System.out.println("publickey========"+rsaPublicKey);
    }

    public CMBRequest(T t, String id, String aesKey,
                      String descKey,
                      String rsaPrivateKey,
                      String rsaPublicKey,
                      String url) {
        this.t = t;
        this.url = url;
        this.descKey = descKey;
        this.aesKey=aesKey;
        this.rsaPrivateKey = rsaPrivateKey;
        this.rsaPublicKey = rsaPublicKey;
        this.id = id;
    }

    public <R> R getResponse(Class<R> rClass) {
        Map<String, String> orderParams = new HashMap<>();
        Map<String, Object> signMap = BeanUtil.transBean2Map(t);
        String jsonParams = JSON.toJSONString(t);
        orderParams.put("Val", EncryptUtil.encodeBase64(
                EncryptUtil.desEncrypt(jsonParams.getBytes(StandardCharsets.UTF_8), this.aesKey)
        ));
        //签名参数
        orderParams.put("Sign", SignUtil.getSignValue(signMap, SignType.RSA256, this.rsaPrivateKey));
        orderParams.put("Id", this.id);
        //调用接口
        String result = HttpClientUtil.postWithJson(this.url, JSON.toJSONString(orderParams));
        JSONObject json = JSON.parseObject(result);
        try {
            String sign = json.getString("Sign");
            String val = json.getString("Val");
            PublicKey publicKey = EncryptUtil.restorePublicKey(EncryptUtil.decodeBase64(
                    this.rsaPublicKey.getBytes(StandardCharsets.UTF_8)));
            String verifyMapString = new String(Objects.requireNonNull(EncryptUtil.desDecrypt(
                    EncryptUtil.decodeBase64(val.getBytes(StandardCharsets.UTF_8)), this.descKey)));
            Map<String, Object> mapStr = JSON.parseObject(JSON.toJSONString
                    (JSON.parseObject(verifyMapString), filter)).getInnerMap();
            boolean verify = EncryptUtil.verify256(SignUtil.getSortSignString(mapStr)
                            .getBytes(StandardCharsets.UTF_8),
                            SignUtil.hexStr2bytes(new String(EncryptUtil.
                                    decodeBase64(sign.getBytes(StandardCharsets.UTF_8)))), publicKey);
            JSONObject resultLast= JSON.parseObject(verifyMapString);
            //追加一个是验证通过
            resultLast.put("verify",verify?1:0);
            logger.info("调用接口生成数据结果,resultLast-{}",resultLast);
            return JSON.parseObject(resultLast.toJSONString(), rClass);
        }catch (Exception exception){
            logger.error("返回的json结果-{}",json);
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "招商银行解析请求失败。。。");
        }

    }

    private static PropertyFilter filter = (obj, s, v) -> {
        if (v instanceof String) {
            return !StringUtils.isEmpty(v);
        }
        return null != v;
    };
}
