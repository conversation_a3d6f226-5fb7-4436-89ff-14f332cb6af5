package com.goodsogood.collectionpay.model.request;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.goodsogood.collectionpay.consts.CMBConstant;
import com.goodsogood.collectionpay.consts.ErrorCode;
import com.goodsogood.collectionpay.exception.AssetServiceException;
import com.goodsogood.collectionpay.util.DateTimeUtil;
import com.goodsogood.collectionpay.util.SignUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.LocalDate;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @describe 招行账单上传
 * @date 2020-06-19
 */
@Data
@NoArgsConstructor
public class CMBCreateBatchOrderRequest  extends  CMBBaseRequest{
    //固定值：5101
    private String txcode;
    //用户ID
    private String opr_usr;
    //商户编号
    private String merch_id;
    //批次名称
    private String batch_name;
    //批次号（第一包不填，上传成功后会返回merch_batch，续传的包必填，用来标志包属于同一个批次）
    //批次号
    private String merch_batch;
    //外部批次号
    private String ext_merch_batch;
    //缴费开始日期（YYYYMMDD）
    private String begin_date;
    //缴费结束日期（YYYYMMDD）
    private String end_date;
    //总笔数
    private String sum_cnt;
    //总金额
    private String sum_amt;
    //本包笔数
    private String pkg_cnt;
    //本包金额
    private String pkg_amt;
    //本包序号
    private String pkg_num;
    //是否续传（Y-是，N-否）
    private String flag_on;
    //账单明细 ：账单明细，每条记录作为数组的一个值。 第一条为表头，从第二条起为数据记录，数据字段顺序与表头一致，每条数据之间用~^作为分隔符。数据记录定义如下：[‘缴费编号~^应缴金额~^姓名~^手机号~^邮箱~^证件号码’,’ABC001~^50.00~^张三~^13456875642~^<EMAIL>~^330111199909099999’]
    private String[] detail;
    //时间戳 /秒
    private String t;


    public static void main(String[] args) {
        String trNo= System.currentTimeMillis() + "";
        System.out.println(trNo);
        LocalDate localDate = LocalDate.now();
        CMBCreateBatchOrderRequest cmbCreateBatchOrderRequest = new CMBCreateBatchOrderRequest();
        cmbCreateBatchOrderRequest.setTxcode("5101");
        cmbCreateBatchOrderRequest.setOpr_usr("JF007901");
        cmbCreateBatchOrderRequest.setMerch_id("JF0079");
        cmbCreateBatchOrderRequest.setBatch_name("2020年8月缴纳党费");
        cmbCreateBatchOrderRequest.setExt_merch_batch("CMB"+DateTimeUtil.
                getFormatDateStr(new Date(),"yyyyMMddHHmmss"));
        cmbCreateBatchOrderRequest.setBegin_date(localDate.toString());
        cmbCreateBatchOrderRequest.setEnd_date(localDate.plusDays(1).toString());
        cmbCreateBatchOrderRequest.setSum_cnt("1");
        cmbCreateBatchOrderRequest.setSum_amt("1");
        cmbCreateBatchOrderRequest.setPkg_cnt("1");
        cmbCreateBatchOrderRequest.setPkg_amt("1");
        cmbCreateBatchOrderRequest.setPkg_num("1");
        cmbCreateBatchOrderRequest.setFlag_on("N");
        cmbCreateBatchOrderRequest.setDetail(new String[]{"缴费编号~^应缴金额~^姓名~^手机号~^明细流水号",
                "CMB001~^1.00~^张**~^186****2305~^"+trNo+""});
        cmbCreateBatchOrderRequest.setT(System.currentTimeMillis() + "");
        CMBRequest<CMBCreateBatchOrderRequest> request = new CMBRequest<>(cmbCreateBatchOrderRequest,
                CMBConstant.MERCH_ID, CMBConstant.DES_KEY,
                CMBConstant.RSA_PRIVATE_KEY, CMBConstant.RSA_PUBLIC_KEY);

        Map<String, Object> mapStr = JSON.parseObject(JSON.toJSONString
                (JSON.parseObject(request.getResponse(String.class)), filter)).getInnerMap();
        if(mapStr.get("verify").toString().equals("0")){
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "招商银行创建订单验答失败。。。");
        }
        String tranNumber = DateTimeUtil.getFormatDateStr(new Date(), "yyyyMMddHHmmss");
        Map<String, Object> wakePayInfo = getWakePayInfo(tranNumber, "1");
        System.out.println(wakePayInfo);

    }

    /**
     * 得到唤醒支付页面
     * @return
     */
    private static Map<String,Object> getWakePayInfo(String tranNumber,
                                              String amount) {
        Map<String, Object> mapStrNew = new HashMap<>();
        mapStrNew.put("dateTime", DateTimeUtil.getFormatDateStr(new Date(),"yyyyMMddHHmmss"));
        mapStrNew.put("branchNo", "0755");
        mapStrNew.put("merchantNo", "000054");
        mapStrNew.put("date", DateTimeUtil.getFormatDateStr(new Date(),"yyyyMMdd"));
        mapStrNew.put("orderNo", tranNumber);
        mapStrNew.put("amount", amount);
        //要小时完成支付
        mapStrNew.put("expireTimeSpan", "1200");
        mapStrNew.put("payNoticeUrl", "1.0");
        //把map所有参数
        String reqData= SignUtil.getSortSignString(mapStrNew);
        StringBuilder strToSign = new StringBuilder(reqData);
        try {
            strToSign.append("&").append(CMBConstant.RSA_PRIVATE_KEY);
            MessageDigest messageDigest = MessageDigest.getInstance("SHA-256");
            messageDigest.update(strToSign.toString().getBytes(StandardCharsets.UTF_8));
            byte[] byteBuffer= messageDigest.digest();
            String sign=HexString(byteBuffer);
            mapStrNew.put("sign", sign);
        }catch (Exception exception){
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "getWakePayInfo唤醒支付失败。。。");
        }
        mapStrNew.put("version", "1.0");
        mapStrNew.put("charset", "UTF-8");
        mapStrNew.put("signType", "SHA-256");
        return mapStrNew;
    }

    /**
     * byte数组 转换成 16进制小写字符串
     */
    public static String HexString(byte[] bytes) {
        StringBuffer sb = new StringBuffer();
        for(int i = 0; i < bytes.length; i++) {
            String hex = Integer.toHexString(bytes[i] & 0xFF);
            if(hex.length() < 2){
                sb.append(0);
            }
            sb.append(hex);
        }
        return sb.toString();
    }

    private static PropertyFilter filter = (obj, s, v) -> {
        if (v instanceof String) {
            return !StringUtils.isEmpty(v);
        }
        return null != v;
    };

}
