package com.goodsogood.collectionpay.model.request;

import javax.validation.constraints.Pattern;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import com.goodsogood.collectionpay.annotation.PositiveInt;

public abstract class ApiRequest {
	
	@Pattern(regexp="^[0-9a-zA-Z_-]{1,}$",message="参数[appId]格式错误,只能出现数字、字母、-、_")
    //@NotEmpty(message = "缺少参数[appId]")
	@Length(max=64,message="参数[appId]最大长度64")
	private String appId ;
	
	@NotEmpty(message = "缺少参数渠道id[dealerId]")
	@Pattern(regexp="^[1-9]{1,1}[0-9]{0,9}$",message="参数[dealerId]无效,只能为大于0的整数,最大长度10字符")
	@PositiveInt(message="参数渠道id[dealerId]无效,超过最大取值范围[1~2147483647]")
	private String dealerId;
	
	
	/**
	 * @return the appId
	 */
	public String getAppId() {
		return appId;
	}


	/**
	 * @param appId the appId to set
	 */
	public void setAppId(String appId) {
		this.appId = appId;
	}


	/**
	 * @return the dealerId
	 */
	public String getDealerId() {
		return dealerId;
	}


	/**
	 * @param dealerId the dealerId to set
	 */
	public void setDealerId(String dealerId) {
		this.dealerId = dealerId;
	}


}
