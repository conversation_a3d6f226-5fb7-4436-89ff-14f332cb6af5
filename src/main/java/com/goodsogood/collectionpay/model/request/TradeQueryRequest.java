package com.goodsogood.collectionpay.model.request;

import javax.validation.constraints.Pattern;

import org.hibernate.validator.constraints.NotEmpty;

public class TradeQueryRequest extends ApiRequest{
	
	
	//@NotEmpty(message = "缺少参数[tradeNo]")
	private String tradeNo;
	
	
	@Pattern(regexp="^[0-9a-zA-Z_-]{1,}$",message="参数[outTradeNo]格式错误,只能出现数字、字母、-、_")
	private String outTradeNo;
	

	/**
	 * @return the outTradeNo
	 */
	public String getOutTradeNo() {
		return outTradeNo;
	}

	/**
	 * @param outTradeNo the outTradeNo to set
	 */
	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}

	/**
	 * @return the tradeNo
	 */
	public String getTradeNo() {
		return tradeNo;
	}

	/**
	 * @param tradeNo the tradeNo to set
	 */
	public void setTradeNo(String tradeNo) {
		this.tradeNo = tradeNo;
	}
	
	

}
