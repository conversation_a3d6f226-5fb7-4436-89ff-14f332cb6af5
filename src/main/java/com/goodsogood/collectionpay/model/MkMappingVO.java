package com.goodsogood.collectionpay.model;

public class MkMappingVO {

    /**
     * 麦卡映射ID
     */
    private String mkMappingId;
    /**
     * 渠道APPID
     */
    private String channelAppId;
    
    /**
     * 麦卡用户ID
     */
    private String mkUserId;

    public String getMkMappingId() {
        return mkMappingId == null ? "" : mkMappingId;
    }

    public void setMkMappingId(String mkMappingId) {
        this.mkMappingId = mkMappingId;
    }

    public String getMkUserId() {
        return mkUserId == null ? "" : mkUserId;
    }

    public void setMkUserId(String mkUserId) {
        this.mkUserId = mkUserId;
    }

    public String getChannelAppId() {
        return channelAppId == null ? "" : channelAppId;
    }

    public void setChannelAppId(String channelAppId) {
        this.channelAppId = channelAppId;
    }

    @Override
    public String toString() {
        return "MkMappingVO [mkMappingId=" + mkMappingId + ", channelAppId=" + channelAppId + ", mkUserId=" + mkUserId
                + "]";
    }
    
}
