package com.goodsogood.collectionpay.model;

import javax.persistence.Column;
import java.util.List;

public class PayPageConfigVo {

    /**
     * 订单编号 党费传递过来
     */
    private String name;
    /**
     * 支付成功时间
     */
    private String code;
    /**
     * 支付图标地址
     */
    private String icon;


    @Column(name="is_available")
    private Integer isAvailable;

    public Integer getIsAvailable() {
        return isAvailable;
    }

    public void setIsAvailable(Integer isAvailable) {
        this.isAvailable = isAvailable;
    }

    /**
     * 下级菜单
     */
    private List<PayPageConfigVo> subList;


    /**
     * 排序
     */
    private Integer order;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public List<PayPageConfigVo> getSubList() {
        return subList;
    }

    public void setSubList(List<PayPageConfigVo> subList) {
        this.subList = subList;
    }
}
