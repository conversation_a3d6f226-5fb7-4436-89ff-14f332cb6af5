package com.goodsogood.collectionpay.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class WeChatPayTransaction {
    @JsonProperty("id")
    private String id;

    @JsonProperty("create_time")
    private Date createTime;

    @JsonProperty("resource_type")
    private String resourceType;

    @JsonProperty("event_type")
    private String eventType;

    @JsonProperty("summary")
    private String summary;

    @JsonProperty("resource")
    private EncryptedResource resource;

}

@Data
class EncryptedResource {
    @JsonProperty("original_type")
    private String originalType;

    @JsonProperty("algorithm")
    private String algorithm;

    @JsonProperty("ciphertext")
    private String ciphertext;

    @JsonProperty("associated_data")
    private String associatedData;

    @JsonProperty("nonce")
    private String nonce;

}
