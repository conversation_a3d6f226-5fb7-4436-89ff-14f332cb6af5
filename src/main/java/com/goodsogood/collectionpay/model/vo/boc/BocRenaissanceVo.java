package com.goodsogood.collectionpay.model.vo.boc;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * 中行复兴号支付文档接入
 */
@Data
public class BocRenaissanceVo {

    @JSONField(name = "asyncNotifyUrl")
    private String asyncNotifyUrl;

    @JSONField(name = "prodNo")
    private String prodNo;

    @JSONField(name = "orderAmount")
    private String orderAmount;

    @JSONField(name = "bankAccountId")
    private String bankAccountId;

    @JSONField(name = "orderDetails")
    private List<OrderDetail> orderDetails;

    @JSONField(name = "signValue")
    private String signValue;

    @JSONField(name = "signType")
    private String signType;

    @JSONField(name = "wxAppId")
    private String wxAppId;

    @JSONField(name = "userFlags")
    private String userFlags;

    /**
     * X2数字人民币
     * C1 微信支付
     * B 支付宝
     * A 中国银行
     */
    @JSONField(name = "channel")
    private String channel;

    /**
     * 用户选择的渠道
     */
    @JSONField(name = "userChooseChannel")
    private String userChooseChannel;

    /**
     * 支付系统 返回给前端信息 1.标识复兴一号中行支付
     */
    @JSONField(name = "gs_flag")
    private Integer gsFlag;


    //订单详细信息
    @Data
    public static class OrderDetail {
        @JSONField(name = "itemSeq")
        private int itemSeq;

        @JSONField(name = "itemId")
        private String itemId;

        @JSONField(name = "itemAmount")
        private String itemAmount;

    }

}
