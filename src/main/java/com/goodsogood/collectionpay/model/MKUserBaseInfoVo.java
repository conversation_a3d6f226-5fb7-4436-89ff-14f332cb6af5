package com.goodsogood.collectionpay.model;

/**
 * 用户基础数据
 * 
 * <AUTHOR>
 *
 */
public class MKUserBaseInfoVo {

    private String userId;
    private String realName;
    private String loginName;
    private String identityCard;
    private String headImgUrl;
    private String mobilePhone;
    private String nation;
    private String nativePlaceCode;
    private String nativePlaceName;
    private String country;
    private Integer createDate;
    private String isIdentityVerify;
    private String isMobileVerify;
    private String sex;
    private String birthdayMonthDay;
    private String birthday;
    private String memo;
    private String userIdNjr;  //娘家人的userId
    private String realNameAuthenticated;
    private String perfectDataStatus;// 完善资料状态：1 已完善 0 未完善

    private Integer channel; //渠道
    private String appRegionSource; //APP所在地区
    public String getUserId() {
        return userId == null ? "" : userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getRealName() {
        return realName == null ? "" : realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getLoginName() {
        return loginName == null ? "" : loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getIdentityCard() {
        return identityCard == null ? "" : identityCard;
    }

    public void setIdentityCard(String identityCard) {
        this.identityCard = identityCard;
    }

    public String getHeadImgUrl() {
        return headImgUrl == null ? "" : headImgUrl;
    }

    public void setHeadImgUrl(String headImgUrl) {
        this.headImgUrl = headImgUrl;
    }

    public String getMobilePhone() {
        return mobilePhone == null ? "" : mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getNation() {
        return nation == null ? "" : nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getNativePlaceCode() {
        return nativePlaceCode == null ? "" : nativePlaceCode;
    }

    public void setNativePlaceCode(String nativePlaceCode) {
        this.nativePlaceCode = nativePlaceCode;
    }

    public String getNativePlaceName() {
        return nativePlaceName == null ? "" : nativePlaceName;
    }

    public void setNativePlaceName(String nativePlaceName) {
        this.nativePlaceName = nativePlaceName;
    }

    public String getCountry() {
        return country == null ? "" : country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public Integer getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Integer createDate) {
        this.createDate = createDate;
    }

    public String getIsIdentityVerify() {
        return isIdentityVerify == null ? "" : isIdentityVerify;
    }

    public void setIsIdentityVerify(String isIdentityVerify) {
        this.isIdentityVerify = isIdentityVerify;
    }

    public String getIsMobileVerify() {
        return isMobileVerify == null ? "" : isMobileVerify;
    }

    public void setIsMobileVerify(String isMobileVerify) {
        this.isMobileVerify = isMobileVerify;
    }

    public String getSex() {
        return sex == null ? "" : sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getBirthdayMonthDay() {
        return birthdayMonthDay == null ? "" : birthdayMonthDay;
    }

    public void setBirthdayMonthDay(String birthdayMonthDay) {
        this.birthdayMonthDay = birthdayMonthDay;
    }

    public String getBirthday() {
        return birthday == null ? "" : birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getMemo() {
        return memo == null ? "" : memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getUserIdNjr() {
        return userIdNjr == null ? "" : userIdNjr;
    }

    public void setUserIdNjr(String userIdNjr) {
        this.userIdNjr = userIdNjr;
    }

    public String getRealNameAuthenticated() {
        return realNameAuthenticated == null ? "" : realNameAuthenticated;
    }

    public void setRealNameAuthenticated(String realNameAuthenticated) {
        this.realNameAuthenticated = realNameAuthenticated;
    }

    public String getPerfectDataStatus() {
        return perfectDataStatus == null ? "" : perfectDataStatus;
    }

    public void setPerfectDataStatus(String perfectDataStatus) {
        this.perfectDataStatus = perfectDataStatus;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public String getAppRegionSource() {
        return appRegionSource == null ? "" : appRegionSource;
    }

    public void setAppRegionSource(String appRegionSource) {
        this.appRegionSource = appRegionSource;
    }
}
