package com.goodsogood.collectionpay.model.bank;

import com.icbc.api.internal.util.fastjson.annotation.JSONField;
import com.icbc.api.request.AggregatePaymentRequestV1;

public class ICBCAggregatePaymentRequestV1 extends AggregatePaymentRequestV1.AggregatePaymentRequestV1Biz {

        @JSONField(name = "pay_limit")
        private String payLimit;


        @JSONField(name = "install_times")
        private String installTimes;

        @JSONField(name = "return_url")
        private String returnUrl;

        @JSONField( name = "goods_body")
        private String goodsBody;

        @JSONField( name = "goods_detail")
        private String goodsDetail;

        @JSONField(name = "order_amount")
        private String orderAmount;

        public String getPayLimit() {
            return payLimit;
        }

        public void setPayLimit(String payLimit) {
            this.payLimit = payLimit;
        }

        public String getInstallTimes() {
            return installTimes;
        }

        public void setInstallTimes(String installTimes) {
            this.installTimes = installTimes;
        }

        public String getReturnUrl() {
            return returnUrl;
        }

        public void setReturnUrl(String returnUrl) {
            this.returnUrl = returnUrl;
        }

        public String getGoodsBody() {
            return goodsBody;
        }

        public void setGoodsBody(String goodsBody) {
            this.goodsBody = goodsBody;
        }

        public String getGoodsDetail() {
            return goodsDetail;
        }

        public void setGoodsDetail(String goodsDetail) {
            this.goodsDetail = goodsDetail;
        }

        public String getOrderAmount() {
            return orderAmount;
        }

        public void setOrderAmount(String orderAmount) {
            this.orderAmount = orderAmount;
        }
}
