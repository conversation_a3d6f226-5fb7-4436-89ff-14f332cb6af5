package com.goodsogood.collectionpay.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class SxPayInfoVo {

    /**
     * 交易流水号
     */
    @JsonProperty(value = "trade_no")
    private String tradeNo;


    /**
     * 支付金额
     */
    @JsonProperty(value = "pay_money")
    private String payMoney;

    /**
     * 支付状态
     */
    @JsonProperty(value = "status")
    private Long status;



}
