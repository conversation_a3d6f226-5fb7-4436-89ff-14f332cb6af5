package com.goodsogood.collectionpay.model;

/**
 * 商城订单Vo
 */
public class MallOrderVo {

    /**
     * 外部订单系统编号
     * 必传
     */
    private String outTranNumber;

    /**
     * 业务系统用户Id 有就传
     */
    private Long userId=-9999L;

    /**
     * 用户名称
     * 必传
     */
    private String subject;

    /**
     * 必传
     * 下单大概内容 比如谁买了什么东西
     */
    private String body;

    /**
     * 必传
     * 下单金额 单位为分 比如 1元就100分
     */
    private Integer amount;

    /**
     * 必传
     *  回调通知地址
     */
    private String notifyUrl;
    /**
     *  能获取就传 不能就不传
     */
    private String openId;


    public String getOutTranNumber() {
        return outTranNumber;
    }

    public void setOutTranNumber(String outTranNumber) {
        this.outTranNumber = outTranNumber;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }
}
