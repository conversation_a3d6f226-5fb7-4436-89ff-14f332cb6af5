package com.goodsogood.collectionpay.api;

import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import com.alibaba.fastjson.JSON;
import com.goodsogood.collectionpay.consts.LogConfiger;

@Aspect  
@Component  
public class WebLogAspect {
	
	private static final Logger logger = LoggerFactory.getLogger(LogConfiger.APP);

	/**
	 * 定义一个切入点. 解释下：
	 *
	 * ~ 第一个 * 代表任意修饰符及任意返回值. ~ 第二个 * 任意包名 ~ 第三个 * 代表任意方法. ~ 第四个 * 定义在web包或者子包 ~
	 * 第五个 * 任意方法 ~ .. 匹配任意数量的参数.
	 */
	@Pointcut("execution(public * com.goodsogood.collectionpay.api.controller..*.*(..))")
	public void webLog() {
	}

/*	@Before("webLog()")
	public void doBefore(JoinPoint joinPoint) {
		StringBuilder str= new StringBuilder() ;
		str.append("request:\n");
		// 接收到请求，记录请求内容
		ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		HttpServletRequest request = attributes.getRequest();
		// 记录下请求内容
		str.append("URL : " + request.getRequestURL().toString());
		str.append("\nHTTP_METHOD : " + request.getMethod());
		str.append("\nHTTP_HEADERS : " + getHeaders(request));
		str.append("\nIP : " + request.getRemoteAddr());
		str.append("\nCLASS_METHOD : " + joinPoint.getSignature().getDeclaringTypeName() + "."
				+ joinPoint.getSignature().getName());
		str.append("\nARGS : " + getArgs(request));
		logger.info(str.toString());
	}*/

	@AfterReturning(pointcut = "webLog()", returning = "object") // 打印输出结果
	public void doAfterReturning(Object object) {
		if(object instanceof java.lang.String){
			logger.info("Response ={}",object);
		}
		else{
			logger.info("Response ={}",JSON.toJSONString(object));
		}
		// 处理完请求，返回内容
	}
	
	private String getHeaders(HttpServletRequest request){
		 Enumeration<String> headers = request.getHeaderNames();
		 Map<String,String> map = new HashMap<>();
		 while(headers.hasMoreElements()){
			String name = headers.nextElement();
			map.put(name, request.getHeader(name));
		 }
		return JSON.toJSONString(map,true);
	}
	
	private String getArgs(HttpServletRequest request){
		 Enumeration<String> headers = request.getParameterNames();
		 Map<String,String> map = new HashMap<>();
		 while(headers.hasMoreElements()){
			String name = headers.nextElement();
			map.put(name, request.getParameter(name));
		 }
		return JSON.toJSONString(map,true);
	}
	

}
