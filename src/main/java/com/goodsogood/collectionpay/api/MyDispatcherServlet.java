package com.goodsogood.collectionpay.api;

import java.io.PrintWriter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.UnsatisfiedServletRequestParameterException;
import org.springframework.web.servlet.DispatcherServlet;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

import com.alibaba.fastjson.JSON;
import com.goodsogood.collectionpay.consts.ErrorCode;
import com.goodsogood.collectionpay.consts.LogConfiger;
import com.goodsogood.collectionpay.exception.AssetServiceException;
import com.goodsogood.collectionpay.model.response.ApiResponse;

@Configuration
@EnableWebMvc
public class MyDispatcherServlet extends DispatcherServlet {
	private static final long serialVersionUID = 91546431331753718L;
	private static final Logger logger = LoggerFactory.getLogger(LogConfiger.APP);

	@SuppressWarnings("unused")
	@Override
	protected ModelAndView processHandlerException(HttpServletRequest request, HttpServletResponse response,
			Object handler, Exception ex) throws Exception {
		String uri = request.getRequestURI();
		ApiResponse<String> content = null;

		if (ex instanceof BindException) {
			BindException be = (BindException) ex;
			content = new ApiResponse<String>();
			content.setCode(Integer.valueOf(ErrorCode.INVALID_PARAMETER));
			content.setMessage(be.getBindingResult().getAllErrors().get(0).getDefaultMessage());
		} else if (ex instanceof AssetServiceException) {
			AssetServiceException ae = (AssetServiceException) ex;
			content = new ApiResponse<String>();
			content.setCode(Integer.valueOf(ae.getErrCode()));
			content.setMessage(ae.getErrMsg());
		} else if (ex instanceof UnsatisfiedServletRequestParameterException) {
			logger.warn("没有指定的接口  method={}", request.getParameter("method"));
			content = new ApiResponse<String>();
			content.setCode(Integer.valueOf(ErrorCode.NO_SUCH_INTERFACE_SUPPORTED));
			content.setMessage("没有指定的接口!");
			// response.setStatus(400);
		} else if (ex instanceof HttpRequestMethodNotSupportedException) {
			HttpRequestMethodNotSupportedException be = (HttpRequestMethodNotSupportedException) ex;
			content = new ApiResponse<String>();
			content.setCode(Integer.valueOf(ErrorCode.HTTP_REQUEST_METHOD_NOT_SUPPORTED));
			content.setMessage("不支持的Http Method:" + be.getMethod() + "!");
			// response.setStatus(400);
		} else {
			logger.error(ex.getMessage(), ex);
			content = new ApiResponse<String>();
			content.setCode(Integer.valueOf(ErrorCode.SERVICE_UNAVAILABLE));
			content.setMessage("系统繁忙,请稍后再试!");
			response.setStatus(503);
		}

		String json = JSON.toJSONString(content, true);
		logger.debug("Response JSON={}", json);
		PrintWriter printWriter = response.getWriter();
		printWriter.println(json);
		printWriter.flush();
		return null;
	}

}
