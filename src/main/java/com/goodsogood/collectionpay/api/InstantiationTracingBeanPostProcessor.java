package com.goodsogood.collectionpay.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import com.goodsogood.collectionpay.consts.LogConfiger;

@Component
public class InstantiationTracingBeanPostProcessor implements ApplicationListener<ContextRefreshedEvent> {

    private final static Logger logger = LoggerFactory.getLogger(LogConfiger.APP);

    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (event.getApplicationContext().getParent() == null) {
            logger.info("********************spring parent ini start********************");
            ApplicationContext ac=event.getApplicationContext();
            String[] beanNameArray=ac.getBeanDefinitionNames();
            for (int i = 0; i < beanNameArray.length; i++) {
                logger.info("parent  bean【{}】 init ok",beanNameArray[i]);
            }
            logger.info("********************spring parent ini end   ********************");
        } else {
            logger.info("*********************spring child ini start*********************");
            ApplicationContext ac=event.getApplicationContext();
            String[] beanNameArray=ac.getBeanDefinitionNames();
            for (int i = 0; i < beanNameArray.length; i++) {
                logger.info("mvc bean 【{}】 init ok",beanNameArray[i]);
            }
            logger.info("********************spring child ini end    ********************");
        }
    }
}
