package com.goodsogood.collectionpay.api;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import com.goodsogood.collectionpay.interceptor.AuthInterceptor;
import com.goodsogood.collectionpay.interceptor.LogInterceptor;

@Configuration
public class MyWebAppConfigurer 
        extends WebMvcConfigurerAdapter {
	
	
	 @Bean
	  public LogInterceptor logInterceptor() {
	    return new LogInterceptor();
	  }
	 
	 @Bean
	  public AuthInterceptor authInterceptor() {
		 return  new AuthInterceptor();
	  }
	
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 多个拦截器组成一个拦截器链
        // addPathPatterns 用于添加拦截规则
        // excludePathPatterns 用户排除拦截
       /// registry.addInterceptor(new MyInterceptor1()).addPathPatterns("/**");
       // registry.addInterceptor(new MyInterceptor2()).addPathPatterns("/**");
    	
    	registry.addInterceptor( logInterceptor()).addPathPatterns("/**");
    	registry.addInterceptor( authInterceptor()).addPathPatterns("/**");
        super.addInterceptors(registry);
    }

}