package com.goodsogood.collectionpay.api.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.goodsogood.collectionpay.annotation.ClearAll;
import com.goodsogood.collectionpay.consts.ApiMethod;
import com.goodsogood.collectionpay.consts.ErrorCode;
import com.goodsogood.collectionpay.consts.LogConfiger;
import com.goodsogood.collectionpay.dao.CollectionDealerTradeDao;
import com.goodsogood.collectionpay.dao.support.PageResult;
import com.goodsogood.collectionpay.entity.CollectionDealerTradeEntity;
import com.goodsogood.collectionpay.exception.AssetServiceException;
import com.goodsogood.collectionpay.model.MallOrderVo;
import com.goodsogood.collectionpay.model.request.*;
import com.goodsogood.collectionpay.model.response.ApiResponse;
import com.goodsogood.collectionpay.model.response.ExportResponse;
import com.goodsogood.collectionpay.model.response.TradeCreateResponse;
import com.goodsogood.collectionpay.model.response.TradeQueryResponse;
import com.goodsogood.collectionpay.service.DigitalServices;
import com.goodsogood.collectionpay.service.PayService;
import com.goodsogood.collectionpay.util.Asserts;
import com.goodsogood.collectionpay.util.DESCoderUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

@RestController
//@RequestMapping("/collection-pay/service")
@RequestMapping(value = {"/collection-pay/service","/ctpay/collection-pay/service"})
public class PayController {

	private static final Logger logger = LoggerFactory.getLogger(LogConfiger.APP);
	
	@Autowired
	private CollectionDealerTradeDao collectionDealerTradeDao ;
	
	@Autowired
	private PayService payService;

    @Autowired
    private DigitalServices digitalServices;

	
	@RequestMapping(params = "method=" + ApiMethod.TRADE_CREATE)
	public TradeCreateResponse tradeCreate(@Validated TradeCreateRequest param,
                                           HttpServletRequest req, HttpServletResponse resp) throws Exception {

		String wxTradeType = param.getWxTradeType();
		if (param.getChannel().startsWith("ICBC_WX_") && StringUtils.isEmpty(param.getReturnUrl())) {
			throw new AssetServiceException(ErrorCode.MISSING_PARAMETER, "缺少参数[returnUrl]");
		}
		if (param.getChannel().startsWith("ABC_WX_")) {
			// trade_type=JSAPI时（即公众号支付），此参数必传
			if ("JSAPI".equals(wxTradeType) && StringUtils.isEmpty(param.getOpenId())) {
				throw new AssetServiceException(ErrorCode.MISSING_PARAMETER, "缺少参数公众号[openid]");
			}
			if ("JSAPI".equals(wxTradeType) && StringUtils.isEmpty(param.getWxAppId())) {
				throw new AssetServiceException(ErrorCode.MISSING_PARAMETER, "缺少参数[wxAppId]");
			}
		}
		return payService.tradeCreate(param,req,resp);
	}
	
	
	@RequestMapping(params = "method=" + ApiMethod.TRADE_QUERY)
	public TradeQueryResponse tradeQuery(@Validated TradeQueryRequest param) {

		Asserts.bizCheck(!StringUtils.isEmpty(param.getOutTradeNo()) || !StringUtils.isEmpty(param.getTradeNo()),
				"缺少参数[tradeNo]");

		if (!StringUtils.isEmpty(param.getTradeNo())) {
			CollectionDealerTradeEntity order = this.collectionDealerTradeDao
					.findByTradeNoAndDealerId(param.getTradeNo(), Integer.valueOf(param.getDealerId()));
			Asserts.bizCheck(order != null, "订单不存在!");
			return new TradeQueryResponse(order);
		} else {
			List<CollectionDealerTradeEntity> order = this.collectionDealerTradeDao
					.findByOutTradeNoAndDealerId(param.getOutTradeNo(), Integer.valueOf(param.getDealerId()));

			Asserts.bizCheck(order.size() > 0, "订单不存在!");
			return new TradeQueryResponse(order.get(0));
		}
	}

    /**
     * 手动调用接口 更新订单状态
     * 用于银行回调没有回调没有成功
     * @return
     */
    @RequestMapping(params = "method=" + ApiMethod.MANUAL_UPDATE)
    public Object manualUpdate(@RequestParam(value = "tran_nums",required = false) List<String> tranNums,
                               @RequestParam(value = "start_time",required = false)  String  startTime,
                               @RequestParam(value = "end_time",required = false)   String   endTime,
                               @RequestParam(value = "channel_code",required = false)   String  channelCode,
                               @RequestParam(value = "flag",defaultValue = "1")   int   flag){
        //按照时间来查询单子情况
        if(flag==1) {
            List<Map<String, Object>> list = this.payService.queryUnPayOrders( startTime,endTime,channelCode);
            for (Map<String, Object> each : list) {
                String orderNo = each.get("trade_no").toString();
                try {
                    this.payService.updateTradeStatus(orderNo);
                } catch (Exception e) {
                    logger.error("TradeStatusUpdateJob err! orderNo=" + orderNo, e);
                }
            }
        } //按照时间查询所有单子
        if(flag==2) {
            List<Map<String, Object>> list = this.payService.queryAllPayOrders( startTime,endTime,channelCode);
            for (Map<String, Object> each : list) {
                String orderNo = each.get("trade_no").toString();
                try {
                    this.payService.updateTradeStatus(orderNo);
                } catch (Exception e) {
                    logger.error("TradeStatusUpdateJob err! orderNo=" + orderNo, e);
                }
            }
        }else {
            for (String orderNo : tranNums) {
                try {
                    logger.info("订单号--{}，手动开始处理", orderNo);
                    this.payService.updateTradeStatus(orderNo);
                    logger.info("订单号--{}，手动开始完成", orderNo);
                } catch (Exception e) {
                    logger.error("订单号-{}，手动处理出错,错误信息-{}", orderNo, e.getMessage());
                    return new ApiResponse<Map<String, Object>>().setMessage("操作失败" + e.getMessage());
                }
            }
        }
        return null;
    }

	@RequestMapping(params = "method=" + ApiMethod.TRADE_REFUND)
	public Object tradeRefund(@Validated TradeRefundRequest param){
		this.payService.refundMark(param);
		return new ApiResponse<Map<String, Object>>().setMessage("操作成功");
	}
	
	@RequestMapping(params = "method=" + ApiMethod.TRADE_LIST_QUERY)
	public Object tradeList(@Validated TradeRecordQueryRequest param){
		PageResult<Map<String, Object>> page = this.payService.queryTradeList(param);
		return new ApiResponse<PageResult<Map<String, Object>>>(page);
	}
	
	@RequestMapping(params = "method=" + ApiMethod.TRADE_LIST_EXPORT)
	public ExportResponse tradeList(@Validated TradeRecordExportRequest param) throws Exception{
		return this.payService.tradeListExport(param);
	}

    /**
     * 管理后台调用接口
     */
    @RequestMapping("/queryPayOrder")
    @ResponseBody
    @ClearAll
    public TradeCreateResponse queryPayOrder(  @RequestParam(value = "user_name") String userName,
                                               @RequestParam(value = "amount")  Integer  amount,
                                               @RequestParam(value = "pay_date")   String   payDate ){
        TradeCreateResponse resp = new TradeCreateResponse();
        PayOrderQueryRequest payOrderQueryRequest = new PayOrderQueryRequest();
        payOrderQueryRequest.setUserName(userName);
        payOrderQueryRequest.setAmount(amount);
        payOrderQueryRequest.setPayDate(payDate);
        List<String> list = this.payService.queryPayOrder( payOrderQueryRequest );
        String tradeNo = CollUtil.join(list, ",");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("tradeNo", tradeNo);
        resp.setData(parameters);
        return resp;
    }


    /**
     * 中行数字人民币 银行发送查询商户侧账户信息请求
     * @return
     * @throws Exception
     */
    @RequestMapping("/bind/queryMerAccountInfo")
    public void queryMerAccountInfo(HttpServletRequest request,
                                    HttpServletResponse response,
                                    @RequestParam(value = "channel_code",defaultValue = "BOC_DIGITAL_1")
                                    String channelCode) throws Exception{
         this.digitalServices.queryMerAccountInfo(channelCode,request,response);
    }


    /**
     * 银行发送Token推送请求
     * @return
     * @throws Exception
     */
    @RequestMapping("/bind/pushToken")
    public void pushToken(@RequestParam(value = "channel_code",defaultValue = "BOC_DIGITAL_1") String channelCode,
                           HttpServletRequest request,
                           HttpServletResponse response) throws Exception{
        this.digitalServices.pushToken(channelCode,request,response);
    }

    /**
     * 支付页面配置
     * @return
     * @throws Exception
     */
    @RequestMapping("/pay_page_config")
    public void payPageConfig(@RequestParam(value = "channel_code") String channelCode,
                          HttpServletRequest request,
                          HttpServletResponse response) throws Exception{
        this.payService.payPageConfig(channelCode,request,response);
    }

    /**
     * 支付配置
     * @return
     * @throws Exception
     */
    @PostMapping("/pay")
    public TradeCreateResponse pay(@RequestParam(value = "transfer_key") String transferKey,
                    @RequestParam(value = "ciphertext") String ciphertext,
                    @RequestParam(value = "code") String code,
                    @RequestParam(value = "open_id") String openId,
                    HttpServletRequest request,
                    HttpServletResponse response) throws Exception{
        try {
            ciphertext = URLEncoder.encode(ciphertext,"utf-8") ;
            String decode = DESCoderUtil.decode(transferKey, ciphertext);
            MallOrderVo mallOrderVo = JSON.parseObject(decode, MallOrderVo.class);
            logger.info("pay接口返回数据------{}",mallOrderVo);
            TradeCreateRequest param = new TradeCreateRequest();
            param.setChannel(code);
            param.setSubject(mallOrderVo.getSubject());
            param.setBody(mallOrderVo.getBody());
            param.setChannel(code);
            param.setNotifyUrl(mallOrderVo.getNotifyUrl());
            param.setOutTradeNo(mallOrderVo.getOutTranNumber());
            param.setAmount(""+mallOrderVo.getAmount());
            param.setOpenId(openId);
            //写死的参数
            param.setDealerId("1");
            if(!StringUtils.isEmpty(openId)) {
                param.setWxTradeType("JSAPI");
            }
            return payService.tradeCreate(param,null,null);
        }catch (Exception e){
            logger.info("pay发生异常", e);
            throw new AssetServiceException(ErrorCode.BUSINESS_ERROR, "支付异常！");
        }
    }


    /**
     * 跑指定时间的单子 回调党费系统
     */
    @GetMapping("/runNoPay")
    public String runNoPayOrder(@RequestParam(value = "tran_number") String tranNumber) {
        payService.runPayNoPay( tranNumber );
        return "SUC";
    }

    public static void main(String[] args) {
        String content = "501";
        String pattern = "500$|([1234]?\\d\\d?$){1,3}";
        boolean isMatch = Pattern.matches(pattern, content);
        System.out.println("字符串中是否包含了 'runoob' 子字符串? " + isMatch);
    }

}
