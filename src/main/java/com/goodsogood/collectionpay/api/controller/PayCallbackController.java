package com.goodsogood.collectionpay.api.controller;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.goodsogood.collectionpay.annotation.ClearAll;
import com.goodsogood.collectionpay.consts.LogConfiger;
import com.goodsogood.collectionpay.consts.RedisKey;
import com.goodsogood.collectionpay.dao.CollectionDealerTradeDao;
import com.goodsogood.collectionpay.redis.RedisPool;
import com.goodsogood.collectionpay.service.*;
import com.goodsogood.collectionpay.util.XMLUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import redis.clients.jedis.Jedis;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * 支付回调
 */
@Controller
@ClearAll
@RequestMapping(value = {"/collection-pay/callback","/ctpay/collection-pay/callback"})
public class PayCallbackController {
    
    static final Logger logger = LoggerFactory.getLogger(LogConfiger.APP);
    
    @Autowired
    private RedisPool redisPool ;
     
    @Autowired
    private PayService payService ;
    
    @Autowired
	private ABCBankService ABCBankService;
    
    @Autowired
    private SybPayService sybPayService;
    
    @Autowired
    private AlipayService alipayService;

    @Autowired
    private CCBService ccbService;
    
	@Autowired
	private AssetDepositChannelConfService assetDepositChannelConfService ;
	
	@Autowired
	private CollectionDealerTradeDao collectionDealerTradeDao ;

    @Autowired
    private MerchantsServices merchantsServices;

    @Autowired
    private ICBCBankService icbcBankService;

    @Autowired
    private SanXianService sanXianService;
    @Autowired
    private WeiXinH5Services xinH5Services;

    @Autowired
    private NotifyService notifyService;

    @Autowired
    private BocRenaissanceServices bocRenaissanceServices;
	
	@RequestMapping("/redirect")
	@ClearAll
	public void redirect(HttpServletRequest request,HttpServletResponse response) throws Exception {
		String orderId = request.getParameter("orderId");
		Jedis jedis = null;
		 String form  ="";
		try{
			 jedis = redisPool.getResource();
			 form = jedis.get(RedisKey.COMPANY_RECHARGE_ALIPAY_FORM +orderId);
			 response.setContentType("text/html");
			 response.setCharacterEncoding("utf-8");
			 PrintWriter pw = response.getWriter();
			 if(form==null){
				 form="<b>未找到待付款订单,请重试!</b>";
			 }
			 String html="<!DOCTYPE html> <body> "+form+"</body></html>";
			 pw.write(html);
			 System.out.println("html="+html);
			 pw.flush(); 
		}finally {
			if(jedis!=null){
				redisPool.returnResource(jedis);
			}
		}
	}

    @RequestMapping("/redirect_page")
    @ClearAll
    public void redirectPage(HttpServletRequest request,HttpServletResponse response) throws Exception {
        String orderId = request.getParameter("orderId");
        Jedis jedis = null;
        try{
            jedis = redisPool.getResource();
            String form = jedis.get(RedisKey.COMPANY_RECHARGE_ALIPAY_FORM +orderId);
            response.setContentType("text/html");
            response.setCharacterEncoding("utf-8");
            PrintWriter pw = response.getWriter();
            if(form==null){
                form="<b>未找到待付款订单,请重试!</b>";
                String html="<!DOCTYPE html> <body> "+form+"</body></html>";
                pw.write(html);
                return;
            }
            response.sendRedirect(form);
        }finally {
            if(jedis!=null){
                redisPool.returnResource(jedis);
            }
        }
    }
    
    /**
     * 解析参数
     */
    @SuppressWarnings({ "unused", "rawtypes" })
	private static Map<String, String> parseRequestToMap(HttpServletRequest request) throws Exception {
    	Map<String,String> params = new HashMap<>();
		Map requestParams = request.getParameterMap();
		for (Iterator iter = requestParams.keySet().iterator(); iter.hasNext();) {
            String name = (String) iter.next();
            String[] values = (String[]) requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i]
                        : valueStr + values[i] + ",";
            }
            //乱码解决，这段代码在出现乱码时使用。如果mysign和sign不相等也可以使用这段代码转化
            //valueStr = new String(valueStr.getBytes("ISO-8859-1"), "gbk");
            params.put(name, valueStr);
        }
		return params;
    }

    /**
     * 解析参数
     */
//    private static Map<String, String> parseRequestToMapNew(HttpServletRequest request) throws Exception {
//        Map<String, String[]> requestParams = request.getParameterMap();
//        Map<String, String> params = new HashMap<>();
//        // 将前台的参数转换为"xxx"->"aaa,bbb"的格式存入params中,实际上回调传来的参数每个key都只对应一个value
//        for (Map.Entry<String, String[]> entry: requestParams.entrySet()) {
//            String key = entry.getKey();
//            String values[] = entry.getValue();
//            StringBuilder valStr = new StringBuilder();
//            for (int i = 0; i < values.length; i ++) {
//                if ( i != values.length - 1) {
//                    valStr.append(values[i]).append(",");
//                } else {
//                    valStr.append(values[i]);
//                }
//            }
//            params.put(key, valStr.toString());
//        }
//        return params;
//    }
    
    
    /**
     * 银联回调
     * @throws Exception 
     */
    @ResponseBody
    @RequestMapping(value="/unionpay",method = RequestMethod.POST)
    @ClearAll
    public String unionpayCallback(HttpServletRequest request) throws Exception {
    	Map<String, String> params = parseRequestToMap(request);
    	logger.info("银联回调参数 {}",JSON.toJSONString(params));
    	return payService.uninonpayNotify(params);
    }
    
    /**
     * 微信回调
     */
    @ResponseBody
    @ClearAll
    @RequestMapping(value="/weixin",method = RequestMethod.POST, produces={"application/xml"})
    public String callback(@RequestBody String filter) {/*HttpServletRequest request, HttpServletResponse response) {*/
    	String returnString = "<xml>" + 
    							"<return_code>" + "<![CDATA[FAIL]]>" + "</return_code>" + 
    							"<return_msg>"+ "<![CDATA[OK]]>" + "</return_msg>" + 
    						  "</xml>";
        try {
            logger.info("pay.callback.weixin.requestStr-->" + filter);
        	
        	XMLUtil xmlUtil = new XMLUtil();
            Map<String, String> parseData;
            try {
            	parseData = xmlUtil.parseXml(filter);
            } catch (Exception e) {
                e.printStackTrace();
                return returnString;
            }
			
			if(parseData == null) {
				return returnString;
			}
			
			String return_code = parseData.get("return_code"); //返回状态码 	 SUCCESS/FAIL
			if (!return_code.equalsIgnoreCase("SUCCESS")) {
				//通信失败
				return returnString;
			}
			String status =payService.wxCallBack(parseData);///payCallbackService.wxCallBack(parseData);
			return "<xml>" + 
					"<return_code>" + "<![CDATA[" + status + "]]>" + "</return_code>" + 
					"<return_msg>"+ "<![CDATA[OK]]>" + "</return_msg>" + 
				  "</xml>";
        } catch(Exception e){
            logger.error("PayCallbackWeixinController.weixin happen error : ", e);
            return returnString;
        }
    }
    
    
    /**
     * 中国工商银行-公众号聚合支付
     * @return
     * @throws Exception
     */
	@RequestMapping(value="/ICBC_WX",method = RequestMethod.POST)
	@ResponseBody
	@ClearAll
	public String ICBCWXNotifyRecev(HttpServletRequest request) throws Exception {
		Map<String, String> params = parseRequestToMap(request);
    	logger.info("中国工商银行-公众号聚合支付回调参数 {}",JSON.toJSONString(params));
    	return this.payService.icbcNotifyHandle(params,request.getRequestURI());
	}

    /**
     * 中国工商银行-聚富通支付
     * @return
     * @throws Exception
     */
    @RequestMapping(value="/ICBC_JFT",method = RequestMethod.POST)
    @ResponseBody
    @ClearAll
    public String ICBCJFTNotifyRecev(HttpServletRequest request) throws Exception {
        Map<String, String> params = parseRequestToMap(request);
        logger.info("中国工商银行-聚富通支付回调参数 {}",JSON.toJSONString(params));
        return this.payService.ICBCJFTNotifyRecev(params,request.getRequestURI());
    }

    /**
     * 中国工商银行-公众号聚合支付
     * @return
     * @throws Exception
     */
    @RequestMapping(value="/CBC_WX",method = RequestMethod.POST)
    @ResponseBody
    @ClearAll
    public String CBCWXNotifyRecev(HttpServletRequest request) throws Exception {
        Map<String, String> params = parseRequestToMap(request);
        logger.info("中国工商银行-公众号聚合支付回调参数 {}",JSON.toJSONString(params));
        return this.payService.icbcNotifyHandle(params,request.getRequestURI());
    }
	
	
    /**
     * 中国农业银行网上支付平台-公众号支付
     * @return
     * @throws Exception
     */
	@RequestMapping(value="/ABC_WX")
	@ResponseBody
	@ClearAll
	public String ABCNotifyRecev(HttpServletRequest request) throws Exception {
		Map<String, String> params = parseRequestToMap(request);
    	logger.info("中国农业银行网上支付平台-公众号支付回调参数 {}",JSON.toJSONString(params));
    	return this.ABCBankService.recevPayResultNotify(params);
	}
	
    /**
     * 通联收银宝-公众号支付
     * @return
     * @throws Exception
     */
	@RequestMapping(value="/ALLINPAY_WX")
	@ResponseBody
	@ClearAll
	public String allinpayNotifyRecev(HttpServletRequest request) throws Exception {
		Map<String, String> params = parseRequestToMap(request);
    	logger.info("通联收银宝-公众号支付回调参数 {}",JSON.toJSONString(params));
    	return this.sybPayService.recevPayResultNotify(params);
	}
	
	/**
	 * 支付宝-通用
	 * @param request
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value="/alipay_common")
	@ResponseBody
	@ClearAll 
	public String alipayCommonNotifyRecev(HttpServletRequest request) throws Exception {
		Map<String, String> params = parseRequestToMap(request);
		//如果使用RSA2 一定清除sign_type
        params.remove("sign_type");
    	logger.info("支付宝-通用 回调参数 {}",JSON.toJSONString(params));
        logger.info("params:{}",params.toString());
    	return this.alipayService.notifyHandle(params);
	}


    /**
     * 支付宝-H5回调处理
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping(value="/alipay_common_h5")
    @ResponseBody
    @ClearAll
    public String alipayCommonNotifyReceiveH5(HttpServletRequest request) throws Exception {
        Map<String, String> params = parseRequestToMap(request);
        //如果使用RSA2 一定清除sign_type
        params.remove("sign_type");
        logger.info("支付宝H5-通用 回调参数 {}",JSON.toJSONString(params));
        logger.info("params:{}",params.toString());
        return this.alipayService.notifyHandleH5(params);
    }

    /**
     * 支付宝-回调处理
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping(value="/alipay_handler")
    @ResponseBody
    @ClearAll
    public String alipayCommonNotifyHandler(HttpServletRequest request) throws Exception {
        Map<String, String> config = assetDepositChannelConfService.findOptionsByChannel("ALIPAYWEB_AI");
        Map<String, String> params = parseRequestToMap(request);
        //如果使用RSA2 一定清除sign_type
        logger.info("支付宝-通用 回调参数 {}",JSON.toJSONString(params));
        logger.info("params:{}",params.toString());
        String code = params.get("app_auth_code");
        Integer result = this.alipayService.authToken(code, config);
        if(result>0) {
            return "Success";
        }else {
            return "Fail";
        }
    }

    /**
     * 建行数字人民币回调接口
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping(value="/cbc_digital")
    @ResponseBody
    @ClearAll
    public String cbcDigitalNotify(HttpServletRequest request,HttpServletResponse response) throws Exception {
        Map<String, String> params = parseRequestToMap(request);
        String notifyURLParam = request.getQueryString();
        logger.info("cbc_digital回调的参数为：{}",notifyURLParam);
        return ccbService.cbcDigitalNotify(params,notifyURLParam,response);
    }


    /**
     * 中行数字人民币回调接口
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping(value="/boc_digital")
    @ResponseBody
    @ClearAll
    public String bocDigitalNotify(HttpServletRequest request,HttpServletResponse response) throws Exception {
        Map<String, String> params = parseRequestToMap(request);
        String notifyURLParam = request.getQueryString();
        logger.info("cbc_digital回调的参数为：{}",notifyURLParam);
        return ccbService.cbcDigitalNotify(params,notifyURLParam,response);
    }

    /**
     * 招
     * 行数字人民币回调接口
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping(value="/merch_digital")
    @ResponseBody
    @ClearAll
    public void merchDigitalNotify(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map<String, String> params = parseRequestToMap(request);
        //解析参数
        String bizContent=params.get("biz_content");
        Map<String, String> mapBizContent = JSON.parseObject(bizContent, Map.class);
        String notifyURLParam = request.getQueryString();
        logger.info("merch_digital回调的参数为：{}",notifyURLParam);
        merchantsServices.merchDigitalNotify(mapBizContent,params,notifyURLParam,response);
    }

    /**
     * 工行新微信支付接口文档
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping(value="/icbc_h5pay")
    @ResponseBody
    @ClearAll
    public void icbcH5PayNotify(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map<String, String> params = parseRequestToMap(request);
        icbcBankService.icbcH5PayNotify(params,response);
    }

    /**
     * @param request
     * @return
     * @throws Exception
     * 建行H5回调
     */
    @RequestMapping(value="/cbc_h5pay")
    @ResponseBody
    @ClearAll
    public void ccbH5PayNotify(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map<String, String> params = parseRequestToMap(request);
        ccbService.ccbH5PayNotify(params,response);
    }


    /**
     * @param request
     * @return
     * @throws Exception
     * 三峡付App付款成功过后回调
     */
    @RequestMapping(value="/sx_pay")
    @ResponseBody
    @ClearAll
    public void sxPayPayNotify(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map<String, String> params = parseRequestToMap(request);
        sanXianService.sxPayPayNotify(params,response);
    }

    /**
     * 微信回调
     */
    @RequestMapping(value = "/wx-h5-notify/{channel}", method = RequestMethod.POST)
    @ResponseBody
    @ClearAll
    public void wxNotifyCommonHandler(HttpServletRequest request,
                                      HttpServletResponse response,
                                      @RequestBody Map<String,Object> map,
                                      @PathVariable String channel) {
        logger.info("wxNotifyCommonHandler的body结果={}",JSONUtil.toJsonStr(map));
        xinH5Services.wxH5Notify(request, response, map, channel);
    }


    /**
     * 中行复兴一号接收主动回调
     */
    @RequestMapping(value = "/boc_renaissance", method = RequestMethod.POST, produces = {"application/text"})
    public String bocRenaissance(@RequestBody String filter) {
        logger.info("bocRenaissance回调的参数为：{}",filter);
        Map bean = JSONUtil.toBean(filter, Map.class);
        return bocRenaissanceServices.handleNotifyOrder(bean);
    }



}
