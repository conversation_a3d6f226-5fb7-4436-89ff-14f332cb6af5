package com.goodsogood.collectionpay.api;

import java.util.ArrayList;
import java.util.List;

public class RequestInfo {
	private String requestId;
	private String appId;
	private String method = "";
	private long requestTime = System.currentTimeMillis();
	private List<String> allowedIp = new ArrayList<>();
	private String requestUri;
	private String appName;

	/**
	 * @return the appName
	 */
	public String getAppName() {
		return appName;
	}

	/**
	 * @param appName
	 *            the appName to set
	 */
	public void setAppName(String appName) {
		this.appName = appName;
	}

	private long gateWayReqId;

	/**
	 * @return the requestUri
	 */
	public String getRequestUri() {
		return requestUri;
	}

	/**
	 * @param requestUri
	 *            the requestUri to set
	 */
	public void setRequestUri(String requestUri) {
		this.requestUri = requestUri;
	}

	/**
	 * @return the requestTime
	 */
	public long getRequestTime() {
		return requestTime;
	}

	public List<String> getAllowedIp() {
		return allowedIp;
	}

	public String getRequestId() {
		return requestId;
	}

	/**
	 * @param requestId
	 *            the requestId to set
	 */
	public void setRequestId(String requestId) {
		this.requestId = requestId;
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getMethod() {
		return method;
	}

	public void setMethod(String method) {
		this.method = method;
	}

	/**
	 * @return the gateWayReqId
	 */
	public long getGateWayReqId() {
		return gateWayReqId;
	}

	/**
	 * @param gateWayReqId
	 *            the gateWayReqId to set
	 */
	public void setGateWayReqId(long gateWayReqId) {
		this.gateWayReqId = gateWayReqId;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		return "RequestInfo [requestId=" + requestId + ", appId=" + appId + ", method=" + method + ", requestTime="
				+ requestTime + ", allowedIp=" + allowedIp + ", requestUri=" + requestUri + ", appName=" + appName
				+ ", gateWayReqId=" + gateWayReqId + "]";
	}

}
