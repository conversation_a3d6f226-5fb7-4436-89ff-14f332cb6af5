package com.goodsogood.collectionpay.annotation;

import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;


/**
 * 标识接口所需要哪些权限
 * @Description
 * <AUTHOR>
 * @time 2017年7月14日下午7:40:45
 */
@Retention(RUNTIME)
@Target({ TYPE, METHOD })
public @interface Permission {
	String scope();
}
