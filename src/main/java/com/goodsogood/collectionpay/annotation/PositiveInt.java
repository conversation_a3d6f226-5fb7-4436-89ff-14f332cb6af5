package com.goodsogood.collectionpay.annotation;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import javax.validation.Constraint;
import javax.validation.Payload;

import com.goodsogood.collectionpay.validator.PositiveIntValidator;

/**
 * 正整数 
 * @Descriptionpositive integer
 * <AUTHOR>
 * @time 2018年1月4日上午10:37:45
 */
@Target({FIELD})  
@Retention(RUNTIME)  
@Constraint(validatedBy = PositiveIntValidator.class)  
@Documented  
//
public @interface PositiveInt {
	String message() default "{PositiveInt}";
	Class<?>[] groups() default {};
	Class<? extends Payload>[] payload() default {};
	int max() default Integer.MAX_VALUE;
}