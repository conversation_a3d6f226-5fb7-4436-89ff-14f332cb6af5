package com.goodsogood.collectionpay.annotation;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import javax.validation.Constraint;
import javax.validation.Payload;

import com.goodsogood.collectionpay.validator.MoneyValidator;

/**
 * hibernate.validator的 扩展
 * @Descriptionpositive 
 * <AUTHOR>
 * @time 2018年1月4日上午10:37:45
 */
@Target({FIELD})  
@Retention(RUNTIME)  
@Constraint(validatedBy = MoneyValidator.class)  
@Documented  
//大于0 的数字
public @interface Money {
	String message() default "{Money}";
	Class<?>[] groups() default {};
	Class<? extends Payload>[] payload() default {};
}