package com.goodsogood.collectionpay.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 用于清除controller前的所有拦截器拦截 。
 * @Description
 * <AUTHOR>
 * @time 2017年6月20日上午10:13:16
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ClearAll {
}