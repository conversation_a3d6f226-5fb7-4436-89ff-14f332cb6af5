package com.goodsogood.collectionpay.job;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.goodsogood.collectionpay.consts.LogConfiger;
import com.goodsogood.collectionpay.service.PayService;

@Component
@EnableScheduling
@Lazy(false)
public class TradeStatusUpdateJob {

	private static final Logger logger = LoggerFactory.getLogger(LogConfiger.APP);
	
	@Autowired
	private PayService payService;


	@Scheduled(cron = "0 0/2 * * * ?")
	public void run() throws IOException {
		logger.info("TradeStatusUpdateJob!");
		List<Map<String, Object>> list = this.payService.queryUnPaymentOrders();
		for(Map<String, Object> each:list){
			String orderNo = each.get("trade_no").toString();
			try {
				this.payService.updateTradeStatus(orderNo);
			} catch (Exception e) {
				logger.error("TradeStatusUpdateJob err! orderNo="+orderNo,e);
			}
		}

	}

}
