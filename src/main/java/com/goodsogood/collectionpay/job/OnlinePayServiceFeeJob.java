package com.goodsogood.collectionpay.job;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.goodsogood.collectionpay.consts.LogConfiger;
import com.goodsogood.collectionpay.service.PayService;

/**
 * 第三方支付的账单下载
 * @Description   
 * <AUTHOR>
 * @time 2019年2月11日上午10:11:06
 */
@Component
@EnableScheduling
@Lazy(false)
public class OnlinePayServiceFeeJob {
	
	private static final Logger logger = LoggerFactory.getLogger(LogConfiger.APP);
	
	@Autowired
	private PayService payService ;
	
    
//	/**
//	 * 每日13点更新订单手续费金额
//	 * @throws Exception
//	 */
//	@Scheduled(cron = "0 0 13 * * ?")
//	public void run() throws Exception {
//		logger.info("OnlinePayServiceFeeJob!");
//		//this.payService.updateOrderServiceFee();
//	}

}
