package com.goodsogood.collectionpay.entity;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name="gs_collection_alipay_config")
public class AssetDepositAlipayConfEntity {

    @Id
    private int id;

    @Column(name="org_id")
    private Long orgId;

    @Column(name="org_name")
    private String orgName;


    @Column(name="app_id")
    private String appId;

    @Column(name="account_id")
    private String accountId;

    @Column(name="app_auth_token")
    private String appAuthToken;

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    @Column(name="auth_time")
    private Date authTime;


    @Column(name="create_time")
    private Date createTime;



    @Column(name="update_time")
    private Date updateTime;

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppAuthToken() {
        return appAuthToken;
    }

    public void setAppAuthToken(String appAuthToken) {
        this.appAuthToken = appAuthToken;
    }

    public Date getAuthTime() {
        return authTime;
    }

    public void setAuthTime(Date authTime) {
        this.authTime = authTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Id
    public int getId() {
        return id;
    }
}
