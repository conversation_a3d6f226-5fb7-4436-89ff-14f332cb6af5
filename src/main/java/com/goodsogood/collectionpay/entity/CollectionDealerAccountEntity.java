package com.goodsogood.collectionpay.entity;

import java.io.Serializable;
import javax.persistence.*;
import java.util.Date;


/**
 * The persistent class for the gs_collection_dealer_account database table.
 * 
 */
@Entity
@Table(name="gs_collection_dealer_account")
public class CollectionDealerAccountEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	@Id
	@Column(name="dealer_id")
	private int dealerId;

	private Long balance;

	@Column(name="create_time")
	private Date createTime;

	@Column(name="dealer_name")
	private String dealerName;

	@Column(name="outer_id")
	private String outerId;

	private int status;

	@Column(name="unionpay_num")
	private Long unionpayNum;

	@Column(name="unionpay_sum")
	private Long unionpaySum;

	@Column(name="update_time")
	private Date updateTime;
    @Version
	private Long version;

	@Column(name="wxpay_num")
	private Long wxpayNum;

	@Column(name="wxpay_sum")
	private Long wxpaySum;

	public CollectionDealerAccountEntity() {
	}

	public int getDealerId() {
		return this.dealerId;
	}

	public void setDealerId(int dealerId) {
		this.dealerId = dealerId;
	}

	public Long getBalance() {
		return this.balance;
	}

	public void setBalance(Long balance) {
		this.balance = balance;
	}

	public Date getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getDealerName() {
		return this.dealerName;
	}

	public void setDealerName(String dealerName) {
		this.dealerName = dealerName;
	}

	public String getOuterId() {
		return this.outerId;
	}

	public void setOuterId(String outerId) {
		this.outerId = outerId;
	}

	public int getStatus() {
		return this.status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public Long getUnionpayNum() {
		return this.unionpayNum;
	}

	public void setUnionpayNum(Long unionpayNum) {
		this.unionpayNum = unionpayNum;
	}

	public Long getUnionpaySum() {
		return this.unionpaySum;
	}

	public void setUnionpaySum(Long unionpaySum) {
		this.unionpaySum = unionpaySum;
	}

	public Date getUpdateTime() {
		return this.updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Long getVersion() {
		return this.version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public Long getWxpayNum() {
		return this.wxpayNum;
	}

	public void setWxpayNum(Long wxpayNum) {
		this.wxpayNum = wxpayNum;
	}

	public Long getWxpaySum() {
		return this.wxpaySum;
	}

	public void setWxpaySum(Long wxpaySum) {
		this.wxpaySum = wxpaySum;
	}

}