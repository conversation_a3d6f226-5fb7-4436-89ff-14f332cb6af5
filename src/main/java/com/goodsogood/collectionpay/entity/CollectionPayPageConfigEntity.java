package com.goodsogood.collectionpay.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name="gs_collection_paypage_config")
public class CollectionPayPageConfigEntity {

    @Id
    private int id;

    @Column(name="name")
    private String name;


    @Column(name="code")
    private String code;

    @Column(name="icon")
    private String icon;

    @Column(name="channel_code")
    private String channelCode;


    @Column(name="callback_url")
    private String callbackUrl;

    /**
     * 排序
     */
    @Column(name="`order`")
    private Integer order;

    @Column(name="parent_id")
    private Integer parentId;


    @Column(name="is_available")
    private Integer isAvailable;


    public int getIsAvailable() {
        return isAvailable;
    }

    public void setIsAvailable(int isAvailable) {
        this.isAvailable = isAvailable;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }



    public void setOrder(Integer order) {
        this.order = order;
    }

    public int getParentId() {
        return parentId;
    }

    public void setParentId(Integer parent_id) {
        this.parentId = parentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }


    public void setId(int id) {
        this.id = id;
    }

    public Integer getOrder() {
        return order;
    }

    public void setIsAvailable(Integer isAvailable) {
        this.isAvailable = isAvailable;
    }
}
