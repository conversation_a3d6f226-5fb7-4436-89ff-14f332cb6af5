package com.goodsogood.collectionpay.entity;

import java.io.Serializable;
import javax.persistence.*;
import java.util.Date;


/**
 * The persistent class for the gs_collection_dealer_account_log database table.
 * 
 */
@Entity
@Table(name="gs_collection_dealer_account_log")
public class CollectionDealerAccountLogEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	@Id
	@Column(name="log_id")
	private String logId;

	private String action;

	private Long amount;

	private String channel;

	@Column(name="channel_txn_id")
	private String channelTxnId;

	private String content;

	@Column(name="create_time")
	private Date createTime;

	@Column(name="dealer_balance")
	private Long dealerBalance;

	@Column(name="dealer_id")
	private int dealerId;

	@Column(name="IN_OR_OUT")
	private String inOrOut;

	@Column(name="open_id")
	private String openId;

	@Column(name="trade_no")
	private String tradeNo;

	public CollectionDealerAccountLogEntity() {
	}

	public String getLogId() {
		return this.logId;
	}

	public void setLogId(String logId) {
		this.logId = logId;
	}

	public String getAction() {
		return this.action;
	}

	public void setAction(String action) {
		this.action = action;
	}

	public Long getAmount() {
		return this.amount;
	}

	public void setAmount(Long amount) {
		this.amount = amount;
	}

	public String getChannel() {
		return this.channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public String getChannelTxnId() {
		return this.channelTxnId;
	}

	public void setChannelTxnId(String channelTxnId) {
		this.channelTxnId = channelTxnId;
	}

	public String getContent() {
		return this.content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public Date getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Long getDealerBalance() {
		return this.dealerBalance;
	}

	public void setDealerBalance(Long dealerBalance) {
		this.dealerBalance = dealerBalance;
	}

	public int getDealerId() {
		return this.dealerId;
	}

	public void setDealerId(int dealerId) {
		this.dealerId = dealerId;
	}

	public String getInOrOut() {
		return this.inOrOut;
	}

	public void setInOrOut(String inOrOut) {
		this.inOrOut = inOrOut;
	}

	public String getOpenId() {
		return this.openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public String getTradeNo() {
		return this.tradeNo;
	}

	public void setTradeNo(String tradeNo) {
		this.tradeNo = tradeNo;
	}

}