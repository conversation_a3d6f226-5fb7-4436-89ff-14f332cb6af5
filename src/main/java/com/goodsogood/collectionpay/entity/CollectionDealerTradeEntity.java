package com.goodsogood.collectionpay.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Version;

/**
 * The persistent class for the gs_collection_dealer_trade database table.
 * 
 */
@Entity
@Table(name = "gs_collection_dealer_trade")
public class CollectionDealerTradeEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	@Id
	@Column(name = "trade_no")
	private String tradeNo;

	@Column(name = "out_trade_no")
	private String outTradeNo;

	@Column(name = "USER_ID")
	private String userId;


    @Column(name = "org_id")
    private Long orgId;

	private Long amount;

	private String attach;

	private String body;

	private String channel;

	@Column(name = "channel_code")
	private String channelCode;

	@Column(name = "channel_txn_id")
	private String channelTxnId;

	@Column(name = "create_time")
	private Date createTime;

	@Column(name = "dealer_id")
	private int dealerId;

	@Column(name = "open_id")
	private String openId;

	@Column(name = "pay_time")
	private Date payTime;

	@Column(name = "prepay_id")
	private String prepayId;

	@Column(name = "status_des")
	private String statusDes;

	@Column(name = "txn_time")
	private String txnTime;

	@Column(name = "notify_url")
	private String notifyUrl;

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    @Column(name = "refund_time")
	private Date refundTime;

	@Column(name = "refund_operator")
	private String refundOperator;
	
	@Column(name = "service_fee")
	private Integer serviceFee;
	
	@Column(name = "refundedAmount")
	private Long refundedAmount;

	@Version
	private Long version;

	private int status;

	private String subject;

	public CollectionDealerTradeEntity() {
	}

	
	/**
	 * @return the serviceFee
	 */
	public Integer getServiceFee() {
		return serviceFee;
	}


	/**
	 * @param serviceFee the serviceFee to set
	 */
	public void setServiceFee(Integer serviceFee) {
		this.serviceFee = serviceFee;
	}


	/**
	 * @return the refundOperator
	 */
	public String getRefundOperator() {
		return refundOperator;
	}

	/**
	 * @param refundOperator
	 *            the refundOperator to set
	 */
	public void setRefundOperator(String refundOperator) {
		this.refundOperator = refundOperator;
	}

	/**
	 * @return the refundTime
	 */
	public Date getRefundTime() {
		return refundTime;
	}

	/**
	 * @param refundTime
	 *            the refundTime to set
	 */
	public void setRefundTime(Date refundTime) {
		this.refundTime = refundTime;
	}

	/**
	 * @return the outTradeNo
	 */
	public String getOutTradeNo() {
		return outTradeNo;
	}

	/**
	 * @param outTradeNo
	 *            the outTradeNo to set
	 */
	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}

	/**
	 * @return the userId
	 */
	public String getUserId() {
		return userId;
	}

	/**
	 * @param userId
	 *            the userId to set
	 */
	public void setUserId(String userId) {
		this.userId = userId;
	}

	/**
	 * @return the txnTime
	 */
	public String getTxnTime() {
		return txnTime;
	}

	/**
	 * @param txnTime
	 *            the txnTime to set
	 */
	public void setTxnTime(String txnTime) {
		this.txnTime = txnTime;
	}

	/**
	 * @return the notifyUrl
	 */
	public String getNotifyUrl() {
		return notifyUrl;
	}

	/**
	 * @param notifyUrl
	 *            the notifyUrl to set
	 */
	public void setNotifyUrl(String notifyUrl) {
		this.notifyUrl = notifyUrl;
	}

	/**
	 * @return the statusDes
	 */
	public String getStatusDes() {
		return statusDes;
	}

	/**
	 * @param statusDes
	 *            the statusDes to set
	 */
	public void setStatusDes(String statusDes) {
		this.statusDes = statusDes;
	}

	/**
	 * @return the tradeNo
	 */
	public String getTradeNo() {
		return tradeNo;
	}

	/**
	 * @param tradeNo
	 *            the tradeNo to set
	 */
	public void setTradeNo(String tradeNo) {
		this.tradeNo = tradeNo;
	}

	public Long getAmount() {
		return this.amount;
	}

	public void setAmount(Long amount) {
		this.amount = amount;
	}

	public String getAttach() {
		return this.attach;
	}

	public void setAttach(String attach) {
		this.attach = attach;
	}

	public String getBody() {
		return this.body;
	}

	public void setBody(String body) {
		this.body = body;
	}

	public String getChannel() {
		return this.channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public String getChannelCode() {
		return this.channelCode;
	}

	public void setChannelCode(String channelCode) {
		this.channelCode = channelCode;
	}

	public String getChannelTxnId() {
		return this.channelTxnId;
	}

	public void setChannelTxnId(String channelTxnId) {
		this.channelTxnId = channelTxnId;
	}

	public Date getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public int getDealerId() {
		return this.dealerId;
	}

	public void setDealerId(int dealerId) {
		this.dealerId = dealerId;
	}

	public String getOpenId() {
		return this.openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public Date getPayTime() {
		return this.payTime;
	}

	public void setPayTime(Date payTime) {
		this.payTime = payTime;
	}

	public String getPrepayId() {
		return this.prepayId;
	}

	public void setPrepayId(String prepayId) {
		this.prepayId = prepayId;
	}

	public int getStatus() {
		return this.status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public String getSubject() {
		return this.subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	/**
	 * @return the version
	 */
	public Long getVersion() {
		return version;
	}

	/**
	 * @param version
	 *            the version to set
	 */
	public void setVersion(Long version) {
		this.version = version;
	}

	
	/**
	 * @return the refundedAmount
	 */
	public Long getRefundedAmount() {
		return refundedAmount;
	}


	/**
	 * @param refundedAmount the refundedAmount to set
	 */
	public void setRefundedAmount(Long refundedAmount) {
		this.refundedAmount = refundedAmount;
	}


	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		return "CollectionDealerTradeEntity [tradeNo=" + tradeNo + ", outTradeNo=" + outTradeNo + ", userId=" + userId
				+ ", amount=" + amount + ", attach=" + attach + ", body=" + body + ", channel=" + channel
				+ ", channelCode=" + channelCode + ", channelTxnId=" + channelTxnId + ", createTime=" + createTime
				+ ", dealerId=" + dealerId + ", openId=" + openId + ", payTime=" + payTime + ", prepayId=" + prepayId
				+ ", statusDes=" + statusDes + ", txnTime=" + txnTime + ", notifyUrl=" + notifyUrl + ", refundTime="
				+ refundTime + ", refundOperator=" + refundOperator + ", serviceFee=" + serviceFee + ", refundedAmount="
				+ refundedAmount + ", version=" + version + ", status=" + status + ", subject=" + subject + "]";
	}

}