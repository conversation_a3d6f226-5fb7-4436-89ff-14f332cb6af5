package com.goodsogood.collectionpay.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name="gs_asset_deposit_channel_conf")
public class AssetDepositChannelConfEntity {
	
	@Id
	@Column(name="id")
	private String id;
	
	@Column(name="option_key")
	private String optionKey;
	
	@Column(name="option_value")
	private String optionValue;
	
	@Column(name="description")
	private String description;
	
	@Column(name="channel")
	private String channel;

	/**
	 * @return the id
	 */
	public String getId() {
		return id;
	}

	/**
	 * @param id the id to set
	 */
	public void setId(String id) {
		this.id = id;
	}

	/**
	 * @return the optionKey
	 */
	public String getOptionKey() {
		return optionKey;
	}

	/**
	 * @param optionKey the optionKey to set
	 */
	public void setOptionKey(String optionKey) {
		this.optionKey = optionKey;
	}

	/**
	 * @return the optionValue
	 */
	public String getOptionValue() {
		return optionValue;
	}

	/**
	 * @param optionValue the optionValue to set
	 */
	public void setOptionValue(String optionValue) {
		this.optionValue = optionValue;
	}

	/**
	 * @return the description
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * @param description the description to set
	 */
	public void setDescription(String description) {
		this.description = description;
	}

	/**
	 * @return the channel
	 */
	public String getChannel() {
		return channel;
	}

	/**
	 * @param channel the channel to set
	 */
	public void setChannel(String channel) {
		this.channel = channel;
	}

	
	
	
	

}
