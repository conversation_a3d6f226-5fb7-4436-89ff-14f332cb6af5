package com.goodsogood.collectionpay;

import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import com.goodsogood.collectionpay.dao.support.ExtJpaRepositoryFactoryBean;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.web.HttpMessageConverters;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.boot.web.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.PropertySource;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.servlet.DispatcherServlet;

import java.util.concurrent.Executor;

/**
 * 启动类 放置在根包下面(com.goodsogood.union.user) 好处①：
 * 
 * @ComponentScan而不使用任何参数
 * 所有应用程序组件（@Component，@Service，@Repository，@Controller等）
 * 将自动注册为SpringBean。
 */  
@SpringBootApplication
//@EnableAutoConfiguration
@Configuration
//@ComponentScan(value = {"com.goodsogood.collectionpay"})
@EnableAspectJAutoProxy(proxyTargetClass = true)
@PropertySource(ignoreResourceNotFound = true, value = {"${out.properties}",  "${out.db.properties}"})
@EnableJpaRepositories(repositoryFactoryBeanClass = ExtJpaRepositoryFactoryBean.class)
public class Application extends SpringBootServletInitializer implements CommandLineRunner {

	/**
	 * fastjson
	 */
	@Bean
	public HttpMessageConverters fastJsonHttpMessageConverters() {
		FastJsonHttpMessageConverter fastConverter = new FastJsonHttpMessageConverter();
		FastJsonConfig fastJsonConfig = new FastJsonConfig();
		fastJsonConfig.setSerializerFeatures(SerializerFeature.PrettyFormat);
		fastConverter.setFastJsonConfig(fastJsonConfig);
		HttpMessageConverter<?> converter = fastConverter;
		return new HttpMessageConverters(converter);
	}

	@Bean
	public ServletRegistrationBean dispatcherRegistration(DispatcherServlet dispatcherServlet) {
		ServletRegistrationBean reg = new ServletRegistrationBean(dispatcherServlet);
		reg.getUrlMappings().clear();
		reg.addUrlMappings("*.html");
		reg.addUrlMappings("*.do");
		return reg;
	}

    /**
     * 线程池
     *
     * @param i  core pool
     * @param i2 max pool
     * @param i3 queue
     * @param s  name
     * @return new ThreadPoolTaskExecutor
     */
    private Executor getExecutor(int i, int i2, int i3, String s) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(i);
        executor.setMaxPoolSize(i2);
        executor.setQueueCapacity(i3);
        executor.setThreadNamePrefix(s);
        executor.initialize();
        return executor;
    }

    @Bean("fileWriteExecutor")
    public Executor workReportExecutor() {
        return getExecutor(60, 120, 10000, "WORK_REPORT_");
    }

	public static void main(String[] args) {
		SpringApplication.run(Application.class, args);
	}

	@Override
	public void run(String... args) throws Exception {
		
	}
	

}
