package com.goodsogood.collectionpay.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.goodsogood.collectionpay.conf.RedisConfiger;
import com.goodsogood.collectionpay.consts.LogConfiger;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

/**
 * redis池
 */
@Component
public class RedisPool {

	static final Logger logger = LoggerFactory.getLogger(LogConfiger.APP);

	private static JedisPool pool;

	@Autowired
	private RedisConfiger redisConfiger;

	private void init() {
		JedisPoolConfig config = new JedisPoolConfig();
		// 最大空闲连接数, 应用自己评估，不要超过ApsaraDB for Redis每个实例最大的连接数
		config.setMaxIdle(redisConfiger.getMaxIdel());
		// 最大连接数, 应用自己评估，不要超过ApsaraDB for Redis每个实例最大的连接数
		config.setMaxTotal(redisConfiger.getMaxTotal());
		config.setTestOnBorrow(false);
		config.setTestOnReturn(false);

		logger.info("redisPool' host is {}", redisConfiger.getAddress());
		pool = new JedisPool(config, redisConfiger.getAddress(), redisConfiger.getPort(),
                redisConfiger.getTimeout(), redisConfiger.getPassword());
	}

	public Jedis getResource() {
		if (pool == null) {
			init();
		}
		return pool.getResource();
	}

	/**
	 * 释放jedis资源
	 * 
	 * @param jedis
	 */
	public void returnResource(final Jedis jedis) {
		if (jedis != null) {
			try {
				jedis.close();
			} catch (Exception e) {
				logger.error("close redis conn fail : ", e);
			}
		}
	}
}
