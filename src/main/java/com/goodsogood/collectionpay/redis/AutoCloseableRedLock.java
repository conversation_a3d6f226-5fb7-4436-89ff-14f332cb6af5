package com.goodsogood.collectionpay.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.goodsogood.redlock.RedLock;

import redis.clients.jedis.JedisPool;

public class AutoCloseableRedLock extends RedLock implements AutoCloseable {

	private static final Logger logger = LoggerFactory.getLogger(AutoCloseableRedLock.class);

	public AutoCloseableRedLock(JedisPool[] pools, String lockKey, int expiryTimeMillis) throws Exception {
		super(pools, lockKey, expiryTimeMillis);
	}

	@Override
	public void close() {
		try {
			super.unLock();
			super.close();
		} catch (Exception e) {
			logger.error("AutoCloseableRedLock close error!", e);
		}

	}

}
