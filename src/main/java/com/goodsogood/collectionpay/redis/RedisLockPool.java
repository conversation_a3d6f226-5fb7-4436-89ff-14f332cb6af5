package com.goodsogood.collectionpay.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.goodsogood.collectionpay.conf.RedisLockConfiger;
import com.goodsogood.collectionpay.consts.LogConfiger;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

/**
 * redis池
 */
///@Component
public class RedisLockPool {

	static final Logger logger = LoggerFactory.getLogger(LogConfiger.APP);

	private  JedisPool[] pools;

	private int defaultExpiryTime = 30 * 1000; //锁过期时间

	public int getDefaultExpiryTime() {
		return defaultExpiryTime;
	}

	/**
	 * @return the pools
	 */
	public synchronized  JedisPool[] getPools() {
		if (pools == null) {
			init();
		}
		return pools;
	}

	@Autowired
	private RedisLockConfiger redisLockConfiger;

	private void init() {
		String[] servers = redisLockConfiger.getAddress();
		pools = new JedisPool[servers.length];
		for (int i = 0; i < pools.length; i++) {
			String address = servers[i];
			JedisPoolConfig config = new JedisPoolConfig();
			// 最大空闲连接数, 应用自己评估，不要超过ApsaraDB for Redis每个实例最大的连接数
			config.setMaxIdle(redisLockConfiger.getMaxIdel());
			// 最大连接数, 应用自己评估，不要超过ApsaraDB for Redis每个实例最大的连接数
			config.setMaxTotal(redisLockConfiger.getMaxTotal());
			config.setTestOnBorrow(false);
			config.setTestOnReturn(false);
			config.setMaxWaitMillis(1000);
			//连接耗尽时是否阻塞, false报异常,ture阻塞直到超时, 默认true
			config.setBlockWhenExhausted(false);
			logger.info("RedisLockPool [{}] host is {}", i, address);
			/*JedisPool pool = new JedisPool(config, address, redisLockConfiger.getPort(), redisLockConfiger.getTimeout(),
					redisLockConfiger.getPassword());*/
			JedisPool pool = null ; 
			if(StringUtils.isEmpty(redisLockConfiger.getPassword())){
				pool =  new JedisPool(config, address, redisLockConfiger.getPort(), redisLockConfiger.getTimeout());
			}else{
				pool = new JedisPool(config, address, redisLockConfiger.getPort(), redisLockConfiger.getTimeout(),
						redisLockConfiger.getPassword());
			}
			pools[i] = pool;
		}

	}

	public synchronized Jedis[] getResource() {
			if (pools == null) {
				init();
			}
			Jedis[] res = new Jedis[pools.length];
			for (int i = 0; i < pools.length; i++) {
				res[i] = pools[i].getResource();
			}
			return res;
	}

	/**
	 * 释放jedis资源
	 * 
	 * @param jedis
	 */
	public void returnResource(final Jedis[] jedis) {
		for (int i = 0; i < jedis.length; i++) {
			Jedis each = jedis[i];
			if (each != null) {
				try {
					each.close();
				} catch (Exception e) {
					logger.error("RedisLockPool close redis conn fail : ", e);
				}
			}
		}

	}
}
