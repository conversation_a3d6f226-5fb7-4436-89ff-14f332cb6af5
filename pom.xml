<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.goodsogood</groupId>
	<artifactId>ows-pay</artifactId>
	<version>1.0</version>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>1.5.22.RELEASE</version>
	</parent>
    <properties>
        <project.basedir>D:\ows\collection-pay</project.basedir>
        <log4j2.version>2.19.0</log4j2.version>
    </properties>
	<!-- Add typical dependencies for a web application -->
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- -->

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-log4j2</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.16.20</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>

		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>

		<dependency>
			<groupId>com.zaxxer</groupId>
			<artifactId>HikariCP</artifactId>
		</dependency>


		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
            <version>1.2.71</version>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.4</version>
		</dependency>

		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
		</dependency>

		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
		</dependency>

		<!-- slf4j核心包 -->
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
		</dependency>

		<!--用于与slf4j保持桥接 -->
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-slf4j-impl</artifactId>
		</dependency>

		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.5</version>
		</dependency>
		<dependency>
			<groupId>com.goodsogood</groupId>
			<artifactId>base-system-util</artifactId>
			<version>1.0.8-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>org.apache.solr</groupId>
			<artifactId>solr-solrj</artifactId>
			<version>6.5.1</version>
		</dependency>

		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>core</artifactId>
			<version>3.3.0</version>
		</dependency>

		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>javase</artifactId>
			<version>3.3.0</version>
		</dependency>

		<dependency>
			<groupId>net.glxn</groupId>
			<artifactId>qrgen</artifactId>
			<version>1.4</version>
		</dependency>

		<dependency>
			<groupId>com.thoughtworks.xstream</groupId>
			<artifactId>xstream</artifactId>
			<version>1.4.19</version>
		</dependency>

		<dependency>
			<groupId>com.github.kevinsawicki</groupId>
			<artifactId>http-request</artifactId>
			<version>6.0</version>
		</dependency>

		<dependency>
			<groupId>com.goodsogood</groupId>
			<artifactId>base-system-alipay-sdk</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>javax.validation</groupId>
			<artifactId>validation-api</artifactId>
			<version>2.0.0.Final</version>
		</dependency>

		<dependency>
			<groupId>org.hibernate.validator</groupId>
			<artifactId>hibernate-validator</artifactId>
			<version>6.0.2.Final</version>
		</dependency>
		<dependency>
			<groupId>org.hibernate.validator</groupId>
			<artifactId>hibernate-validator-annotation-processor</artifactId>
			<version>6.0.2.Final</version>
		</dependency>

		<dependency>
			<groupId>javax.el</groupId>
			<artifactId>javax.el-api</artifactId>
			<version>3.0.0</version>
		</dependency>

		<dependency>
			<groupId>org.glassfish.web</groupId>
			<artifactId>javax.el</artifactId>
			<version>2.2.6</version>
		</dependency>

		<dependency>
			<groupId>icbc-api-sdk-cop</groupId>
			<artifactId>icbc-api-sdk-cop</artifactId>
			<version>1.0</version>
			  <scope>system</scope>
            <systemPath>${project.basedir}/lib/icbc-api-sdk-cop.jar</systemPath>
		</dependency>

		<dependency>
			<groupId>icbc-api-sdk-cop-io</groupId>
			<artifactId>icbc-api-sdk-cop-io</artifactId>
			<version>1.0</version>
			  <scope>system</scope>
            <systemPath>${project.basedir}/lib/icbc-api-sdk-cop-io.jar</systemPath>
		</dependency>

<!--		<dependency>-->
<!--			<groupId>com.goodsogood</groupId>-->
<!--			<artifactId>com.goodsogood</artifactId>-->
<!--			<version>1.0.0-SNAPSHOT</version>-->
<!--			<scope>system</scope>-->
<!--			<systemPath>${project.basedir}/lib/bcprov-jdk15on-1.66.jar</systemPath>-->
<!--		</dependency>-->

        <dependency>
            <groupId>com.hitrust.trustpay</groupId>
            <artifactId>TrustPayCBPClient</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/TrustPayCBPClient.jar</systemPath>
        </dependency>
		
		<dependency>
			<groupId>com.abc.pay.client</groupId>
			<artifactId>TrustPayClien</artifactId>
			<version>3.1.4</version>
			<scope>system</scope>
            <systemPath>${project.basedir}/lib/TrustPayClient-V3.1.4.jar</systemPath>
		</dependency>

<!--        <dependency>-->
<!--            <groupId>com.goodsogood</groupId>-->
<!--            <artifactId>bcprov-jdk</artifactId>-->
<!--            <version>1.0</version>-->
<!--            <scope>system</scope>-->
<!--            <systemPath>${project.basedir}/lib/bcprov-jdk14-128.jar</systemPath>-->
<!--        </dependency>-->

<!--		<dependency>-->
<!--			<groupId>com.goodsogood</groupId>-->
<!--			<artifactId>com.goodsogood</artifactId>-->
<!--			<version>1.0.0-SNAPSHOT</version>-->
<!--			<scope>system</scope>-->
<!--			<systemPath>${project.basedir}/lib/bcprov-jdk15on-1.66.jar</systemPath>-->
<!--		</dependency>-->

        <dependency>
            <groupId>com.goodsogood</groupId>
            <artifactId>hsm-software-share</artifactId>
            <version>1.0.3</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/hsm-software-share-1.0.3.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.goodsogood</groupId>
            <artifactId>netpay</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/netpay.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.goodsogood</groupId>
            <artifactId>pkcs7</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/pkcs7.jar</systemPath>
        </dependency>

		<dependency>
			<groupId>commons-httpclient</groupId>
			<artifactId>commons-httpclient</artifactId>
			<version>3.1</version>
		</dependency>

		<dependency>
			<groupId>com.aliyun.oss</groupId>
			<artifactId>aliyun-sdk-oss</artifactId>
			<version>2.2.1</version>
		</dependency>

		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>4.1.2</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>4.1.2</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml-schemas</artifactId>
			<version>4.1.2</version>
		</dependency>
		<dependency>
            <groupId>xerces</groupId>
            <artifactId>xercesImpl</artifactId>
            <version>2.11.0</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.7.19</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/hutool-all-5.7.19.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>ccom.goodsogood</groupId>
            <artifactId>ccbpay</artifactId>
            <version>1.0.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/ccbpay-api-java-1.0.0.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>org.bgee.log4jdbc-log4j2</groupId>
            <artifactId>log4jdbc-log4j2-jdbc4</artifactId>
            <version>1.16</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>


		<dependency>
			<groupId>com.github.wechatpay-apiv3</groupId>
			<artifactId>wechatpay-apache-httpclient</artifactId>
			<version>0.4.9</version>
		</dependency>

		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
			<version>5.0.0-alpha.11</version>
		</dependency>

		<dependency>
			<groupId>com.github.wechatpay-apiv3</groupId>
			<artifactId>wechatpay-java</artifactId>
			<version>0.2.12</version>
		</dependency>

		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-jdk15on</artifactId>
			<version>1.70</version>
		</dependency>

		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcpkix-jdk15on</artifactId>
			<version>1.70</version>
		</dependency>
		<dependency>
			<groupId>com.dameng</groupId>
			<artifactId>DmJdbcDriver18</artifactId>
			<version>8.1.3.140</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<!-- Package as an executable jar 打包成执行jar的插件 -->
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
                    <!-- 导入本地引用的jar包 -->
                    <includeSystemScope>true</includeSystemScope>
					<mainClass>com.goodsogood.collectionpay.Application</mainClass>
				</configuration>
			</plugin>
			
			<plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                    <compilerArguments>
                        <extdirs>${project.basedir}/src/main/resources/lib</extdirs>
                    </compilerArguments>
                </configuration>
            </plugin>
            
		</plugins>
		<resources>
			<resource>
				<directory>src/main/java</directory>
				<includes>
					<include>**/*.xml</include>
				</includes>
			</resource>
			<resource>
				<directory>src/main/resources</directory>
				<includes>
					<include>**/*.*</include>
					<include>**/*.csv</include>
				</includes>
			</resource>
			<resource>
                    <directory>${project.basedir}/src/main/resources/lib</directory>
                    <targetPath>WEB-INF/lib/</targetPath>
                    <includes>
                        <include>**/*.jar</include>
                    </includes>
            </resource>
		</resources>
	</build>
</project>
